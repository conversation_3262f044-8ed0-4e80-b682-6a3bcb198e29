﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Localization.Practices.Categories;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class TopicCategoriesLocalizationsController : AbsController
    {
        public TopicCategoriesLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/TopicCategories")]
        public IActionResult TopicCategories()
        {
            var cats = DB.TopicCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            cats.Reverse();

            var vm = new LocalizationListVM<TopicCategory>
            {
                Items = cats,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Practices\Categories\TopicCategories\TopicCategories.cshtml", vm);
        }







        [Route("Localizations/CreateTopicCategoryLocalization")]
        public IActionResult CreateTopicCategoryLocalization(int id)
        {
            var vm = new LocalizationVM<TopicCategoryLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Practices\Categories\TopicCategories\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreateTopicCategoryLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateTopicCategoryLocalizationPOST([FromForm] LocalizationVM<TopicCategoryLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();

            var cat = DB.TopicCategories.FirstOrDefault(o => o.Id == vm.Id);
            cat.Localizations.Add(vm.Localization);

            DB.TopicCategories.Update(cat);
            await DB.SaveChangesAsync();
            return RedirectToAction("TopicCategories", "TopicCategoriesLocalizations");
        }





        [Route("Localizations/UpdateTopicCategoryLocalization")]
        public IActionResult UpdateTopicCategoryLocalization(int id)
        {
            var cat = DB.TopicCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<TopicCategoryLocalization>
            {
                Id = id,
                Localization = cat.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Practices\Categories\TopicCategories\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdateTopicCategoryLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateTopicCategoryLocalizationPOST([FromForm] LocalizationVM<TopicCategoryLocalization> vm)
        {
            DB.TopicCategoryLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("TopicCategories", "TopicCategoriesLocalizations");
        }

    }
}
