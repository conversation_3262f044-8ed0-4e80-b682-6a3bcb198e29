﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Tests;
using Triggero.Models.Tests;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;
using Triggero.Web.ViewModels.Localization.Tests;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class TestResultsLocalizationController : AbsController
    {
        public TestResultsLocalizationController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }


        [Route("Localizations/Tests/CreateResultLocalization")]
        public IActionResult CreateResultLocalization(int id)
        {
            var vm = new LocalizationVM<TestResultLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Tests\Results\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/Tests/CreateResultLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateResultLocalizationPOST([FromForm] LocalizationVM<TestResultLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();
            var post = DB.TestResults.FirstOrDefault(o => o.Id == vm.Id);
            post.Localizations.Add(vm.Localization);

            DB.TestResults.Update(post);
            await DB.SaveChangesAsync();
            return RedirectToAction("Tests", "TestStartPageLocalization");
        }





        [Route("Localizations/Tests/UpdateResultLocalization")]
        public IActionResult UpdateResultLocalization(int id)
        {
            var post = DB.TestResults
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<TestResultLocalization>
            {
                Id = id,
                Localization = post.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Tests\Results\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/Tests/UpdateResultLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateResultLocalizationPOST([FromForm] LocalizationVM<TestResultLocalization> vm)
        {
            DB.TestResultLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("Tests", "TestStartPageLocalization");
        }

    }
}
