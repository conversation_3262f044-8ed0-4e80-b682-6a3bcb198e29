﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Localization
@using Triggero.Models.Localization.MoodTracker
@using Triggero.Models.Localization.Practices
@using Triggero.Models.Localization.Practices.Categories
@using Triggero.Web.ViewModels.Localization
@{
    ViewData["Title"] = "СОЗДАНИЕ ПЕРЕВОДА";
    ViewData["ShowLang"] = true;
}
@model LocalizationVM<FactorDetailLocalization>

 <!-- CONTENT -->
<form asp-action="CreateFactorDetailLocalizationPOST" asp-controller="FactorDetailLocalizations" method="post" enctype="multipart/form-data" class="content">

    <input type="hidden" asp-for="Id" value="@Model.Id" />

    <div class="row">
        <div class="page">
            
            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Localization.Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>