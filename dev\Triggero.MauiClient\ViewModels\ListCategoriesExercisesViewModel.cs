﻿using Triggero.Models.Practices.Categories;

namespace Triggero.MauiClient.ViewModels;

public class ListCategoriesExercisesViewModel : BaseCategoriesViewModel
{
    public override Color ThemeColor
    {
        get
        {
            return Color.FromHex("#FFF8F2");
        }
    }

    protected override void OnViewTapped(SkiaControl control)
    {
        App.OpenView(new ListElementsView(control.BindingContext as AbstractCategory));
    }

    public override async Task InitializeAsyc()
    {
        var items = await ApplicationState.Data.GetExerciseCategories();

        items.Insert(0, new ExerciseCategory
        {
            Title = App.This.Interface.Library.Library.AllExercises,
            Description = App.This.Interface.Library.Library.AllExercisesDescription,
            ImgPath = "/built_in/images/allExercises.png"
        });

        MainThread.BeginInvokeOnMainThread(() =>
        {
            ItemTemplate = new DataTemplate(() =>
            {
                return new CellCategoryDrawn();//ExerciseCategoryCard();
            });

            Items.AddRange(items);

            //preload all images..
            //var cancel = new CancellationTokenSource(TimeSpan.FromSeconds(10));
            //CancelPreload = cancel;
            //SkiaImageManager.Instance.PreloadImages(items.Select(s => s.ImgPath.AddBaseUrl(Triggero.Mobile.Constants.UrlContent)), cancel);

            OnPropertyChanged(nameof(IsEmpty));

        });

        IsInitialized = true;
    }
}