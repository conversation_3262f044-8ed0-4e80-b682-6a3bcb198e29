﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <SourceRevisionId>build$([System.DateTime]::UtcNow.ToString("yyyy-MM-ddTHH:mm:ss:fffZ"))</SourceRevisionId>
    <PublishProfileExcludeFolders>wwwroot\uploads</PublishProfileExcludeFolders>
    <Nullable>disable</Nullable>
	<LangVersion>latest</LangVersion>
    <ImplicitUsings>enable</ImplicitUsings>
    <RootNamespace>Triggero.Api</RootNamespace>
  </PropertyGroup>

  <PropertyGroup> <GenerateDocumentationFile>true</GenerateDocumentationFile> 
    <NoWarn>$(NoWarn);1591</NoWarn> 
  </PropertyGroup>

  <PropertyGroup Condition="'$(Configuration)'=='Release'">
    <ExcludeFoldersFromDeployment>wwwroot\uploads</ExcludeFoldersFromDeployment> 
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Services\**" />
    <Content Remove="Services\**" />
    <EmbeddedResource Remove="Services\**" />
    <None Remove="Services\**" />
  </ItemGroup>

 

  <ItemGroup>
    <Compile Include="..\..\..\Triggero.Web.Secrets.cs" Link="Triggero.Web.Secrets.cs" />
  </ItemGroup>

 

  <ItemGroup>
    <Content Include="..\..\..\..\Triggero\Secrets\secrets.json" Link="secrets.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
 
  <ItemGroup>
    <PackageReference Include="AppoMobi.Specials" Version="8.0.3" />
 
    <PackageReference Include="Magick.NET-Q16-AnyCPU" Version="13.8.0" />
    <PackageReference Include="Magick.NET.Core" Version="13.8.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.5" />
    <PackageReference Include="Microsoft.EntityFrameworkCore.Design" Version="8.0.5">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Newtonsoft.Json" Version="13.0.3" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.6.2" />

    <PackageReference Include="HtmlAgilityPack" Version="1.11.61" />
    <PackageReference Include="JsonKnownTypes" Version="0.6.0" />

  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\MobileAPIWrapper\MobileAPIWrapper.csproj" />

    <ProjectReference Include="..\Triggero.Application\Triggero.Application.csproj" />
    <ProjectReference Include="..\Triggero.PushNotifications\Triggero.PushNotifications.csproj" />
 
  </ItemGroup>

  <ItemGroup>
    <Folder Include="Pages\Components\" />
    <Folder Include="wwwroot\uploads\" />
  </ItemGroup>
 
</Project>
