﻿<?xml version="1.0" encoding="UTF-8" ?>
<draw:SkiaLayout
    x:Class="Triggero.MauiClient.Views.DrawnListCategories"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
    HorizontalOptions="Fill"
    VerticalOptions="Fill">


    <draw:SkiaScroll
        FrictionScrolled="0.35"
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <draw:SkiaLayout
            CommandChildTapped="{Binding CommandChildTapped}"
            HorizontalOptions="Fill"
            ItemTemplate="{Binding ItemTemplate}"
            ItemsSource="{Binding Items}"
            RecyclingTemplate="Disabled"
            Spacing="12"
            Tag="StackCategoriesCells"
            Type="Column"
            UseCache="Image" />

    </draw:SkiaScroll>

</draw:SkiaLayout>