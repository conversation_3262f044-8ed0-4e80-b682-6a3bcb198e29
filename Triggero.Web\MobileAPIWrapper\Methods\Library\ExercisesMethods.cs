﻿using Newtonsoft.Json;
using Odintcovo.API.Helpers;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;
using Triggero.Models.Tests;

namespace MobileAPIWrapper.Methods.Library
{
    public class ExercisesMethods
    {
        public static string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("Exercises/");


        public async Task<List<ExerciseCategory>> GetExerciseCategories()
        {
            string url = BASE_HOST + "GetExerciseCategories";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<ExerciseCategory>>(response.Content);
            return obj;
        }





        public async Task<List<Exercise>> GetExercises()
        {
            string url = BASE_HOST + "GetExercises";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Exercise>>(response.Content);
            return obj;
        }

        public async Task<List<Exercise>> GetExercisesChunk(int count, int offset)
        {
            string url = BASE_HOST + $"GetExercisesChunk?count={count}&offset={offset}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Exercise>>(response.Content);
            return obj;
        }





        public async Task<List<Exercise>> GetExercisesByCategory(int categoryId)
        {
            string url = BASE_HOST + $"GetExercisesByCategory?categoryId={categoryId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Exercise>>(response.Content);
            return obj;
        }
        public async Task<List<Exercise>> GetExercisesByCategoryChunk(int categoryId, int count, int offset)
        {
            string url = BASE_HOST + $"GetExercisesByCategoryChunk?categoryId={categoryId}&count={count}&offset={offset}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Exercise>>(response.Content);
            return obj;
        }







        public async Task<List<Exercise>> GetExercisesByTag(string tag)
        {
            string url = BASE_HOST + $"GetExercisesByTag?tag={tag}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Exercise>>(response.Content);
            return obj;
        }



        public async Task<Exercise> GetExercise(int id)
        {
            string url = BASE_HOST + $"GetExercise?id={id}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<Exercise>(response.Content);
            return obj;
        }



        public async Task AddWatch(int exerciseId)
        {
            string url = BASE_HOST + $"AddWatch?exerciseId={exerciseId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);
        }
    }
}
