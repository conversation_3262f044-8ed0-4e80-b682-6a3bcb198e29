﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.MoodTracker;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Localization.Practices.Categories;
using Triggero.Models.MoodTracker;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class FactorLocalizationsController : AbsController
    {
        public FactorLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/Factors")]
        public IActionResult Factors()
        {
            var cats = DB.Factors
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            cats.Reverse();

            var vm = new LocalizationListVM<Factor>
            {
                Items = cats,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\MoodTracker\Factors\Factors.cshtml", vm);
        }







        [Route("Localizations/CreateFactorLocalization")]
        public IActionResult CreateFactorLocalization(int id)
        {
            var vm = new LocalizationVM<FactorLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\MoodTracker\Factors\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreateFactorLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateFactorLocalizationPOST([FromForm] LocalizationVM<FactorLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();

            var cat = DB.Factors.FirstOrDefault(o => o.Id == vm.Id);
            cat.Localizations.Add(vm.Localization);

            DB.Factors.Update(cat);
            await DB.SaveChangesAsync();
            return RedirectToAction("Factors", "FactorLocalizations");
        }





        [Route("Localizations/UpdateFactorLocalization")]
        public IActionResult UpdateFactorLocalization(int id)
        {
            var cat = DB.Factors
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<FactorLocalization>
            {
                Id = id,
                Localization = cat.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\MoodTracker\Factors\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdateFactorLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateFactorLocalizationPOST([FromForm] LocalizationVM<FactorLocalization> vm)
        {
            DB.FactorLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("Factors", "FactorLocalizations");
        }

    }
}
