!function(e,i){"use strict";var t={enableLineHighlight:!0,languageNames:{html:"HTML",xml:"XML",svg:"SVG",mathml:"MathML",ssml:"SSML",css:"CSS",clike:"C-like",js:"JavaScript",abap:"ABAP",abnf:"Augmented Backus–Naur form",al:"AL",antlr4:"ANTLR4",g4:"ANTLR4",apacheconf:"Apache Configuration",apl:"APL",aql:"AQL",arff:"ARFF",asciidoc:"AsciiDoc",adoc:"AsciiDoc",asm6502:"6502 Assembly",aspnet:"ASP.NET (C#)",autohotkey:"AutoHotkey",autoit:"AutoIt",basic:"BASIC",bbcode:"BBcode",bnf:"Backus–Naur form",rbnf:"Routing Backus–Naur form",conc:"Concurnas",csharp:"C#",cs:"C#",dotnet:"C#",cpp:"C++",cil:"CIL",coffee:"CoffeeScript",cmake:"CMake",csp:"Content-Security-Policy","css-extras":"CSS Extras",dax:"DAX",django:"Django/Jinja2",jinja2:"Django/Jinja2","dns-zone-file":"DNS zone file","dns-zone":"DNS zone file",dockerfile:"Docker",ebnf:"Extended Backus–Naur form",ejs:"EJS",etlua:"Embedded Lua templating",erb:"ERB","excel-formula":"Excel Formula",xlsx:"Excel Formula",xls:"Excel Formula",fsharp:"F#","firestore-security-rules":"Firestore security rules",ftl:"FreeMarker Template Language",gcode:"G-code",gdscript:"GDScript",gedcom:"GEDCOM",glsl:"GLSL",gml:"GameMaker Language",gamemakerlanguage:"GameMaker Language",graphql:"GraphQL",hs:"Haskell",hcl:"HCL",hlsl:"HLSL",http:"HTTP",hpkp:"HTTP Public-Key-Pins",hsts:"HTTP Strict-Transport-Security",ichigojam:"IchigoJam",iecst:"Structured Text (IEC 61131-3)",inform7:"Inform 7",javadoc:"JavaDoc",javadoclike:"JavaDoc-like",javastacktrace:"Java stack trace",jq:"JQ",jsdoc:"JSDoc","js-extras":"JS Extras","js-templates":"JS Templates",json:"JSON",jsonp:"JSONP",json5:"JSON5",latex:"LaTeX",tex:"TeX",context:"ConTeXt",lilypond:"LilyPond",ly:"LilyPond",emacs:"Lisp",elisp:"Lisp","emacs-lisp":"Lisp",llvm:"LLVM IR",lolcode:"LOLCODE",md:"Markdown","markup-templating":"Markup templating",matlab:"MATLAB",mel:"MEL",moon:"MoonScript",n1ql:"N1QL",n4js:"N4JS",n4jsd:"N4JS","nand2tetris-hdl":"Nand To Tetris HDL",nasm:"NASM",neon:"NEON",nginx:"nginx",nsis:"NSIS",objectivec:"Objective-C",objc:"Objective-C",ocaml:"OCaml",opencl:"OpenCL",parigp:"PARI/GP",objectpascal:"Object Pascal",pcaxis:"PC-Axis",px:"PC-Axis",peoplecode:"PeopleCode",pcode:"PeopleCode",php:"PHP",phpdoc:"PHPDoc","php-extras":"PHP Extras",plsql:"PL/SQL",powerquery:"PowerQuery",pq:"PowerQuery",mscript:"PowerQuery",powershell:"PowerShell",properties:".properties",protobuf:"Protocol Buffers",py:"Python",q:"Q (kdb+ database)",qml:"QML",rkt:"Racket",jsx:"React JSX",tsx:"React TSX",renpy:"Ren'py",rest:"reST (reStructuredText)",robotframework:"Robot Framework",robot:"Robot Framework",rb:"Ruby",sas:"SAS",sass:"Sass (Sass)",scss:"Sass (Scss)","shell-session":"Shell session",solidity:"Solidity (Ethereum)","solution-file":"Solution file",sln:"Solution file",soy:"Soy (Closure Template)",sparql:"SPARQL",rq:"SPARQL","splunk-spl":"Splunk SPL",sqf:"SQF: Status Quo Function (Arma 3)",sql:"SQL",tap:"TAP",toml:"TOML",tt2:"Template Toolkit 2",trig:"TriG",ts:"TypeScript","t4-cs":"T4 Text Templates (C#)",t4:"T4 Text Templates (C#)","t4-vb":"T4 Text Templates (VB)","t4-templating":"T4 templating",uscript:"UnrealScript",uc:"UnrealScript",vbnet:"VB.Net",vhdl:"VHDL",vim:"vim","visual-basic":"Visual Basic",vb:"Visual Basic",wasm:"WebAssembly",wiki:"Wiki markup",xeoracube:"XeoraCube",xojo:"Xojo (REALbasic)",xquery:"XQuery",yaml:"YAML",yml:"YAML"}};function a(i){return e("<div/>").text(i).html()}function o(e){var t=e.o.plugins.highlight.languageNames,o=Object.keys(t);return Object.keys(i.languages).filter((function(e){return o.indexOf(e)>=0})).map((function(e){return{id:e,name:t[e]}})).sort((function(e,i){return e.name.localeCompare(i.name)})).map((function(e){return'<option value="'+a(e.id)+'">'+a(e.name)+"</option>"})).join("")}function l(e){return!1===e.o.plugins.highlight.enableLineHighlight?"":'<div class="'+e.o.prefix+'highlight-form-group">   <input placeholder="'+e.lang.highlightLine+'" class="'+e.o.prefix+'highlight-form-control trumbowyg-line-highlight"/></div>'}function s(e){return{fn:function(){var t=e.openModal("Code",['<div class="'+e.o.prefix+'highlight-form-group">','   <select class="'+e.o.prefix+'highlight-form-control language" autofocus>',o(e),"   </select>","</div>",'<div class="'+e.o.prefix+'highlight-form-group">','   <textarea class="'+e.o.prefix+'highlight-form-control code"></textarea>',"</div>",l(e)].join("\n")),a=t.find(".language"),s=t.find(".code"),n=t.find(".trumbowyg-line-highlight");t.on("tbwconfirm",(function(){var t,o,l;e.restoreRange(),e.execCmd("insertHTML",(t=s.val(),o=a.val(),l=n.val(),['<pre class="language-'+o+'" '+(l?'data-line="'+l+'"':"")+">",'<code class="language-'+o+'">'+i.highlight(t,i.languages[o])+"</code>","</pre>"].join(""))),e.execCmd("insertHTML","<p><br></p>"),e.closeModal()})),t.on("tbwcancel",(function(){e.closeModal()}))}}}e.extend(!0,e.trumbowyg,{langs:{en:{highlight:"Code syntax highlight",highlightLine:"Highlight lines, e.g.: 1,3-5"},sl:{highlight:"Označi sintakso kode",highlightLine:"Označi številko vrstice, npr.: 1,3-5"},by:{highlight:"Падсветка сінтаксісу кода",highlightLine:"Падсвятліць радкі, напр.: 1,3-5"},es:{highlight:"Resaltado de sintaxis de código",highlightLine:"Resaltar lineas, ej: 1,3-5"},et:{highlight:"Koodi esiletoomine",highlightLine:"Koodiread, näiteks: 1,3-5"},hu:{highlight:"Kód kiemelés"},ko:{highlight:"코드 문법 하이라이트"},pt_br:{highlight:"Realçar sintaxe de código"},ru:{highlight:"Подсветка синтаксиса кода",highlightLine:"Подсветить строки, напр.: 1,3-5"},tr:{highlight:"Kod sözdizimini vurgula",highlightLine:"Vurgu çizgileri, örneğin: 1,3-5"}},plugins:{highlight:{init:function(i){i.o.plugins.highlight=e.extend(!0,{},t,i.o.plugins.highlight||{}),i.addBtnDef("highlight",s(i))}}}})}(jQuery,Prism);