/* ===========================================================
 * th.js
 * Thai translation for Trumbowyg
 * https://github.com/ionsoft/Trumbowyg
 * ===========================================================
 * Author : Gonatee Klanktong
 *          Github : https://github.com/gonateek
 */

jQuery.trumbowyg.langs.th = {
  viewHTML: 'ดู HTML',

  formatting: 'จัดรูปแบบ',
  p: 'ย่อหน้า',
  blockquote: 'อ้างอิง',
  code: 'โค๊ด',
  header: 'ส่วนหัว',

  bold: 'หนา',
  italic: 'เอียง',
  strikethrough: 'ขีดทับ',
  underline: 'เส้นใต้',

  strong: 'สำคัญ',
  em: 'เน้น',
  del: 'ลบ',

  unorderedList: 'รายการ',
  orderedList: 'รายการ(ตัวเลข)',

  insertImage: 'ใส่รูป',
  insertVideo: 'ใส่วิดีโอ',
  link: 'ลิงค์',
  createLink: 'สร้างลิงค์',
  unlink: 'ยกเลิกลิงค์',

  justifyLeft: 'ชิดซ้าย',
  justifyCenter: 'กลาง',
  justifyRight: 'ชิดขวา',
  justifyFull: 'เต็มบรรทัด',

  horizontalRule: 'เส้นแนวนอน',

  fullscreen: 'เต็มหน้าจอ',

  close: 'ปิด',

  submit: 'ตกลง',
  reset: 'เริ่มใหม่',

  required: 'จำเป็น',
  description: 'คำอธิบาย',
  title: 'หัวเรื่อง',
  text: 'ข้อความ'
};
