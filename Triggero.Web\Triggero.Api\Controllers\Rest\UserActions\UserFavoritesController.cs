﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Domain.Enums;
using Triggero.Models;
using Triggero.Models.General;
using Triggero.Models.General.UserData;
using Triggero.Models.Practices;
using Triggero.Models.Tests;

namespace Triggero.Domain.Controllers.Rest.General.Users
{
    [ApiController]
    [Route("[controller]")]
    public class UserFavoritesController : ApiController
    {
        public UserFavoritesController(DatabaseContext db) : base(db)
        {
        }

        #region Получение избранных


        [HttpGet, Route("GetUserFavoritesAll")]
        public async Task<UserFavorites> GetUserFavoritesAll(int id)
        {
            var favorites = DB.Users.Include(o => o.UserFavorites).ThenInclude(o => o.Tests)
                                    .Include(o => o.UserFavorites).ThenInclude(o => o.Practices)
                                    .Include(o => o.UserFavorites).ThenInclude(o => o.Exercises)
                                    .Include(o => o.UserFavorites).ThenInclude(o => o.Topics)
                                    .FirstOrDefault(o => o.Id == id)
                                    .UserFavorites;

            favorites.TestsIds = favorites.Tests.Where(o => !o.IsDeleted).Select(o => o.Id);
            favorites.ExercisesIds = favorites.Exercises.Where(o => !o.IsDeleted).Select(o => o.Id);
            favorites.PracticesIds = favorites.Practices.Where(o => !o.IsDeleted).Select(o => o.Id);
            favorites.TopicsIds = favorites.Topics.Where(o => !o.IsDeleted).Select(o => o.Id);


            favorites.Tests = new List<Test>();
            favorites.Exercises = new List<Exercise>();
            favorites.Practices = new List<Practice>();
            favorites.Topics = new List<Topic>();

            return favorites;
        }


        [HttpGet, Route("GetUserFavoriteTests")]
        public async Task<string> GetUserFavoriteTests(int id)
        {
            var tests = DB.Users.Include(o => o.UserFavorites).ThenInclude(o => o.Tests)
                                 .Include(o => o.UserFavorites).ThenInclude(o => o.Tests).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                 .Include(o => o.UserFavorites).ThenInclude(o => o.Tests).ThenInclude(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                 .Include(o => o.UserFavorites).ThenInclude(o => o.Tests).ThenInclude(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                 .Include(o => o.UserFavorites).ThenInclude(o => o.Tests).ThenInclude(o => o.Results).ThenInclude(o => o.Scale)
                                 .Include(o => o.UserFavorites).ThenInclude(o => o.Tests).ThenInclude(o => o.Scales).ThenInclude(o => o.Localizations)
                                .FirstOrDefault(o => o.Id == id)
                                .UserFavorites.Tests.Where(o => !o.IsDeleted);
            foreach (var item in tests)
            {
                TestsController.IncludeQuestions(DB, item);
                item.UserFavoritesReferences = new List<UserFavorites>();
            }

            TestsController.OrderQuestions(tests);
            var str = MakeNewtonsoftJsonString(tests);
            return str;

        }

        [HttpGet, Route("GetUserFavoriteTopics")]
        public async Task<IEnumerable<Topic>> GetUserFavoriteTopics(int id)
        {
            var topics = DB.Users.Include(o => o.UserFavorites).ThenInclude(o => o.Topics).ThenInclude(o => o.Category)
                                 .Include(o => o.UserFavorites).ThenInclude(o => o.Topics).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                 .FirstOrDefault(o => o.Id == id)
                                 .UserFavorites.Topics.Where(o => !o.IsDeleted);

            foreach (var topic in topics)
            {
                topic.UserFavoritesReferences = new List<UserFavorites>();
            }

            return topics;
        }
        [HttpGet, Route("GetUserFavoritePractices")]
        public async Task<IEnumerable<Practice>> GetUserFavoritePractices(int id)
        {
            var practices = DB.Users.Include(o => o.UserFavorites).ThenInclude(o => o.Practices).ThenInclude(o => o.Category)
                                    .Include(o => o.UserFavorites).ThenInclude(o => o.Practices).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                    .FirstOrDefault(o => o.Id == id)
                                    .UserFavorites.Practices.Where(o => !o.IsDeleted);

            foreach (var item in practices)
            {
                item.UserFavoritesReferences = new List<UserFavorites>();
            }

            return practices;
        }
        [HttpGet, Route("GetUserFavoriteExercises")]
        public async Task<IEnumerable<Exercise>> GetUserFavoriteExercises(int id)
        {
            var exercises = DB.Users.Include(o => o.UserFavorites).ThenInclude(o => o.Exercises).ThenInclude(o => o.Category)
                                    .Include(o => o.UserFavorites).ThenInclude(o => o.Exercises).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                    .FirstOrDefault(o => o.Id == id)
                                    .UserFavorites.Exercises.Where(o => !o.IsDeleted);

            foreach (var item in exercises)
            {
                item.UserFavoritesReferences = new List<UserFavorites>();
            }

            return exercises;
        }
        #endregion

        #region Добавление/удаление из избранных

        [HttpPost, Route("SetFavorite")]
        public async Task<bool?> SetFavorite(int userId, AppContentType content, int id, bool value)
        {
            try
            {
                var users = DB.Users
                    .Include(o => o.UserFavorites);
                User user = null;
                switch (content)
                {

                    case AppContentType.Exercise:
                    user = users
                        .ThenInclude(o => o.Exercises)
                        .First(o => o.Id == userId);

                    var exercise = user.UserFavorites.Exercises.FirstOrDefault(o => o.Id == id);
                    if (value && exercise == null)
                    {
                        var existing = DB.Exercises.First(o => o.Id == id);
                        user.UserFavorites.Exercises.Add(existing);
                    }
                    else if (exercise != null && !value)
                    {
                        user.UserFavorites.Exercises.Remove(exercise);
                    }

                    break;

                    case AppContentType.Practice:
                    user = users
                        .ThenInclude(o => o.Practices)
                        .First(o => o.Id == userId);

                    var practice = user.UserFavorites.Practices.FirstOrDefault(o => o.Id == id);
                    if (value && practice == null)
                    {
                        var existing = DB.Practices.First(o => o.Id == id);
                        user.UserFavorites.Practices.Add(existing);
                    }
                    else if (practice != null && !value)
                    {
                        user.UserFavorites.Practices.Remove(practice);
                    }
                    break;

                    case AppContentType.Topic:
                    user = users
                        .ThenInclude(o => o.Topics)
                        .First(o => o.Id == userId);

                    var topic = user.UserFavorites.Topics.FirstOrDefault(o => o.Id == id);
                    if (value && topic == null)
                    {
                        var existing = DB.Topics.First(o => o.Id == id);
                        user.UserFavorites.Topics.Add(existing);
                    }
                    else if (topic != null && !value)
                    {
                        user.UserFavorites.Topics.Remove(topic);
                    }
                    break;

                    case AppContentType.Test:
                    user = users
                        .ThenInclude(o => o.Tests)
                        .First(o => o.Id == userId);

                    var test = user.UserFavorites.Tests.FirstOrDefault(o => o.Id == id);
                    if (value && test == null)
                    {
                        var existing = DB.Tests.First(o => o.Id == id);
                        user.UserFavorites.Tests.Add(existing);
                    }
                    else if (test != null && !value)
                    {
                        user.UserFavorites.Tests.Remove(test);
                    }
                    break;

                }

                if (user != null)
                {
                    DB.Users.Update(user);
                    await DB.SaveChangesAsync();
                }

                return value;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            return null;




        }

        [HttpPut, Route("ToggleFavoriteExercise")]
        public async Task<object> ToggleFavoriteExercise(int userId, int exerciseId)
        {
            var user = DB.Users
                .Include(o => o.UserFavorites).ThenInclude(o => o.Exercises)
                .FirstOrDefault(o => o.Id == userId);

            try
            {
                var found = user.UserFavorites.Exercises.FirstOrDefault(o => o.Id == exerciseId);
                if (found is null)
                {
                    var itemFromDB = DB.Exercises.FirstOrDefault(o => o.Id == exerciseId);

                    user.UserFavorites.Exercises.Add(itemFromDB);
                    DB.Users.Update(user);
                    await DB.SaveChangesAsync();
                    return true;
                }
                else
                {
                    user.UserFavorites.Exercises.Remove(found);

                    DB.Users.Update(user);
                    await DB.SaveChangesAsync();
                    return false;
                }
            }
            catch (Exception ex)
            {
                return ex.ToString();
            }
        }

        [HttpPut, Route("ToggleFavoritePractice")]
        public async Task<bool> ToggleFavoritePractice(int userId, int practiceId)
        {
            var user = DB.Users
                .Include(o => o.UserFavorites).ThenInclude(o => o.Practices)
                .FirstOrDefault(o => o.Id == userId);

            var found = user.UserFavorites.Practices.FirstOrDefault(o => o.Id == practiceId);
            if (found is null)
            {
                var itemFromDB = DB.Practices.FirstOrDefault(o => o.Id == practiceId);

                user.UserFavorites.Practices.Add(itemFromDB);
                DB.Users.Update(user);
                await DB.SaveChangesAsync();
                return true;
            }
            else
            {
                user.UserFavorites.Practices.Remove(found);

                DB.Users.Update(user);
                await DB.SaveChangesAsync();
                return false;
            }
        }

        [HttpPut, Route("ToggleFavoriteTopic")]
        public async Task<bool> ToggleFavoriteTopic(int userId, int topicId)
        {
            var user = DB.Users
                .Include(o => o.UserFavorites).ThenInclude(o => o.Topics)
                .FirstOrDefault(o => o.Id == userId);

            var found = user.UserFavorites.Topics.FirstOrDefault(o => o.Id == topicId);
            if (found is null)
            {
                var itemFromDB = DB.Topics.FirstOrDefault(o => o.Id == topicId);

                user.UserFavorites.Topics.Add(itemFromDB);
                DB.Users.Update(user);
                await DB.SaveChangesAsync();
                return true;
            }
            else
            {
                user.UserFavorites.Topics.Remove(found);

                DB.Users.Update(user);
                await DB.SaveChangesAsync();
                return false;
            }
        }

        [HttpPut, Route("ToggleFavoriteTest")]
        public async Task<bool> ToggleFavoriteTest(int userId, int testId)
        {
            var user = DB.Users
                .Include(o => o.UserFavorites).ThenInclude(o => o.Tests)
                .FirstOrDefault(o => o.Id == userId);

            var found = user.UserFavorites.Tests.FirstOrDefault(o => o.Id == testId);
            if (found is null)
            {
                var itemFromDB = DB.Tests.FirstOrDefault(o => o.Id == testId);

                user.UserFavorites.Tests.Add(itemFromDB);
                DB.Users.Update(user);
                await DB.SaveChangesAsync();
                return true;
            }
            else
            {
                user.UserFavorites.Tests.Remove(found);

                DB.Users.Update(user);
                await DB.SaveChangesAsync();
                return false;
            }
        }
        #endregion

        #region Проверка наличия в избранных
        [HttpGet, Route("HasFavoriteExercise")]
        public async Task<bool> HasFavoriteExercise(int userId, int exerciseId)
        {
            var user = DB.Users
                .Include(o => o.UserFavorites).ThenInclude(o => o.Exercises)
                .FirstOrDefault(o => o.Id == userId);
            return user.UserFavorites.Exercises.Any(o => o.Id == exerciseId);
        }
        [HttpGet, Route("HasFavoritePractice")]
        public async Task<bool> HasFavoritePractice(int userId, int practiceId)
        {
            var user = DB.Users
                .Include(o => o.UserFavorites).ThenInclude(o => o.Practices)
                .FirstOrDefault(o => o.Id == userId);
            return user.UserFavorites.Practices.Any(o => o.Id == practiceId);
        }
        [HttpGet, Route("HasFavoriteTopic")]
        public async Task<bool> HasFavoriteTopic(int userId, int topicId)
        {
            var user = DB.Users
                .Include(o => o.UserFavorites).ThenInclude(o => o.Topics)
                .FirstOrDefault(o => o.Id == userId);
            return user.UserFavorites.Topics.Any(o => o.Id == topicId);
        }
        [HttpGet, Route("HasFavoriteTest")]
        public async Task<bool> HasFavoriteTest(int userId, int testId)
        {
            var user = DB.Users
                .Include(o => o.UserFavorites).ThenInclude(o => o.Tests)
                .FirstOrDefault(o => o.Id == userId);
            return user.UserFavorites.Tests.Any(o => o.Id == testId);
        }
        #endregion
    }
}
