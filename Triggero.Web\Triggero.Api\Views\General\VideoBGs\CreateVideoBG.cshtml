﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.General
@using Triggero.Models.Practices
@using Triggero.Models.Practices.Categories
@using Triggero.Web.ViewModels.Practices
@{
    ViewData["Title"] = "ДОБАВЛЕНИЕ ВИДЕО ФОНА";
}
@model VideoBg

 <!-- CONTENT -->
<form asp-action="CreateVideoBGPOST" asp-controller="VideoBg" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">

            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Видеозапись</label>
                <input type="file" name="file" required class="form-control" placeholder="">
            </div>   

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>