﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Practices.Categories
@using Triggero.Web.ViewModels.Practices
@{
    ViewData["Title"] = "РЕДАКТИРОВАНИЕ ПРАКТИКИ";
}
@model CreatePracticeVM

 <!-- CONTENT -->
<form asp-action="UpdatePracticePOST" asp-controller="Practices" onsubmit="onSubmit(event)" method="post" enctype="multipart/form-data" class="content">

    <input type="hidden" asp-for="NewPractice.Id" value="@Model.NewPractice.Id"/>
    <input type="hidden" asp-for="NewPractice.IsHidden" value="@Model.NewPractice.IsHidden" />
    <input type="hidden" asp-for="NewPractice.AudioPath" value="@Model.NewPractice.AudioPath"/>
    <input type="hidden" asp-for="NewPractice.ImgPath" value="@Model.NewPractice.ImgPath" />
    <input type="hidden" asp-for="NewPractice.IconImgPath" value="@Model.NewPractice.IconImgPath" />
    <input type="hidden" asp-for="NewPractice.Watches" value="@Model.NewPractice.Watches" />

    <div class="row">
        <div class="page">

            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="NewPractice.Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Текст</label>
                <textarea id="editor-input" asp-for="NewPractice.Description" rows="3"></textarea>
                <script>
                    $('#editor-input')
                        .trumbowyg({
                            btnsDef: {
                                // Create a new dropdown
                                image: {
                                    dropdown: ['insertImage', 'noembed'],
                                    ico: 'insertImage'
                                }
                            },
                            // Redefine the button pane
                            btns: [
                                ['viewHTML'],
                                ['formatting'],
                                ['strong', 'em', 'del'],
                                ['superscript', 'subscript'],
                                ['link'],
                                ['image'], // Our fresh created dropdown
                                ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
                                ['unorderedList', 'orderedList'],
                                ['horizontalRule'],
                                ['removeformat'],
                                ['fullscreen']
                            ]
                        });
                </script>
            </div>
            
            <div class="mb-3">
                <label for="" class="formlabel">Ссылка на источник</label>
                <input type="url" asp-for="NewPractice.ReferenceLink" class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Время в минутах на прохождение</label>
                <input type="number" asp-for="NewPractice.PassingTimeInMinutes" required class="form-control" placeholder="">
            </div>


            <div class="mb-3">
                <label for="" class="formlabel">Превью</label>
                <div class="content-load">
                    <div class="close-file" id="close_file3"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image3" for="pct3" style="height: 6em;background-image: url('@Model.NewPractice.IconImgPath');background-size: cover;background-repeat: no-repeat;">
                        <div class="text-fileupload" id="uploadText3">
                            @if (string.IsNullOrEmpty(Model.NewPractice.IconImgPath))
                            {
                                @("Загрузите файл")
                            }
                        </div>
                    </label>
                    <input class="pctfile" type="file" name="imgPreview" id="pct3">
                </div>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Загрузить изображение</label>
                <div class="content-load" style="">
                    <div class="close-file" id="close_file"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image" for="pct" style="height: 11em;background: url('@Model.NewPractice.ImgPath');background-size: cover;background-repeat: no-repeat;">
                        <div class="text-fileupload">
                            @if (string.IsNullOrEmpty(Model.NewPractice.ImgPath))
                            {
                                @("Загрузите файл")
                            }
                        </div>
                    </label>
                    <input class="pctfile" type="file" name="img" id="pct">
                </div>
            </div>
            
            <div class="mb-3">
                <label for="" class="formlabel">Аудиозапись</label>             
                <audio src="@Model.NewPractice.AudioPath"></audio>
                <input type="file" name="audio" class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Основные теги</label>
                <input type="text" asp-for="NewPractice.MainTags" value="@Model.NewPractice.MainTags" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Вторичные теги</label>
                <input type="text" asp-for="NewPractice.SecondaryTags" value="@Model.NewPractice.SecondaryTags" required class="form-control" placeholder="">
            </div>


            <div class="mb-3">
                <label for="" class="formlabel">Категория</label>
                <select class="form-select" asp-for="NewPractice.CategoryId"  required>
                    @Html.Raw(@ClassesToHtmlHelper<PracticeCategory>.Convert(Model.Categories,Model.NewPractice.CategoryId))
                </select>
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>

<script>

    async function onSubmit(event) {
        event.preventDefault();

        await cropFinal('pct', 'upload-image');
        await cropFinal('pct3', 'upload-image3');

        event.target.submit();
    }

    $(document).ready(function () {
        initImageCroppieSized('upload-image', 'pct', 'close_file', 322, 640);
        initImageCroppieSized('upload-image3', 'pct3', 'close_file3', 300, 300);
    });


</script>