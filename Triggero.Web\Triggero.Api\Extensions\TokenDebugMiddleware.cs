﻿using System.IdentityModel.Tokens.Jwt;
using System.Text;
using Microsoft.IdentityModel.Tokens;

namespace Triggero.Domain.Extensions;

public class TokenDebugMiddleware
{
    private readonly RequestDelegate _next;
    private readonly IConfiguration _configuration;
    private readonly ILogger<TokenDebugMiddleware> _logger;

    public TokenDebugMiddleware(RequestDelegate next, IConfiguration configuration, ILogger<TokenDebugMiddleware> logger)
    {
        _next = next;
        _configuration = configuration;
        _logger = logger;
    }

    public async Task Invoke(HttpContext context)
    {
        var token = context.Request.Headers["Authorization"].FirstOrDefault()?.Split(" ").Last();
        if (token != null)
        {
            var validationResult = ValidateToken(token);
            if (!validationResult.IsValid)
            {
                _logger.LogError($"Token validation failed: {validationResult.ErrorMessage}");
                context.Response.StatusCode = 401; // Unauthorized
                await context.Response.WriteAsync($"Invalid token: {validationResult.ErrorMessage}");
                return;
            }
        }
        await _next(context);
    }

    public TokenValidationResult ValidateToken(string token)
    {
        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuer = true,
            ValidIssuer = _configuration["Jwt:Issuer"],
            ValidateAudience = true,
            ValidAudience = _configuration["Jwt:Issuer"],
            ValidateLifetime = true,
            IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(_configuration["Jwt:Key"])),
            ValidateIssuerSigningKey = true
        };

        var tokenHandler = new JwtSecurityTokenHandler();
        try
        {
            var principal = tokenHandler.ValidateToken(token, validationParameters, out var validatedToken);
            return new TokenValidationResult { IsValid = true };
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Token validation exception.");
            return new TokenValidationResult { IsValid = false, ErrorMessage = ex.Message };
        }
    }

    public class TokenValidationResult
    {
        public bool IsValid { get; set; }
        public string ErrorMessage { get; set; }
    }
}