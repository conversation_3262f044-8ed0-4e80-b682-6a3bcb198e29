﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Plans
@{
    ViewData["Title"] = "РЕДАКТИРОВАНИЕ ПРОМОКОДА";
}
@model Promocode

 <!-- CONTENT -->
<form asp-action="UpdatePromocodePOST" asp-controller="Promocodes" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">

            <input type="hidden" asp-for="Id" value="@Model.Id" />

           <div class="mb-3">
               <label for="" class="formlabel">Код</label>
               <input type="text" asp-for="Code" value="@Model.Code"  required class="form-control" placeholder="">
           </div>

           <div class="mb-3">
                <label for="" class="formlabel">Процент</label>
                <input type="number" asp-for="Value" value="@Model.Value" required class="form-control" placeholder="">
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>