﻿using Newtonsoft.Json;
using System.IO;
using Triggero.Models.General;

namespace Triggero.MauiClient.Helpers.Modules
{
    public class ConfigData
    {
        public static string FilePath = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + @"/configData.json";
        public bool IsFirstUse { get; set; } = true;
        public bool WasShownStartTutorial { get; set; } = false;
        public bool WasShownFullTutorial { get; set; } = false;

        public string AuthToken { get; set; }
        public int UserId { get; set; }
        public User User { get; set; }

        public void SaveChangesToMemory()
        {
            try
            {
                var json = JsonConvert.SerializeObject(this);
                File.WriteAllText(FilePath, json);
            }
            catch { }
        }
    }
}
