﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Domain.Models;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Models.Enums;
using Triggero.Models.Tests;

namespace Triggero.Domain.Controllers.Rest
{
    [ApiController]
    [Route("[controller]")]
    public class TestsController : ApiController
    {
        public TestsController(DatabaseContext db) : base(db)
        {
        }

        [ResponseCache(Duration = 120)]
        [HttpGet, Route("GetTestCategories")]
        public async Task<List<TestCategory>> GetTestCategories()
        {
            return DB.TestCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .ToList();
        }

        [ResponseCache(VaryByQueryKeys = new[] { "count", "offset" }, Duration = 120)]
        [HttpGet, Route("GetTests")]
        public async Task<TestsSelectionModel> GetTests(int count, int offset)
        {
            var tests = DB.Tests
                 .Where(o => !o.IsDeleted && !o.IsHidden && o.TestPurpose == TestPurpose.Public)
                 .Skip(offset)
                 .Take(count)
                 .Include(o => o.Localizations).ThenInclude(o => o.Language)
                 .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                 .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                 .Include(o => o.Results).ThenInclude(o => o.Scale)
                 .Include(o => o.Scales).ThenInclude(o => o.Localizations)
                 .ToList();

            tests.ForEach(o => IncludeQuestions(DB, o));


            OrderQuestions(tests);//https://localhost:7013/tests/getTests
            var str = MakeNewtonsoftJsonString(tests);
            int l = str.Length;

            return new TestsSelectionModel
            {
                StartRow = offset,
                Count = count,
                Json = str,
                Total = DB.Tests.Count(o => !o.IsDeleted && !o.IsHidden && o.TestPurpose == TestPurpose.Public)
            };
        }

        [HttpGet, Route("GetRandomTests")]
        public async Task<string> GetRandomTests(int count)
        {
            var tests = DB.Tests
                 .Where(o => !o.IsDeleted && !o.IsHidden && o.TestPurpose == TestPurpose.Public)
                 .Include(o => o.Localizations).ThenInclude(o => o.Language)
                 .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                 .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                 .Include(o => o.Results).ThenInclude(o => o.Scale)
                 .Include(o => o.Scales).ThenInclude(o => o.Localizations)
                 .ToList();

            tests.ForEach(o => IncludeQuestions(DB, o));


            List<Test> randomTests = new List<Test>();
            int totalCount = tests.Count();
            var random = new Random();
            for (int i = 0; i < count; i++)
            {
                randomTests.Add(tests.ElementAt(random.Next(0, totalCount - 1)));
            }


            OrderQuestions(randomTests);
            var str = MakeNewtonsoftJsonString(randomTests);


            return str;
        }

        [HttpGet, Route("GetTestsByCategory")]
        public async Task<string> GetTestsByCategory(int categoryId)
        {
            var tests = DB.Tests
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Questions).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.ScaleScoreInfos)
                .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Results).ThenInclude(o => o.Scale)
                .Include(o => o.Scales).ThenInclude(o => o.Localizations)
                .Where(o => !o.IsDeleted && !o.IsHidden && o.CategoryId == categoryId && o.TestPurpose == TestPurpose.Public)
                .ToList();

            tests.ForEach(o => IncludeQuestions(DB, o));

            OrderQuestions(tests);
            return MakeNewtonsoftJsonString(tests);
        }

        [HttpGet, Route("GetTestsByCategoryChunk")]
        public async Task<string> GetTestsByCategoryChunk(int categoryId, int count, int offset)
        {
            var tests = DB.Tests
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Questions).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.ScaleScoreInfos)
                .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Results).ThenInclude(o => o.Scale)
                .Include(o => o.Scales).ThenInclude(o => o.Localizations)
                .Where(o => !o.IsDeleted && !o.IsHidden && o.CategoryId == categoryId && o.TestPurpose == TestPurpose.Public)
                .Skip(offset).Take(count)
                .ToList();

            tests.ForEach(o => IncludeQuestions(DB, o));

            OrderQuestions(tests);
            return MakeNewtonsoftJsonString(tests);
        }






        [HttpGet, Route("GetTestsByTag")]
        public async Task<string> GetTestsByTag(string tag)
        {
            var tests = DB.Tests
                 .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Results).ThenInclude(o => o.Scale)
                .Include(o => o.Scales).ThenInclude(o => o.Localizations)
                 .Where(o => !o.IsDeleted && !o.IsHidden && o.TestPurpose == TestPurpose.Public)
                 .Where(o => o.MainTags.Equals(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Equals(tag, StringComparison.OrdinalIgnoreCase))
                 .ToList();

            tests.ForEach(o => IncludeQuestions(DB, o));


            var exercises = DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.MainTags.Equals(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Equals(tag, StringComparison.OrdinalIgnoreCase))
                .ToList();
            var practices = DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.MainTags.Equals(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Equals(tag, StringComparison.OrdinalIgnoreCase))
                .ToList();




            OrderQuestions(tests);
            return MakeNewtonsoftJsonString(tests);
        }

        [HttpGet, Route("GetTestsByTagChunk")]
        public async Task<string> GetTestsByTagChunk(string tag, int count, int offset)
        {
            var tests = DB.Tests
                 .Include(o => o.Localizations).ThenInclude(o => o.Language)
                 .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                 .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                 .Include(o => o.Results).ThenInclude(o => o.Scale)
                 .Include(o => o.Scales).ThenInclude(o => o.Localizations)
                 .Where(o => !o.IsDeleted && !o.IsHidden && o.TestPurpose == TestPurpose.Public)
                 .Where(o => o.MainTags.Equals(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Equals(tag, StringComparison.OrdinalIgnoreCase))
                 .Skip(offset).Take(count)
                 .ToList();

            tests.ForEach(o => IncludeQuestions(DB, o));


            var exercises = DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.MainTags.Equals(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Equals(tag, StringComparison.OrdinalIgnoreCase))
                .ToList();
            var practices = DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.MainTags.Equals(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Equals(tag, StringComparison.OrdinalIgnoreCase))
                .ToList();




            OrderQuestions(tests);
            return MakeNewtonsoftJsonString(tests);
        }






        [HttpGet, Route("GetTest")]
        public async Task<string> GetTest(int id)
        {
            var test = DB.Tests
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Results).ThenInclude(o => o.Scale)
                .Include(o => o.Scales).ThenInclude(o => o.Localizations)
                .FirstOrDefault(o => o.Id == id);

            IncludeQuestions(DB, test);

            OrderQuestions(test);
            return MakeNewtonsoftJsonString(test);
        }

        [HttpGet, Route("GetTutorialTest")]
        public async Task<string> GetTutorialTest()
        {
            var test = DB.Tests
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Questions).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.ScaleScoreInfos)
                .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Results).ThenInclude(o => o.Scale)
                .Include(o => o.Scales).ThenInclude(o => o.Localizations)
                .FirstOrDefault(o => o.TestPurpose == TestPurpose.Tutorial && !o.IsDeleted);

            OrderQuestions(test);
            return MakeNewtonsoftJsonString(test);
        }



        [HttpPut, Route("AddWatch")]
        public async Task AddWatch(int testId)
        {
            var test = DB.Tests.FirstOrDefault(o => o.Id == testId);
            test.Watches++;

            DB.Tests.Update(test);
            await DB.SaveChangesAsync();
        }











        private void OrderQuestions(params Test[] tests)
        {
            foreach (var test in tests)
            {
                test.GlobalId = "";
                test.Questions = test.Questions.OrderBy(o => o.Number).ToList();

                test.Questions.ForEach(o => o.GlobalId = "");
                test.Results.ForEach(o => o.GlobalId = "");
                test.Scales.ForEach(o => o.GlobalId = "");

                foreach (var option in test.Questions.SelectMany(o => o.Options))
                {
                    option.GlobalId = "";
                    option.ScaleScoreInfos.ForEach(o => o.GlobalId = "");
                }
            }
        }
        public static void OrderQuestions(IEnumerable<Test> tests)
        {
            foreach (var test in tests)
            {
                test.GlobalId = "";

                foreach (var result in test.Results)
                {
                    result.GlobalId = "";
                    if (result.Scale != null)
                    {
                        result.Scale.GlobalId = "";
                    }
                }


                test.Scales.ForEach(o => o.GlobalId = "");


                test.Questions = test.Questions.OrderBy(o => o.Number).ToList();
                test.Questions.ForEach(o => o.GlobalId = "");
                foreach (var option in test.Questions.SelectMany(o => o.Options))
                {
                    option.GlobalId = "";
                    option.ScaleScoreInfos.ForEach(o =>
                    {
                        o.GlobalId = "";
                        o.Scale = null;
                    });
                }
            }
        }


        public static void IncludeQuestions(DatabaseContext db, Test test)
        {
            var questions = db.Questions.Include(o => o.Options).ThenInclude(o => o.ScaleScoreInfos)
                                           // .Include(o => o.Options).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                           //.Include(o => o.Localizations).ThenInclude(o => o.Language)
                                           .Where(o => o.TestId == test.Id)
                                           .ToList();
            test.Questions = questions;
        }
    }
}
