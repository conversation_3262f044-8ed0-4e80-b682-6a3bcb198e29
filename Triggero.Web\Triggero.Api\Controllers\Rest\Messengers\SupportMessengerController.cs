﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Enums;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Models.Enums;
using Triggero.Models.Messengers.Support;

namespace Triggero.Domain.Controllers.Rest.Messengers
{
    [ApiController]
    [Route("[controller]")]
    public class SupportMessengerController : ApiController
    {
        public SupportMessengerController(DatabaseContext db) : base(db)
        {
        }

        [HttpGet, Route("GetChat")]
        public async Task<SupportChat> GetChat(int userId)
        {
            var user = DB.Users.Include(o => o.SupportChat).ThenInclude(o => o.Messages)
                               .FirstOrDefault(o => o.Id == userId);
            user.SupportChat.User = null;
            return user.SupportChat;
        }

        [HttpPut, Route("SendMessage")]
        public async Task SendMessage(int userId, SupportChatMessage message)
        {
            var user = DB.Users.Include(o => o.SupportChat)
                               .FirstOrDefault(o => o.Id == userId);

            message.ChatSide = ChatSide.User;
            message.SentAt = DateTime.UtcNow;

            user.SupportChat.Messages.Add(message);
            DB.Users.Update(user);
            await DB.SaveChangesAsync();
        }

        [HttpGet, Route("LongPolling")]
        public async Task<SupportChatLongPollItem> LongPolling(int userId)
        {
            var result = new SupportChatLongPollItem();
            await Task.Run(async () =>
            {
                var user = DB.Users.Include(o => o.SupportChat).ThenInclude(o => o.Messages)
                               .FirstOrDefault(o => o.Id == userId);
                int count = user.SupportChat.Messages.Count;

                for (int i = 0; i < 30; i++)
                {

                    var userV2 = DB.Users.Include(o => o.SupportChat).ThenInclude(o => o.Messages)
                                 .FirstOrDefault(o => o.Id == userId);
                    int countV2 = user.SupportChat.Messages.Count;

                    if (countV2 > count)
                    {
                        result = new SupportChatLongPollItem
                        {
                            Type = SupportChatLongPollType.NewMessage,
                            Messages = user.SupportChat.Messages.TakeLast(countV2 - count).ToList()
                        };
                    }

                    await Task.Delay(3000);
                }
            });
            return result;
        }


        [HttpGet, Route("LongPollingV2")]
        public async Task<SupportChatLongPollItem> LongPollingV2(int userId, int startMessagesCount)
        {
            var result = new SupportChatLongPollItem()
            {
                TotalMessagesCount = startMessagesCount
            };

            //await Task.Run(async () =>
            //{
            //    //for (int i = 0; i < 30; i++)
            //    //{
            //        ///await Task.Delay(3000);
            //    //}





            //});

            var userV2 = DB.Users.Include(o => o.SupportChat).ThenInclude(o => o.Messages)
                                 .FirstOrDefault(o => o.Id == userId);
            int countV2 = userV2.SupportChat.Messages.Count;

            if (countV2 > startMessagesCount)
            {
                result = new SupportChatLongPollItem
                {
                    TotalMessagesCount = countV2,
                    Type = SupportChatLongPollType.NewMessage,
                    Messages = userV2.SupportChat.Messages.TakeLast(countV2 - startMessagesCount).ToList()
                };
            }


            return result;
        }
    }
}
