﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Plans;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Plans;
using Triggero.Models.Practices;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class PlanOptionsLocalizationsController : AbsController
    {
        public PlanOptionsLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/PlanOptions")]
        public IActionResult PlanOptions()
        {
            var posts = DB.PlanOptions
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .ToList();
            posts.Reverse();

            var vm = new LocalizationListVM<PlanOption>
            {
                Items = posts,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Plans\PlanOptions\PlanOptions.cshtml", vm);
        }






        [Route("Localizations/CreatePlanOptionLocalization")]
        public IActionResult CreatePlanOptionLocalization(int id)
        {
            var vm = new LocalizationVM<PlanOptionLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Plans\PlanOptions\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreatePlanOptionLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreatePlanOptionLocalizationPOST([FromForm] LocalizationVM<PlanOptionLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();

            var option = DB.PlanOptions
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => o.Id == vm.Id);
            option.Localizations.Add(vm.Localization);

            DB.PlanOptions.Update(option);
            await DB.SaveChangesAsync();
            return RedirectToAction("PlanOptions", "PlanOptionsLocalizations");
        }





        [Route("Localizations/UpdatePlanOptionLocalization")]
        public IActionResult UpdatePlanOptionLocalization(int id)
        {
            var option = DB.PlanOptions
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<PlanOptionLocalization>
            {
                Id = id,
                Localization = option.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Plans\PlanOptions\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdatePlanOptionLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdatePlanOptionLocalizationPOST([FromForm] LocalizationVM<PlanOptionLocalization> vm)
        {
            DB.PlanOptionLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("PlanOptions", "PlanOptionsLocalizations");
        }

    }
}
