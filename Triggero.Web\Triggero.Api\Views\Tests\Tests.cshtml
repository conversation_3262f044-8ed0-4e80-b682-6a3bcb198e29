﻿@using MarkupCreator.Helpers.Converters;
@using Triggero.Models
@using Triggero.Models.Enums;
@using Triggero.Models.Tests
@using Triggero.Web.Extensions;
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "ТЕСТЫ";
}
@model List<Test>
<!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                    <th scope="col">ID</th>
                    <th scope="col">Изображение</th>
                    <th scope="col">Название</th>
                    <th scope="col">Краткое содержание</th>
                    <th scope="col">Тип</th>
                    <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model)
                    {
                         <tr>
                            <th scope="row">#@item.Id</th>
                            <th scope="row"><div class="imagetable" style="background: url('@item.IconImgPath'); background-size: 100% 100%;"></div></th>
                            <td>@item.Title</td>
                            <td>@Html.Raw(item.Description.SafeSubstringWithStr(0,100,"..."))</td>
                            <td>@EnumDescriptionHelper.GetDescription(item.TestPurpose)</td>
                            <td>
                                <div class="interect">
                                    <a asp-action="TestStartPage" asp-controller="Tests" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    @if(item.TestPurpose == TestPurpose.Public)
                                    {
                                        <a onclick="confirmDeleting('deleteItem(@item.Id)')" class="delete"><i class="fa-solid fa-trash"></i></a>

                                        @if (item.IsHidden)
                                        {
                                            <a asp-controller="Tests" asp-action="SetVisibility" asp-route-id="@item.Id" asp-route-hidden="@(!item.IsHidden)" class="delete"><i class="fa-solid fa-eye"></i></a>
                                        }
                                        else
                                        {
                                            <a asp-controller="Tests" asp-action="SetVisibility" asp-route-id="@item.Id" asp-route-hidden="@(!item.IsHidden)" class="delete"><i class="fa-solid fa-eye-slash"></i></a>
                                        }
                                    }

                                 

                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

            <div class="mb-3 position-right">
                <a asp-action="TestStartPage" asp-controller="Tests" class="button-classic">Добавить</a>
            </div>

            <script>
                $(document).ready( function () {
                    $('#tablemanager').DataTable();
                } );
            </script>

            <script>
                async function deleteItem(id) {
                    await fetch(document.location.origin + '/DeleteTest?id=' + id,
                        {
                            method: 'DELETE'
                        });
                }
            </script>
        </div>
    </div>
</div>