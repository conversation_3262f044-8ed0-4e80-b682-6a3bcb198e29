﻿using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.AspNetCore.Mvc.ModelBinding;
using Triggero.Models.Abstractions;
using Triggero.Models.Localization.Interface.Auth;

namespace Triggero.Web
{
    public class LocalizationModelModelBinding
    {
    }

    //public class LocalizationModelBinderProvider : IModelBinderProvider
    //{
    //    public IModelBinder GetBinder(ModelBinderProviderContext context)
    //    {
    //        if (context.Metadata.ModelType != typeof(LocalizationModel))
    //        {
    //            return null;
    //        }

    //        var subclasses = new[] { typeof(AccountRegisteredLocalization), typeof(CreateAccountLocalization), };

    //        var binders = new Dictionary<Type, (ModelMetadata, IModelBinder)>();
    //        foreach (var type in subclasses)
    //        {
    //            var modelMetadata = context.MetadataProvider.GetMetadataForType(type);
    //            binders[type] = (modelMetadata, context.CreateBinder(modelMetadata));
    //        }

    //        return new LocalizationModelBinder(binders);
    //    }
    //}

    //public class LocalizationModelBinder : IModelBinder
    //{
    //    private Dictionary<Type, (ModelMetadata, IModelBinder)> binders;

    //    public LocalizationModelBinder(Dictionary<Type, (ModelMetadata, IModelBinder)> binders)
    //    {
    //        this.binders = binders;
    //    }

    //    public async Task BindModelAsync(ModelBindingContext bindingContext)
    //    {
    //        var modelKindName = ModelNames.CreatePropertyModelName(bindingContext.ModelName, nameof(Device.Kind));
    //        var modelTypeValue = bindingContext.ValueProvider.GetValue(modelKindName).FirstValue;

    //        IModelBinder modelBinder;
    //        ModelMetadata modelMetadata;
    //        if (modelTypeValue == "Laptop")
    //        {
    //            (modelMetadata, modelBinder) = binders[typeof(Laptop)];
    //        }
    //        else if (modelTypeValue == "SmartPhone")
    //        {
    //            (modelMetadata, modelBinder) = binders[typeof(SmartPhone)];
    //        }
    //        else
    //        {
    //            bindingContext.Result = ModelBindingResult.Failed();
    //            return;
    //        }

    //        var newBindingContext = DefaultModelBindingContext.CreateBindingContext(
    //            bindingContext.ActionContext,
    //            bindingContext.ValueProvider,
    //            modelMetadata,
    //            bindingInfo: null,
    //            bindingContext.ModelName);

    //        await modelBinder.BindModelAsync(newBindingContext);
    //        bindingContext.Result = newBindingContext.Result;

    //        if (newBindingContext.Result.IsModelSet)
    //        {
    //            // Setting the ValidationState ensures properties on derived types are correctly 
    //            bindingContext.ValidationState[newBindingContext.Result.Model] = new ValidationStateEntry
    //            {
    //                Metadata = modelMetadata,
    //            };
    //        }
    //    }
    //}
}
