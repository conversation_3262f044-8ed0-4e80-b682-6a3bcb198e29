﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Localization
@using Triggero.Models.Localization.Practices
@using Triggero.Models.Localization.Practices.Categories
@using Triggero.Web.ViewModels.Localization
@{
    ViewData["Title"] = "РЕДАКТИРОВАНИЕ ПЕРЕВОДА";
    ViewData["ShowLang"] = true;
}
@model LocalizationVM<TopicCategoryLocalization>

 <!-- CONTENT -->
<form asp-action="UpdateTopicCategoryLocalizationPOST" asp-controller="TopicCategoriesLocalizations" method="post" enctype="multipart/form-data" class="content">

    <input type="hidden" asp-for="Id" value="@Model.Id" />
    <input type="hidden" asp-for="Localization.Id" value="@Model.Localization.Id" />
    <input type="hidden" asp-for="Localization.LanguageId" value="@Model.Localization.LanguageId" />

    <div class="row">
        <div class="page">
            
            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Localization.Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Описание</label>
                <textarea asp-for="Localization.Description" required class="form-control" placeholder="">@Model.Localization.Description</textarea>
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>