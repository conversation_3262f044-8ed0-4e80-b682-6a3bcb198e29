﻿using Triggero.Mobile.Abstractions;
using Triggero.MobileMaui.Services;

namespace Triggero.MobileMaui
{
    public partial class MainPage : ContentPage
    {
        int count = 0;

        public MainPage()
        {
            InitializeComponent();
        }

        private void OnCounterClicked(object? sender, EventArgs e)
        {
            count++;

            if (count == 1)
                CounterBtn.Text = $"Clicked {count} time";
            else
                CounterBtn.Text = $"Clicked {count} times";

            SemanticScreenReader.Announce(CounterBtn.Text);

            // TODO: Test the new platform services (replace old DependencyService calls)
            // Example: ServiceHelper.ToastMessage.ShortAlert($"Button clicked {count} times!");
            // Example: var screenInfo = $"Screen: {App.PlatformUi.Screen.WidthDip}x{App.PlatformUi.Screen.HeightDip}";
        }
    }
}
