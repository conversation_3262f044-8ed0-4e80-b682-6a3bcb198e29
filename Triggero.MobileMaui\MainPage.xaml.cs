﻿using Triggero.Mobile.Abstractions;
using Triggero.MobileMaui.Services;
using Triggero.MobileMaui.Views;
using DrawnUi.Draw;

namespace Triggero.MobileMaui
{
    public partial class MainPage : ContentPage
    {
        // Sub-views for each section
        public HomeView? SubViewHome { get; set; }
        public LibraryView? SubViewLibrary { get; set; }
        public TestsView? SubViewTestCategories { get; set; }
        public ChatBotView? SubViewChatBot { get; set; }

        private bool _maybeExit;

        public MainPage()
        {
            InitializeComponent();

            // Initialize with HomeView
            LoadHomeView();
            Footer.IsMainPageSelected = true;
        }

        protected override bool OnBackButtonPressed()
        {
            // Handle Android back button behavior
            if (Navigation.NavigationStack.Count > 1 || DeviceInfo.Platform != DevicePlatform.Android)
            {
                return false; // Let system handle
            }

            if (GoBack()) // Has views on top
            {
                return true;
            }

            if (_maybeExit)
            {
                return false; // Exit app
            }

            ServiceHelper.ToastMessage.ShortAlert("Нажмите еще раз для выхода из программы");
            _maybeExit = true;

            // Reset exit flag after 2 seconds
            Dispatcher.StartTimer(TimeSpan.FromSeconds(2), () =>
            {
                _maybeExit = false;
                return false;
            });

            return true; // Prevent system back handling
        }

        private bool GoBack()
        {
            // TODO: Implement view stack navigation if needed
            return false;
        }

        private void LoadHomeView()
        {
            if (SubViewHome == null)
            {
                SubViewHome = new HomeView();
                MainViewGrid.Children.Add(SubViewHome);
                SubViewHome.Render();
            }
        }

        private void LoadLibraryView()
        {
            if (SubViewLibrary == null)
            {
                SubViewLibrary = new LibraryView();
                LibraryGrid.Children.Add(SubViewLibrary);
                SubViewLibrary.Render();
            }
        }

        private void LoadTestsView()
        {
            if (SubViewTestCategories == null)
            {
                SubViewTestCategories = new TestsView();
                TestsGrid.Children.Add(SubViewTestCategories);
                SubViewTestCategories.Render();
            }
        }

        private void LoadChatBotView()
        {
            if (SubViewChatBot == null)
            {
                SubViewChatBot = new ChatBotView();
                ChatBotGrid.Children.Add(SubViewChatBot);
                SubViewChatBot.Render();
            }
        }

        public void SetView(object view)
        {
            // Hide all views
            MainViewGrid.IsVisible = false;
            LibraryGrid.IsVisible = false;
            TestsGrid.IsVisible = false;
            ChatBotGrid.IsVisible = false;

            // Show the selected view
            switch (view)
            {
                case HomeView:
                    LoadHomeView();
                    MainViewGrid.IsVisible = true;
                    break;
                case LibraryView:
                    LoadLibraryView();
                    LibraryGrid.IsVisible = true;
                    break;
                case TestsView:
                    LoadTestsView();
                    TestsGrid.IsVisible = true;
                    break;
                case ChatBotView:
                    LoadChatBotView();
                    ChatBotGrid.IsVisible = true;
                    break;
            }
        }

        protected override void OnAppearing()
        {
            base.OnAppearing();

            // Apply theme when page appears
            try
            {
                ServiceHelper.PlatformUi.ApplyTheme();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Theme application error: {ex.Message}");
            }
        }
    }
}
