using Triggero.Mobile.Abstractions;
using Triggero.MobileMaui.Services;
using DrawnUi.Draw;

namespace Triggero.MobileMaui
{
    public partial class MainPage : ContentPage
    {
        int count = 0;

        public MainPage()
        {
            InitializeComponent();
        }

        private void OnCounterClicked(object? sender, EventArgs e)
        {
            count++;

            if (count == 1)
                CounterBtn.Text = $"Clicked {count} time";
            else
                CounterBtn.Text = $"Clicked {count} times";

            SemanticScreenReader.Announce(CounterBtn.Text);

            // Test platform services - migrated from DependencyService
            try
            {
                ServiceHelper.ToastMessage.ShortAlert($"Button clicked {count} times!");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Toast error: {ex.Message}");
            }
        }

        private void OnDrawnUiTestClicked(object sender, SkiaGesturesParameters e)
        {
            try
            {
                // Test platform services through ServiceHelper (replaces DependencyService)
                var platformUi = ServiceHelper.PlatformUi;
                var screen = platformUi.Screen;

                var platformInfo = $"Platform: {DeviceInfo.Platform}\n" +
                                 $"Screen: {screen.WidthDip}x{screen.HeightDip}\n" +
                                 $"Density: {screen.Density:F2}\n" +
                                 $"Safe Area Top: {screen.TopInset}";

                PlatformInfoLabel.Text = platformInfo;

                // Test toast message service
                ServiceHelper.ToastMessage.ShortAlert("✅ Platform services working!");

                System.Diagnostics.Debug.WriteLine($"[DrawnUI Test] Platform services test completed successfully");
            }
            catch (Exception ex)
            {
                PlatformInfoLabel.Text = $"❌ Error: {ex.Message}";
                System.Diagnostics.Debug.WriteLine($"[DrawnUI Test] Error: {ex.Message}");
            }
        }
    }
}
