﻿@using Triggero.Models
@using Triggero.Models.General
@{
    ViewData["Title"] = "ВИДЕО ФОНЫ";
}
@model List<VideoBg>
<!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                    <th scope="col">ID</th>
                    <th scope="col">Название</th>
                    <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model)
                    {
                         <tr>
                            <th scope="row">#@item.Id</th>
                            <td>@item.Title</td>
                            <td>
                                <div class="interect">
                                    <a asp-action="UpdateVideoBG" asp-controller="VideoBg" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    <a asp-action="DeleteVideoBG" asp-controller="VideoBg" asp-route-id="@item.Id" class="delete"><i class="fa-solid fa-trash"></i></a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

            <div class="mb-3 position-right">
                <a asp-action="CreateVideoBG" asp-controller="VideoBg" class="button-classic">Добавить</a>
            </div>

            <script>
                $(document).ready( function () {
                    $('#tablemanager').DataTable();
                } );
            </script>
        </div>
    </div>
</div>