﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Localization
@using Triggero.Models.Localization.Practices
@using Triggero.Models.Localization.Tests;
@using Triggero.Models.Localization.Tests.QuestionOptions;
@using Triggero.Web.ViewModels.Localization
@{
    ViewData["Title"] = "РЕДАКТИРОВАНИЕ ПЕРЕВОДА ДЛЯ ВАРИАНТА ОТВЕТА";
    ViewData["ShowLang"] = true;
}
@model LocalizationVM<SimpleQuestionOptionLocalization>

 <!-- CONTENT -->
<form asp-action="UpdateSimpleOptionLocalizationPOST" asp-controller="TestOptionsLocalization" method="post" enctype="multipart/form-data" class="content">

    <input type="hidden" asp-for="Id" value="@Model.Id" />
    <input type="hidden" asp-for="Localization.Id" value="@Model.Localization.Id" />
    <input type="hidden" asp-for="Localization.LanguageId" value="@Model.Localization.LanguageId" />

    <div class="row">
        <div class="page">          

            <div class="mb-3">
                <label for="" class="formlabel">Текст</label>
                <textarea class="form-control" required asp-for="Localization.Text" rows="3" placeholder=""></textarea>
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>