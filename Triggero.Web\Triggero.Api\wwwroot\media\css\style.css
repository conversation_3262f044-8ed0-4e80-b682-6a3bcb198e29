/* 
    Author     : <PERSON><PERSON>
    Mail       : <<EMAIL>>
    Homepage   : http://www.dragar.net
*/

#fileInput{
  width:0;
  height:0;
  overflow:hidden;
}

#modal{
  z-index: 10;
  position: fixed;
  top: 0px;
  left: 0px;
  width: 100%;
  height: 100%;
  background-color: #5F5F5F;
  opacity: 0.95;
  display: none;
}

#preview{
  z-index: 11;
  position: fixed;
  top: 0px;
  left: 0px;
  display: none;
  border: 4px solid #A5A2A2;
  border-radius: 4px;
  float: left;
  font-size: 0px;
  line-height: 0px;
}

#preview .buttons{
  width: 36px;
  position: absolute;
  bottom:0px;
  right: -44px;
}

#preview .buttons .ok{
  border: 4px solid #F5F5F5;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  line-height: 0px;
  font-size: 0px;
  background-image: url('../images/Ok.png');
  background-repeat: no-repeat;
}
#preview .buttons .ok:hover{
  background-image: url('../images/OkGreen.png');
}

#preview .buttons .cancel{
  margin-bottom: 4px;
  border: 4px solid #F5F5F5;
  border-radius: 4px;
  width: 28px;
  height: 28px;
  line-height: 0px;
  font-size: 0px;
  background-image: url('../images/Cancel.png');
  background-repeat: no-repeat;
}

#preview .buttons .cancel:hover{
  background-image: url('../images/CancelRed.png');
}
