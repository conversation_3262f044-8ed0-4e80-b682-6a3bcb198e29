﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Models.Enums;
using Triggero.Models.Plans;

namespace Triggero.Domain.Controllers.Rest.General.Users
{
    [ApiController]
    [Route("[controller]")]
    public class UserSubscriptionController : ApiController
    {
        public UserSubscriptionController(DatabaseContext db) : base(db)
        {
        }
        [HttpGet, Route("GetSubscriptions")]
        public async Task<List<Plan>> GetSubscriptions()
        {
            return DB.Plans
                .Include(o => o.Localizations)
                .ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
        }
        [HttpGet, Route("GetSubscriptionOptions")]
        public async Task<List<PlanOption>> GetSubscriptionOptions()
        {
            return DB.PlanOptions
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted).ToList();
        }




        [HttpPut, Route("StartTrial")]
        public async Task<bool> StartTrial(int userId)
        {
            var user = DB.Users.Include(o => o.UserSubscription)
                               .FirstOrDefault(o => o.Id == userId);
            if (user.UserSubscription.TrialActivated) return false;


            user.UserSubscription.TrialActivated = true;
            user.UserSubscription.SubscriptionBefore = DateTime.UtcNow.AddDays(7);
            user.UserSubscription.SubscriptionType = SubscriptionType.Trial;

            DB.Users.Update(user);
            await DB.SaveChangesAsync();

            return true;
        }

        [HttpPut, Route("SetInfinite")]
        public async Task SetInfinite(int userId)
        {
            var user = DB.Users.Include(o => o.UserSubscription)
                               .FirstOrDefault(o => o.Id == userId);


            user.UserSubscription.TrialActivated = true;
            user.UserSubscription.SubscriptionBefore = DateTime.MaxValue;
            user.UserSubscription.SubscriptionType = SubscriptionType.None;

            DB.Users.Update(user);
            await DB.SaveChangesAsync();
        }

    }
}
