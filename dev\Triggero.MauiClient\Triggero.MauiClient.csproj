﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net8.0-android;net8.0-ios;net8.0-maccatalyst</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net8.0-windows10.0.19041.0</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net8.0-tizen</TargetFrameworks> -->

		<!-- Note for MacCatalyst:
		The default runtime is maccatalyst-x64, except in Release config, in which case the default is maccatalyst-x64;maccatalyst-arm64.
		When specifying both architectures, use the plural <RuntimeIdentifiers> instead of the singular <RuntimeIdentifier>.
		The Mac App Store will NOT accept apps with ONLY maccatalyst-arm64 indicated;
		either BOTH runtimes must be indicated or ONLY macatalyst-x64. -->
		<!-- For example: <RuntimeIdentifiers>maccatalyst-x64;maccatalyst-arm64</RuntimeIdentifiers> -->

		<OutputType>Exe</OutputType>
		<RootNamespace>Triggero.MauiClient</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>Triggero.MauiClient</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.triggero.mauiclient</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">11.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'maccatalyst'">13.1</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
	</PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
	  <MauiXaml Remove="Views\CellCategoryDrawn.xaml" />
	  <MauiXaml Remove="Views\CellElementDrawn.xaml" />
	  <MauiXaml Remove="Views\CheckBoxFavorite.xaml" />
	  <MauiXaml Remove="Views\DrawnListCategories.xaml" />
	  <MauiXaml Remove="Views\ListCategoriesView.xaml" />
	  <MauiXaml Remove="Views\ListElementsView.xaml" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="Views\CellCategoryDrawn.xaml">
	    <SubType>Designer</SubType>
	    <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
	  </EmbeddedResource>
	  <EmbeddedResource Include="Views\CellElementDrawn.xaml">
	    <SubType>Designer</SubType>
	    <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
	  </EmbeddedResource>
	  <EmbeddedResource Include="Views\CheckBoxFavorite.xaml">
	    <SubType>Designer</SubType>
	    <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
	  </EmbeddedResource>
	  <EmbeddedResource Include="Views\DrawnListCategories.xaml">
	    <SubType>Designer</SubType>
	    <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
	  </EmbeddedResource>
	  <EmbeddedResource Include="Views\ListCategoriesView.xaml">
	    <SubType>Designer</SubType>
	    <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
	  </EmbeddedResource>
	  <EmbeddedResource Include="Views\ListElementsView.xaml">
	    <SubType>Designer</SubType>
	    <Generator>MSBuild:UpdateDesignTimeXaml</Generator>
	  </EmbeddedResource>
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AppoMobi.Maui.DrawnUi" Version="1.2.9.7" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />
		<PackageReference Include="Microsoft.Maui.Controls.Compatibility" Version="$(MauiVersion)" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="8.0.1" />
	</ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\..\Common\Triggero.Common.csproj" />
    <ProjectReference Include="..\..\Triggero.Web\MobileAPIWrapper\MobileAPIWrapper.csproj" />
    <ProjectReference Include="..\..\Triggero.Web\Triggero.ChatBot\Triggero.ChatBot.csproj" />
    <ProjectReference Include="..\..\Triggero.Web\Triggero.Models\Triggero.Models.csproj" />
    <ProjectReference Include="..\..\Triggero.Web\TriggeroWeb.Models\Triggero.Domain.csproj" />
  </ItemGroup>


</Project>
