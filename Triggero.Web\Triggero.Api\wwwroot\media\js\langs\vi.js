/* ===========================================================
 * vi.js
 * Vietnamese translation for Trumbowyg
 * http://alex-d.github.com/Trumbowyg
 * ===========================================================
 * Author : heocoi
 *          Github: https://github.com/heocoi
 */

jQuery.trumbowyg.langs.vi = {
    viewHTML: 'Hiển thị HTML',

    formatting: 'Định dạng',
    p: 'Đoạn',
    blockquote: 'Trích dẫn',
    code: 'Code',
    header: 'Đầu trang',

    bold: 'In đậm',
    italic: 'In nghiêng',
    strikethrough: 'Gạch ngang',
    underline: 'Gạch chân',

    strong: 'In đậm',
    em: 'In nghiêng',
    del: 'Gạch ngang',

    unorderedList: '<PERSON>h sách không thứ tự',
    orderedList: '<PERSON>h sách có thứ tự',

    insertImage: 'Chèn hình ảnh',
    insertVideo: 'Chèn video',
    link: 'Đường dẫn',
    createLink: 'Tạo đường dẫn',
    unlink: 'Hủy đường dẫn',

    justifyLeft: 'Canh lề trái',
    justifyCenter: 'Canh giữa',
    justifyRight: 'Canh lề phải',
    justifyFull: 'Canh đều',

    horizontalRule: 'Thêm đường kẻ ngang',

    fullscreen: 'Toàn màn hình',

    close: 'Đóng',

    submit: 'Đồng ý',
    reset: 'Hủy bỏ',

    required: 'Bắt buộc',
    description: 'Mô tả',
    title: 'Tiêu đề',
    text: 'Nội dung',
    target: 'Đối tượng'
};
