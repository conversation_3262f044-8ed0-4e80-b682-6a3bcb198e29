﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.General;
using Triggero.Models.General.Influence;
using Triggero.Web.Abstractions;

namespace Triggero.Web.Controllers
{
    [Authorize]
    public class NeedToHandleController : AbsController
    {
        public NeedToHandleController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("NeedToHandle")]
        public IActionResult NeedToHandle()
        {
            var posts = DB.NeedToHandle.Where(o => !o.IsDeleted).ToList();
            posts.Reverse();
            return View(@"Views\NeedToHandle\NeedToHandle.cshtml", posts);
        }


        //[Route("CreateNeedToHandle")]
        public IActionResult CreateNeedToHandle()
        {
            return View(@"Views\NeedToHandle\CreateNeedToHandle.cshtml");
        }

        [HttpPost]
        //[Route("CreateNeedToHandlePOST"), HttpPost]
        public async Task<IActionResult> CreateNeedToHandlePOST([FromForm] NeedToHandle item)
        {
            DB.NeedToHandle.Add(item);
            await DB.SaveChangesAsync();
            return RedirectToAction("NeedToHandle", "NeedToHandle");
        }


        //[Route("UpdateNeedToHandle")]
        public IActionResult UpdateNeedToHandle(int id)
        {
            var post = DB.NeedToHandle.FirstOrDefault(o => o.Id == id);
            return View(@"Views\NeedToHandle\UpdateNeedToHandle.cshtml", post);
        }

        [HttpPost]
        //[Route("UpdateNeedToHandlePOST"), HttpPost]
        public async Task<IActionResult> UpdateNeedToHandlePOST([FromForm] NeedToHandle item)
        {
            DB.NeedToHandle.Update(item);
            await DB.SaveChangesAsync();
            return RedirectToAction("NeedToHandle", "NeedToHandle");
        }

        [HttpDelete]
        //[Route("DeleteNeedToHandle"), HttpDelete]
        public async Task<IActionResult> DeleteNeedToHandle(int id)
        {
            var item = DB.NeedToHandle.FirstOrDefault(o => o.Id == id);
            item.IsDeleted = true;
            DB.NeedToHandle.Update(item);
            await DB.SaveChangesAsync();
            return RedirectToAction("NeedToHandle", "NeedToHandle");
        }
    }
}
