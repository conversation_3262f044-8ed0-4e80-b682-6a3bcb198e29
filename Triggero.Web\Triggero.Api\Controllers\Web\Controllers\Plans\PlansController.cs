﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.General;
using Triggero.Models.Plans;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Plans;

namespace Triggero.Web.Controllers
{
    [Authorize]
    public class PlansController : AbsController
    {
        public PlansController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Plans")]
        public IActionResult Plans()
        {
            var posts = DB.Plans.Where(o => !o.IsDeleted).ToList();
            posts.Reverse();
            return View(@"Views\Plans\Plans\Plans.cshtml", posts);
        }


        //[Route("CreatePlan")]
        public IActionResult CreatePlan()
        {
            var vm = new CreatePlanVM
            {
                Options = DB.PlanOptions.ToList()
            };
            return View(@"Views\Plans\Plans\CreatePlan.cshtml", vm);
        }

        [HttpPost]
        //[Route("CreatePlanPOST"), HttpPost]
        public async Task<IActionResult> CreatePlanPOST([FromBody] CreatePlanVM vm)
        {
            foreach (var id in vm.SelectedIds)
            {
                var option = DB.PlanOptions.FirstOrDefault(o => o.Id == id);
                vm.NewPlan.PlanOptions.Add(option);
            }


            DB.Plans.Add(vm.NewPlan);
            await DB.SaveChangesAsync();
            return RedirectToAction("Plans", "Plans");
        }

        //[Route("UpdatePlan")]
        public IActionResult UpdatePlan(int id)
        {
            var plan = DB.Plans
                .Include(o => o.PlanOptions)
                .FirstOrDefault(o => o.Id == id);
            var vm = new CreatePlanVM
            {
                Options = DB.PlanOptions.ToList(),
                NewPlan = plan
            };
            return View(@"Views\Plans\Plans\UpdatePlan.cshtml", vm);
        }


        [HttpPost]
        //[Route("ClearPlanIds"), HttpPost]
        public async Task ClearPlanIds(int planId)
        {
            var plan = DB.Plans
                 .Include(o => o.PlanOptions)
                 .FirstOrDefault(o => o.Id == planId);
            plan.PlanOptions.Clear();

            DB.Plans.Update(plan);
            await DB.SaveChangesAsync();
        }

        [HttpPost]
        //[Route("UpdatePlanPOST"), HttpPost]
        public async Task<IActionResult> UpdatePlanPOST([FromBody] CreatePlanVM vm)
        {
            foreach (var id in vm.SelectedIds)
            {
                var option = DB.PlanOptions.FirstOrDefault(o => o.Id == id);
                vm.NewPlan.PlanOptions.Add(option);
            }

            DB.Plans.Update(vm.NewPlan);
            await DB.SaveChangesAsync();
            return RedirectToAction("Plans", "Plans");
        }

        [HttpDelete]
        //[Route("DeletePlan"), HttpDelete]
        public async Task<IActionResult> DeletePlan(int id)
        {
            var plan = DB.Plans.FirstOrDefault(o => o.Id == id);
            plan.IsDeleted = true;
            DB.Plans.Update(plan);
            await DB.SaveChangesAsync();
            return RedirectToAction("Plans", "Plans");
        }
    }
}
