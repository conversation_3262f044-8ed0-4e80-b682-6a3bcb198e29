﻿using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models.General;
using Triggero.Web.Abstractions;

namespace Triggero.Web.Abstractions
{
    public abstract class AbsController : Controller
    {
        public DatabaseContext DB { get; set; }

        public IWebHostEnvironment _hosting { get; set; }

        public AbsController(DatabaseContext db, IWebHostEnvironment appEnv)
        {
            DB = db;

            _hosting = appEnv;
        }

        [NonAction]
        public Language GetCurrentLanguage()
        {
            using (DatabaseContext db = new DatabaseContext())
            {
                Language lang = null;
                try
                {
                    if (HttpContext.Request.Cookies.ContainsKey("Language"))
                    {
                        int id = Convert.ToInt32(HttpContext.Request.Cookies["Language"]);
                        lang = db.Languages.FirstOrDefault(o => o.Id == id);
                    }
                    else
                    {
                        lang = db.Languages.FirstOrDefault();
                    }
                }
                catch { }

                return lang;
            }
        }



        #region Файловая система

        protected string WebRootPath
        {
            get
            {
                if (string.IsNullOrWhiteSpace(_hosting.WebRootPath))
                {
                    _hosting.WebRootPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
                }
                return _hosting.WebRootPath;
            }
        }

        public string GetWWWROOTPath(string filename)
        {
            return Path.Combine(WebRootPath, filename);
        }

        public string GetWWWROOTPath(string folderBefore, string filename)
        {
            return Path.Combine(WebRootPath, folderBefore, filename);
        }

        public string GenerateFileName(string extension)
        {
            return Guid.NewGuid().ToString() + "." + extension;
        }
        public string GenerateFolderName()
        {
            return Guid.NewGuid().ToString();
        }

        #endregion

        /// <summary>
        /// Сохраняем файл на сервер и возвращаем путь к нему
        /// </summary>
        /// <param name="oldFilePath">Используется, если не прикрепили файл в форме и чтобы не затереть старый путь</param>
        /// <returns></returns>
        [NonAction]
        public async Task<string> SetAttachmentIfHas(string oldFilePath, int index = 0)
        {
            var attachment = Request.Form.Files.Skip(index).FirstOrDefault();
            var filePath = "/uploads/" + Guid.NewGuid().ToString();

            if (attachment != null && attachment.Length > 0)
            {
                string path = filePath + attachment.FileName;
                using (var fileStream = new FileStream(WebRootPath + path, FileMode.Create))
                {
                    await attachment.CopyToAsync(fileStream);
                }
                return path;
            }
            return oldFilePath;
        }

        [NonAction]
        public async Task<string> SetAttachmentIfHas(string oldFilePath, string inputFileName)
        {
            var attachment = Request.Form.Files.FirstOrDefault(o => o.Name == inputFileName);
            var filePath = "/uploads/" + Guid.NewGuid().ToString();

            if (attachment != null && attachment.Length > 0)
            {
                string path = filePath + attachment.FileName;
                using (var fileStream = new FileStream(WebRootPath + path, FileMode.Create))
                {
                    await attachment.CopyToAsync(fileStream);
                }
                return path;
            }
            return oldFilePath;
        }
    }
}
