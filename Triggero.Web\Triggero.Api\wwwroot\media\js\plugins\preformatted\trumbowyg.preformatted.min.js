/* ===========================================================
 * trumbowyg.preformatted.js v1.0
 * Preformatted plugin for Trumbowyg
 * http://alex-d.github.com/Trumbowyg
 * ===========================================================
 * Author : <PERSON><PERSON> (Civile)
 */
!function(e){"use strict";e.extend(!0,e.trumbowyg,{langs:{en:{preformatted:"Code sample <pre>"},sl:{preformatted:"Vstavi neformatiran tekst <pre>"},by:{preformatted:"Прыклад кода <pre>"},da:{preformatted:"Præformateret <pre>"},et:{preformatted:"Eelvormindatud tekst <pre>"},fr:{preformatted:"Exemple de code <pre>"},hu:{preformatted:"Kód minta <pre>"},it:{preformatted:"Codice <pre>"},ja:{preformatted:"コードサンプル <pre>"},ko:{preformatted:"코드 예제 <pre>"},pt_br:{preformatted:"Exemple de código <pre>"},ru:{preformatted:"Пример кода <pre>"},tr:{preformatted:"Kod örneği <pre>"},zh_cn:{preformatted:"代码示例 <pre>"},zh_tw:{preformatted:"代碼範例 <pre>"}},plugins:{preformatted:{init:function(t){var r={fn:function(){t.saveRange();var r,n,o=t.getRangeText();if(""!==o.replace(/\s/g,""))try{var a=function(){var e,t=null;window.getSelection?(e=window.getSelection()).rangeCount&&1!==(t=e.getRangeAt(0).commonAncestorContainer).nodeType&&(t=t.parentNode):(e=document.selection)&&"Control"!==e.type&&(t=e.createRange().parentElement());return t}().tagName.toLowerCase();if("code"===a||"pre"===a)return function(){var t=null;if(document.selection)t=document.selection.createRange().parentElement();else{var r=window.getSelection();r.rangeCount>0&&(t=r.getRangeAt(0).startContainer.parentNode)}var n=e(t).contents().closest("pre").length,o=e(t).contents().closest("code").length;n&&o?e(t).contents().unwrap("code").unwrap("pre"):n?e(t).contents().unwrap("pre"):o&&e(t).contents().unwrap("code")}();t.execCmd("insertHTML","<pre><code>"+(r=o,(n=document.createElement("DIV")).innerHTML=r,(n.textContent||n.innerText||"")+"</code></pre>"))}catch(e){}},tag:"pre"};t.addBtnDef("preformatted",r)}}}})}(jQuery);