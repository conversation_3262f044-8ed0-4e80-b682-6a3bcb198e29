﻿namespace Triggero.Common.Helpers;

public static class PhoneConverter
{
    //private static Char[] phoneSymbols = new Char[] { '+', '(', ')', ' ', '-' };

    //public static string ConvertToNumberFormat(string phoneNumber)
    //{
    //    if (String.IsNullOrEmpty(phoneNumber))
    //    {
    //        return null;
    //    }
    //    string result = phoneNumber.Trim();
    //    foreach (var symbol in phoneSymbols)
    //    {
    //        result = result.Replace(symbol.ToString(), string.Empty);
    //    }
    //    return result;
    //}

    public static string ImportPhoneNumber(string input, bool badAsNull = false)
    {
        string cleanedInput = null;
        if (!string.IsNullOrEmpty(input))
        {
            // Step 1: Remove all characters except digits
            cleanedInput = string.Concat(input.Where(char.IsDigit));

            // Step 2: Check if cleaned input starts with "37"
            if (cleanedInput.StartsWith("37"))
            {
                if (cleanedInput.Length == 12) return cleanedInput;
                return badAsNull ? null : cleanedInput;
            }

            // Step 3: If cleaned input starts with "8", replace it with "7"
            if (cleanedInput.StartsWith("8"))
            {
                cleanedInput = "7" + cleanedInput.Substring(1);
            }

            // Step 4: If cleaned input starts with "7", check if length is 11
            if (cleanedInput.StartsWith("7"))
            {
                if (cleanedInput.Length == 11) return cleanedInput;
                return badAsNull ? null : cleanedInput;
            }

        }

        // Return null if none of the above conditions are met
        return badAsNull ? null : cleanedInput;
    }


    public static string DisplayPhoneNumber(string cleanedInput)
    {
        if (string.IsNullOrEmpty(cleanedInput))
            return cleanedInput;

        //if (cleanedInput.StartsWith("37") && cleanedInput.Length == 12)
        //{
        //    return $"+{cleanedInput.Substring(0, 3)}{cleanedInput.Substring(3, 2)}-{cleanedInput.Substring(5, 3)}-{cleanedInput.Substring(8, 2)}-{cleanedInput.Substring(10, 2)}";
        //}
        if (cleanedInput.StartsWith("37") && cleanedInput.Length == 12)
        {
            return $"+{cleanedInput.Substring(0, 3)}({cleanedInput.Substring(3, 2)}){cleanedInput.Substring(5, 3)}-{cleanedInput.Substring(8, 2)}-{cleanedInput.Substring(10, 2)}";
        }
        else if (cleanedInput.StartsWith("7") && cleanedInput.Length == 11)
        {
            return $"+{cleanedInput[0]} ({cleanedInput.Substring(1, 3)}) {cleanedInput.Substring(4, 3)}-{cleanedInput.Substring(7, 2)}-{cleanedInput.Substring(9, 2)}";
        }

        return cleanedInput;
    }



}