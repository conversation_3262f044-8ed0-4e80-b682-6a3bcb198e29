/* ===========================================================
 * trumbowyg.allowTagsFromPaste.js v1.0.2
 * It cleans tags from pasted text, whilst allowing several specified tags
 * http://alex-d.github.com/Trumbowyg
 * ===========================================================
 * Author	: Fathi Anshory (0x00000F5C)
 * Twitter	: @fscchannl
 * Notes:
 *  - removeformatPasted must be set to FALSE since it was applied prior to pasteHandlers, or else it will be useless
 *	- It is most advisable to use along with the cleanpaste plugin, or else you'd end up with dirty markup
 */
!function(e){"use strict";var a={allowedTags:[],removableTags:["a","abbr","address","b","bdi","bdo","blockquote","br","cite","code","del","dfn","details","em","h1","h2","h3","h4","h5","h6","hr","i","ins","kbd","mark","meter","pre","progress","q","rp","rt","ruby","s","samp","small","span","strong","sub","summary","sup","time","u","var","wbr","img","map","area","canvas","figcaption","figure","picture","audio","source","track","video","ul","ol","li","dl","dt","dd","table","caption","th","tr","td","thead","tbody","tfoot","col","colgroup","style","div","p","form","input","textarea","button","select","optgroup","option","label","fieldset","legend","datalist","keygen","output","iframe","link","nav","header","hgroup","footer","main","section","article","aside","dialog","script","noscript","embed","object","param"]};e.extend(!0,e.trumbowyg,{plugins:{allowTagsFromPaste:{init:function(t){if(t.o.plugins.allowTagsFromPaste){t.o.removeformatPasted=!1;var o=t.o.plugins.allowTagsFromPaste.allowedTags||a.allowedTags,r=t.o.plugins.allowTagsFromPaste.removableTags||a.removableTags;if(0!==o.length){var s=e(r).not(o).get();t.pasteHandlers.push((function(){setTimeout((function(){var a=t.$ed.html();e.each(s,(function(e,t){a=a.replace(new RegExp("<\\/?"+t+"(\\s[^>]*)?>","gi"),"")})),t.$ed.html(a)}),0)}))}}}}}})}(jQuery);