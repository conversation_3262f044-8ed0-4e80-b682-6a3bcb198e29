# Triggero MAUI Migration - AI Assistant Prompt

## Context Setup
You are continuing the Triggero Xamarin.Forms to .NET MAUI migration project. This is a structured, multi-phase migration with specific technical requirements and established patterns.

## Essential Reading (REQUIRED)
Before starting any work, you MUST read these files in order:

1. **MIGRATION_OVERVIEW.md** - Complete project context, phases, and strategy
2. **PLATFORM_SERVICES_GUIDE.md** - Service architecture and implementation patterns  
3. **DRAWNUI_MIGRATION_GUIDE.md** - UI framework migration approach
4. **TECHNICAL_DECISIONS_AND_PATTERNS.md** - All coding standards and decisions

## Quick Context Gathering
After reading the documentation, use these tools to understand current state:

```
1. Use codebase-retrieval tool to understand current codebase state:
   "What is the current status of the Triggero MAUI migration project? Show me the project structure, completed implementations, and any pending work."

2. Check task management status:
   view_tasklist

3. Verify build status by running:
   dotnet build -f net9.0-windows10.0.19041.0 --configuration Debug
```

## Critical Technical Requirements

### Platform Strategy
- **Primary Development**: Windows (fast iteration, hot reload, 50% faster compilation)
- **Production Targets**: Android, iOS
- **EXCLUDED**: MacCatalyst (explicitly not supported)
- **Development Workflow**: Windows-first → Cross-platform testing → Mobile validation

### Migration Patterns (NON-NEGOTIABLE)
1. **NEVER CREATE CUSTOM CONTROLS FROM SCRATCH**: Always copy existing Xamarin UI files as-is, then replace namespaces from Xamarin to MAUI and edit for compatibility. DO NOT throw away working Xamarin code to create custom implementations.
2. **USE TERMINAL COPY COMMANDS**: Always use `copy` command to copy Xamarin files to MAUI project, then edit them. This avoids file creation errors.
3. **PROPER PORTING WORKFLOW**: Terminal copy Xamarin files → Replace namespaces → Update syntax → Test build
3. **TODO Comments**: Always add when omitting/replacing Xamarin code
4. **Preservation Strategy**: Copy uncompilable Xamarin code with comments rather than silent omission
5. **Phone Only**: Port Phone screens, exclude all Tablet implementations
6. **Package Managers**: Always use `dotnet add package`, never edit .csproj manually
7. **Color Conversion**: Use `.ToPlatform()` extension, not Xamarin `.ToAndroid()/.ToiOS()`
8. **Namespace Conflicts**: Use `global::` prefix for platform types

### Service Architecture
- **Pattern**: Xamarin DependencyService → MAUI Dependency Injection
- **Registration**: Conditional compilation (`#if ANDROID`, `#if IOS`, `#if WINDOWS`)
- **Access**: ServiceHelper class for DependencyService-like compatibility
- **Integration**: Services registered in MauiProgram.cs with `.AddPlatformServices()`

### Build Testing Commands (CRITICAL)
**NEVER BUILD WITHOUT --configuration Debug FLAG!**

```bash
# Windows development (primary) - ALWAYS USE THIS
dotnet build -f net9.0-windows10.0.19041.0 --configuration Debug

# Android verification
dotnet build -f net9.0-android --configuration Debug

# iOS validation
dotnet build -f net9.0-ios --configuration Debug
```

## Current Project Status
**Phase 1 Foundation**: Platform Services ✅ COMPLETED, DrawnUI Integration PENDING

### Completed Work
- ✅ Platform Services (IPlatformUi, IToastMessage) for Windows, Android, iOS
- ✅ MAUI Dependency Injection setup
- ✅ DrawnUI package installation (AppoMobi.Maui.DrawnUi v1.5.1.4)
- ✅ Cross-platform build verification
- ✅ Windows-first development workflow established

### Key Files Implemented
- `Abstractions/IPlatformUi.cs` & `IToastMessage.cs` - Service interfaces
- `Platforms/Windows/PlatformUi.cs` & `ToastMessage.cs` - Windows implementations
- `Platforms/Android/PlatformUi.cs` & `ToastMessage.cs` - Android implementations  
- `Platforms/iOS/PlatformUi.cs` & `ToastMessage.cs` - iOS implementations
- `Services/PlatformServicesExtensions.cs` - Service registration
- `MauiProgram.cs` - Updated with platform services
- `App.xaml.cs` - Compatibility properties

## Immediate Next Steps
Based on task list and current status:

1. **Setup DrawnUI MAUI Integration** - Add `.UseDrawnUi()` to MauiProgram.cs
2. **Migrate Core Dependencies** - Replace remaining DependencyService calls
3. **Continue with Phase 2** - UI Framework Migration

## Working Approach

### Information Gathering
```
1. Use codebase-retrieval for detailed code analysis:
   "Show me all DependencyService.Get<T>() calls that need migration"
   "What DrawnUI components are currently used in the Xamarin project?"
   "Show me the current MauiProgram.cs configuration"

2. Before making changes, always gather context:
   "Show me the specific code I need to modify for [task]"
```

### Making Changes
1. **Always use codebase-retrieval** before editing to understand current implementation
2. **Use str-replace-editor** for modifications, never recreate files
3. **Test builds regularly** with Windows target first
4. **Update task status** using task management tools
5. **Follow established patterns** from documentation

### Code Examples
```csharp
// Service Registration Pattern
#if ANDROID
    builder.Services.AddSingleton<IService, Platforms.Android.Service>();
#elif IOS  
    builder.Services.AddSingleton<IService, Platforms.iOS.Service>();
#elif WINDOWS
    builder.Services.AddSingleton<IService, Platforms.Windows.Service>();
#endif

// TODO Comment Pattern
// TODO: MAUI Migration - Replace Xamarin dependency
// Original: DependencyService.Get<IService>()
var service = ServiceHelper.ServiceName;

// Color Conversion Pattern
Activity.Window.SetNavigationBarColor(mauiColor.ToPlatform());

// Namespace Resolution Pattern
var context = global::Android.App.Application.Context;
```

## Task Management
- Use task management tools for complex work
- Update task states: NOT_STARTED → IN_PROGRESS → COMPLETE
- Mark previous task complete when starting new task
- Use batch updates for efficiency

## Quality Assurance
- **Windows builds must always succeed** (primary development platform)
- **Cross-platform builds** for verification
- **TODO comments** for all omitted functionality
- **Documentation updates** for significant changes
- **Test on target platforms** after Windows stability

## Emergency Recovery
If you encounter issues:
1. **Check build status** 
2. **Review recent changes** using diagnostics tool
3. **Consult documentation** for established patterns
4. **Use codebase-retrieval** for understanding current state
5. **Ask user for guidance** if stuck

## Success Criteria
- Windows development workflow maintained
- Cross-platform builds successful
- Migration phases progressing systematically
- Code quality and patterns consistent
- Documentation kept current

Remember: This is a production migration project. Maintain high code quality, follow established patterns, and prioritize the Windows-first development workflow for maximum efficiency.

Update existing documentation when you receive new general requirements that are not reflected there or contradict to it.

## CRITICAL MISTAKE TO NEVER REPEAT

### FooterNavigation Implementation Error
**What Happened**: AI created custom FooterNavigation controls from scratch instead of properly porting the existing working Xamarin Footer.xaml/Footer.xaml.cs files.

**User Feedback**: "i dont understand whats going on here. we had a working code in xamarin. you are throwing it to bin or what? why dont you just copy all the existing xamarin UI files as-is, the replace namespaces from xamarin to maui and edit them so they work in maui. wht you create some custom cotrols from strach, what the fuck i dont understand here?"

**Correct Approach - USE TERMINAL COPY COMMANDS**:
1. Use terminal to copy BOTH files with wildcard: `copy "..\TriggeroV2\TriggeroV2\TriggeroV2\Controls\Parts\Footer.xaml*" "Views\"`
2. Rename files: `ren "Views\Footer.xaml" "FooterNavigation.xaml"` and `ren "Views\Footer.xaml.cs" "FooterNavigation.xaml.cs"`
3. Edit copied XAML: Replace `xmlns="http://xamarin.com/schemas/2014/forms"` with `xmlns="http://schemas.microsoft.com/dotnet/2021/maui"`
4. Edit copied C#: Update using statements, class name, and inheritance for MAUI
5. Test build with Debug configuration

**CRITICAL**: ALWAYS use `*.xaml*` wildcard to copy BOTH .xaml AND .xaml.cs files together!
**WHY TERMINAL COPY**: Avoids file creation errors that occur with save-file tool. Always use terminal copy commands for porting Xamarin files.

**NEVER**: Create custom implementations when working Xamarin code exists
**ALWAYS**: Port existing code structure and preserve proven implementations