/* ===========================================================
 * trumbowyg.noembed.js v1.0
 * noEmbed plugin for Trumbowyg
 * http://alex-d.github.com/Trumbowyg
 * ===========================================================
 * Author : <PERSON> (jake<PERSON>hns)
 */
!function(e){"use strict";var r={proxy:"https://noembed.com/embed?nowrap=on",urlFiled:"url",data:[],success:void 0,error:void 0};e.extend(!0,e.trumbowyg,{langs:{en:{noembed:"Noembed",noembedError:"Error"},sl:{noembed:"Noembed",noembedError:"Napaka"},by:{noembedError:"Памылка"},cs:{noembedError:"Chyba"},da:{noembedError:"Fejl"},et:{noembed:"Noembed",noembedError:"Viga"},fr:{noembedError:"Erreur"},hu:{noembed:"Noembed",noembedError:"Hiba"},ja:{noembedError:"エラー"},ko:{noembed:"oEmbed 넣기",noembedError:"에러"},pt_br:{noembed:"Incorporar",noembedError:"Erro"},ru:{noembedError:"Ошибка"},sk:{noembedError:"Chyba"},tr:{noembedError:"Hata"},zh_tw:{noembed:"插入影片",noembedError:"錯誤"}},plugins:{noembed:{init:function(o){o.o.plugins.noembed=e.extend(!0,{},r,o.o.plugins.noembed||{});var n={fn:function(){var r=o.openModalInsert(o.lang.noembed,{url:{label:"URL",required:!0}},(function(n){e.ajax({url:o.o.plugins.noembed.proxy,type:"GET",data:n,cache:!1,dataType:"json",success:function(n){o.o.plugins.noembed.success?o.o.plugins.noembed.success(n,o,r):n.html?(o.execCmd("insertHTML",n.html),setTimeout((function(){o.closeModal()}),250)):o.addErrorOnModalField(e("input[type=text]",r),n.error)},error:o.o.plugins.noembed.error||function(){o.addErrorOnModalField(e("input[type=text]",r),o.lang.noembedError)}})}))}};o.addBtnDef("noembed",n)}}}})}(jQuery);