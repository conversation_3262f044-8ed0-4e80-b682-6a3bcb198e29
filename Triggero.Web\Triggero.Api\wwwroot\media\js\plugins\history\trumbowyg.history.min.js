/*/* ===========================================================
 * trumbowyg.history.js v1.0
 * history plugin for Trumbowyg
 * http://alex-d.github.com/Trumbowyg
 * ===========================================================
 * Author : <PERSON> [dune<PERSON>@forelabs.eu]
 */
!function(o){"use strict";o.extend(!0,o.trumbowyg,{langs:{en:{history:{redo:"Redo",undo:"Undo"}},sl:{history:{redo:"Ponovno uveljavi",undo:"Razveljavi"}},by:{history:{redo:"Паўтарыць",undo:"Скасаваць"}},da:{history:{redo:"<PERSON><PERSON><PERSON> fortryd",undo:"Fortryd"}},de:{history:{redo:"Wiederholen",undo:"Rückgängig"}},et:{history:{redo:"Võta tagasi",undo:"Tee uuesti"}},fr:{history:{redo:"Annuler",undo:"Rétablir"}},hu:{history:{redo:"Visszállít",undo:"Visszavon"}},ko:{history:{redo:"다시 실행",undo:"되돌리기"}},pt_br:{history:{redo:"Refazer",undo:"Desfazer"}},ru:{history:{redo:"Повторить",undo:"Отменить"}},tr:{history:{redo:"Geri al",undo:"Yinele"}},zh_tw:{history:{redo:"重做",undo:"復原"}}},plugins:{history:{destroy:function(o){o.$c.off("tbwinit.history tbwchange.history")},init:function(i){i.o.plugins.history=o.extend(!0,{_stack:[],_index:-1,_focusEl:void 0},i.o.plugins.history||{});var t={title:i.lang.history.redo,ico:"redo",key:"Y",fn:function(){if(i.o.plugins.history._index<i.o.plugins.history._stack.length-1){i.o.plugins.history._index+=1;var o=i.o.plugins.history._index,t=i.o.plugins.history._stack[o];i.execCmd("html",t),i.o.plugins.history._stack[o]=i.$ed.html(),d(),e()}}},n={title:i.lang.history.undo,ico:"undo",key:"Z",fn:function(){if(i.o.plugins.history._index>0){i.o.plugins.history._index-=1;var o=i.o.plugins.history._index,t=i.o.plugins.history._stack[o];i.execCmd("html",t),i.o.plugins.history._stack[o]=i.$ed.html(),d(),e()}}},e=function(){var o=i.o.plugins.history._index,t=i.o.plugins.history._stack.length,n=0!==t&&o!==t-1;s("historyUndo",o>0),s("historyRedo",n)},s=function(o,t){var n=i.$box.find(".trumbowyg-"+o+"-button");t?n.removeClass("trumbowyg-disable"):n.hasClass("trumbowyg-disable")||n.addClass("trumbowyg-disable")},r=function(o,i){if(o===i)return!0;if(null==o||null==i)return!1;if(o.length!==i.length)return!1;for(var t=0;t<o.length;t+=1)if(o[t]!==i[t])return!1;return!0},d=function(){var o=i.doc.getSelection().focusNode,t=i.doc.createRange();o.childNodes.length>0&&(t.setStartAfter(o.childNodes[o.childNodes.length-1]),t.setEndAfter(o.childNodes[o.childNodes.length-1]),i.doc.getSelection().removeAllRanges(),i.doc.getSelection().addRange(t))};i.$c.on("tbwinit.history tbwchange.history",(function(){var t,n,s=i.o.plugins.history._index,d=i.o.plugins.history._stack,l=d.slice(-1)[0]||"<p></p>",u=d[s],h=i.$ed.html(),y=i.doc.getSelection().focusNode,a="",c=i.o.plugins.history._focusEl;t=o("<div>"+l+"</div>").find("*").map((function(){return this.localName})),n=o("<div>"+h+"</div>").find("*").map((function(){return this.localName})),y&&(i.o.plugins.history._focusEl=y,a=y.outerHTML||y.textContent),h!==u&&(a.slice(-1).match(/\s/)||!r(t,n)||i.o.plugins.history._index<=0||y!==c?(i.o.plugins.history._index+=1,i.o.plugins.history._stack=d.slice(0,i.o.plugins.history._index),i.o.plugins.history._stack.push(h)):i.o.plugins.history._stack[s]=h,e())})),i.addBtnDef("historyRedo",t),i.addBtnDef("historyUndo",n)}}}})}(jQuery);