﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Enums;
using Newtonsoft.Json;
using Newtonsoft.Json.Converters;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Models.Enums;
using Triggero.Models.Messengers.ChatBot;

namespace Triggero.Domain.Controllers.Rest.Messengers
{
    [ApiController]
    [Route("[controller]")]
    public class ChatBotMessengerController : ApiController
    {
        public ChatBotMessengerController(DatabaseContext db) : base(db)
        {
        }

        [HttpGet, Route("GetChat")]
        public async Task<string> GetChat(int userId)
        {
            try
            {
                var user = DB.Users.Include(o => o.ChatBotChat).ThenInclude(o => o.Messages).ThenInclude(o => o.Attachments)
                              .FirstOrDefault(o => o.Id == userId);
                // return new ChatBotChat { Messages = new List<ChatBotMessage> { new ChatBotMessage { Text = user.ToString() } } };


                var chat = user.ChatBotChat;
                chat.User.ChatBotChat = null;

                return MakeNewtonsoftJsonString(chat);

            }
            catch (Exception ex)
            {
                var chat = new ChatBotChat { Messages = new List<ChatBotMessage> { new ChatBotMessage { Text = ex.ToString() } } };
                return MakeNewtonsoftJsonString(chat);
            }
        }




        [HttpPut, Route("SendMessage")]
        public async Task<string> SendMessage(int userId, string newtonJsonEncoded)
        {
            try
            {
                var user = DB.Users.Include(o => o.ChatBotChat)
                               .FirstOrDefault(o => o.Id == userId);

                //    var str = JsonConvert.DeserializeObject<string>(newtonJsonEncoded);
                var message = JsonConvert.DeserializeObject<ChatBotMessage>(newtonJsonEncoded, new IsoDateTimeConverter());
                message.SentAt = DateTime.UtcNow;

                user.ChatBotChat.Messages.Add(message);
                DB.Users.Update(user);
                await DB.SaveChangesAsync();

                return "Хуй";
            }
            catch (Exception ex)
            {
                return ex.ToString();
            }
        }


        [HttpPut, Route("SendMessages")]
        public async Task<string> SendMessages(int userId, string newtonJsonEncoded)
        {
            try
            {
                var user = DB.Users.Include(o => o.ChatBotChat)
                               .FirstOrDefault(o => o.Id == userId);

                //   var str = JsonConvert.DeserializeObject<string>(newtonJsonEncoded);
                var messages = JsonConvert.DeserializeObject<List<ChatBotMessage>>(newtonJsonEncoded, new IsoDateTimeConverter());
                foreach (var msg in messages)
                {
                    msg.SentAt = DateTime.UtcNow;
                }


                user.ChatBotChat.Messages.AddRange(messages);
                DB.Users.Update(user);
                await DB.SaveChangesAsync();

                return "Хуй";
            }
            catch (Exception ex)
            {
                return ex.ToString();
            }

        }




        [HttpGet, Route("LongPolling")]
        public async Task<ChatBotLongPollItem> LongPolling(int userId)
        {
            var result = new ChatBotLongPollItem();
            await Task.Run(async () =>
            {
                var user = DB.Users.Include(o => o.ChatBotChat).ThenInclude(o => o.Messages).ThenInclude(o => o.Attachments)
                               .FirstOrDefault(o => o.Id == userId);
                int count = user.ChatBotChat.Messages.Count;

                for (int i = 0; i < 5; i++)
                {

                    var userV2 = DB.Users.Include(o => o.ChatBotChat).ThenInclude(o => o.Messages).ThenInclude(o => o.Attachments)
                                 .FirstOrDefault(o => o.Id == userId);
                    int countV2 = userV2.ChatBotChat.Messages.Count;

                    if (countV2 > count)
                    {
                        result = new ChatBotLongPollItem
                        {
                            Type = ChatBotLongPollType.NewMessage,
                            Messages = userV2.ChatBotChat.Messages.TakeLast(countV2 - count).ToList()
                        };
                    }

                    await Task.Delay(3000);
                }
            });
            return result;
        }

        [HttpGet, Route("LongPollingV2")]
        public async Task<ChatBotLongPollItem> LongPollingV2(int userId, int startMessagesCount)
        {
            var result = new ChatBotLongPollItem()
            {
                TotalMessagesCount = startMessagesCount
            };

            //await Task.Run(async () =>
            //{
            //    for (int i = 0; i < 5; i++)
            //    {


            //        await Task.Delay(3000);
            //    }
            //});

            var userV2 = DB.Users.Include(o => o.ChatBotChat).ThenInclude(o => o.Messages).ThenInclude(o => o.Attachments)
                                .FirstOrDefault(o => o.Id == userId);
            int countV2 = userV2.ChatBotChat.Messages.Count;

            if (countV2 > startMessagesCount)
            {
                result = new ChatBotLongPollItem
                {
                    TotalMessagesCount = countV2,
                    Type = ChatBotLongPollType.NewMessage,
                    Messages = userV2.ChatBotChat.Messages.TakeLast(countV2 - startMessagesCount).ToList()
                };
            }


            return result;
        }



        [HttpPut, Route("SetSeverity")]
        public async Task SetSeverity(int userId, ChatBotChatSeverity severity)
        {
            var user = DB.Users.Include(o => o.ChatBotChat)
                            .FirstOrDefault(o => o.Id == userId);

            user.ChatBotChat.Severity = severity;

            DB.Users.Update(user);
            await DB.SaveChangesAsync();
        }

    }
}
