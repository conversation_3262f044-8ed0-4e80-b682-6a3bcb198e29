﻿using HtmlAgilityPack;
using System.Xml;

namespace Triggero.Web.Extensions
{
    public static class StringExtensions
    {
        public static string SafeSubstring(this string str,int start,int length)
        {
            var hui = ExtractText(str);
            if (hui.Length >=  start + length)
            {
                return hui.Substring(start, length);
            }
            return hui;
        }
        public static string SafeSubstringWithStr(this string str, int start, int length,string withStr)
        {
            var hui = ExtractText(str);
            if (hui.Length >= start + length)
            {
                return hui.Substring(start, length) + withStr;
            }
            return hui;
        }

        public static string ExtractText(string html)
        {
            if (html == null)
            {
                throw new ArgumentNullException("html");
            }

            HtmlDocument doc = new HtmlDocument();
            doc.LoadHtml(html);

            var chunks = new List<string>();

            foreach (var item in doc.DocumentNode.DescendantNodesAndSelf())
            {
                if (item.NodeType == HtmlNodeType.Text)
                {
                    if (item.InnerText.Trim() != "")
                    {
                        chunks.Add(item.InnerText.Trim());
                    }
                }
            }
            return String.Join(" ", chunks);
        }
    }
}
