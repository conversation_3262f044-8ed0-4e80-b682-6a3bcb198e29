﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.General;
using Triggero.Web.Abstractions;

namespace Triggero.Web.Controllers
{
    [Authorize]
    public class PostsController : AbsController
    {
        public PostsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Posts")]
        public IActionResult Posts()
        {
            var posts = DB.Posts.Where(o => !o.IsDeleted).ToList();
            posts.Reverse();
            return View(@"Views\Posts\Posts.cshtml", posts);
        }


        //[Route("CreatePost")]
        public IActionResult CreatePost()
        {
            return View(@"Views\Posts\CreatePost.cshtml");
        }

        [HttpPost]
        //[Route("CreatePostPOST"), HttpPost]
        public async Task<IActionResult> CreatePostPOST([FromForm] Post post)
        {
            post.ImgPath = await SetAttachmentIfHas(post.ImgPath);
            DB.Posts.Add(post);
            await DB.SaveChangesAsync();
            return RedirectToAction("Posts", "Posts");
        }


        //[Route("UpdatePost")]
        public IActionResult UpdatePost(int id)
        {
            var post = DB.Posts.FirstOrDefault(o => o.Id == id);
            return View(@"Views\Posts\UpdatePost.cshtml", post);
        }

        [HttpPost]
        //[Route("UpdatePostPOST"), HttpPost]
        public async Task<IActionResult> UpdatePostPOST([FromForm] Post post)
        {
            post.ImgPath = await SetAttachmentIfHas(post.ImgPath);
            DB.Posts.Update(post);
            await DB.SaveChangesAsync();
            return RedirectToAction("Posts", "Posts");
        }

        [HttpDelete]
        //[Route("DeletePost"), HttpDelete]
        public async Task<IActionResult> DeletePost(int id)
        {
            var post = DB.Posts.FirstOrDefault(o => o.Id == id);
            post.IsDeleted = true;
            DB.Posts.Update(post);
            await DB.SaveChangesAsync();
            return RedirectToAction("Posts", "Posts");
        }

    }
}
