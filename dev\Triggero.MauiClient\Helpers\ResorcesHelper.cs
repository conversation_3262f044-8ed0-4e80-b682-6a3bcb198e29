﻿using System.IO;
using System.Net.Http;
using System.Threading.Tasks;
using System.Xml;
using Xamarin.Essentials;
using Xamarin.Forms;
using Path = System.IO.Path;

namespace Triggero.MauiClient.Helpers
{
    public static class ResorcesHelper
    {
        [Obsolete]
        public async static Task<ImageSource> GetImageSource(string path)
        {
            if (ConnectionHelper.HasInternet())
            {
                return await DownloadImage(path);
            }
            else
            {
                string filename = Path.GetFileName(path);
                var imageFile = Path.Combine(ApplicationState.DownloadsPath, filename);
                return ImageSource.FromFile(imageFile);
            }
        }

        private static async Task<ImageSource> DownloadImage(string path)
        {
            string url = Constants.BuildContentUrl(path);

            string filename = Path.GetFileName(url);
            if (!Directory.Exists(ApplicationState.DownloadsPath))
            {
                Directory.CreateDirectory(ApplicationState.DownloadsPath);
            }

            if (!string.IsNullOrEmpty(filename))
            {
                var imageFile = Path.Combine(ApplicationState.DownloadsPath, filename);
                if (!File.Exists(imageFile))
                {
                    try
                    {
                        await Download(url, Path.GetFullPath(imageFile));
                    }
                    catch (Exception ex) { }
                }


                return ImageSource.FromFile(imageFile);
            }
            return ImageSource.FromFile("noimage.jpg");
        }



        public static void MakeWebViewFromHtmlLabel(StackLayout layout, Label htmlLabel)
        {
            string headerString = "<header><meta name='viewport' content='width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no'></meta>" +
                "<style>" +
                "* { font-family: inherit !important; }" +
                "html{font-family: Arial !important; }" +
                "body{font-family:Arial;padding: 0;margin: 0;font-size: 16px;}" +
                "p{font-size: 16px;font-family: Arial; margin-top:4px; margin-bottom:4px;}" +
                "h1{font-size: 32px;font-family: Arial;margin-top:4px; margin-bottom:4px;}" +
                "h2{font-size: 24px;font-family: Arial;margin-top:4px; margin-bottom:4px;}" +
                "h3{font-size: 19px;font-family: Arial;margin-top:4px; margin-bottom:4px;}" +
                "h4{font-size: 16px;font-family: Arial;margin-top:4px; margin-bottom:4px;}" +
                "h5{font-size: 14px;font-family: Arial;margin-top:4px; margin-bottom:4px;}" +
                "h6{font-size: 19px;font-family: Arial;margin-top:4px; margin-bottom:4px;}" +
                "ol{ margin-top:4px; margin-bottom:4px;}" +
                "ul{ margin-top:4px; margin-bottom:4px;}" +
                "li{line-height: 1.5;}" +
                "img{max-width:100%}" +
                "" +
                "</style>" +
                "</header>";
            string finalHtml = $"<html>" +
                               $"{headerString}" +
                               $"<body>{htmlLabel.Text}</body>" +
                               $"</html>";

            // htmlLabel.Text = finalHtml;
            //htmlLabel.FontSize = 16;

            //var webView = new SuperWebView
            //{
            //    HeightRequest = htmlLabel.Height,
            //    Margin = new Thickness(20, 0, 20, 0),
            //    VerticalOptions = LayoutOptions.Start,
            //    InputTransparent = true,
            //    Source = new SuperHtmlWebViewSource() { Html = finalHtml },

            //};

            //
            string xmlHtml = finalHtml.Replace("&nbsp;", "&#160;").Replace("<br>", "<br/>");
            xmlHtml = xmlHtml.Replace("<br style=\"caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); -webkit-tap-highlight-color: rgba(26, 26, 26, 0.3); -webkit-text-size-adjust: auto;\">",
                "<br style=\"caret-color: rgb(0, 0, 0); color: rgb(0, 0, 0); -webkit-tap-highlight-color: rgba(26, 26, 26, 0.3); -webkit-text-size-adjust: auto;\"/>");

            var xmlDoc = new XmlDocument();
            xmlDoc.LoadXml(xmlHtml);


            double plusHeight = 0;
            foreach (XmlNode node in xmlDoc.ChildNodes)
            {
                plusHeight += GetHtmlHDigit(node);
            }


            //if(Device.RuntimePlatform == Device.Android)
            //{
            //    plusHeight += htmlLabel.Height / 10;
            //}



            WebView webView = new WebView
            {
                HeightRequest = htmlLabel.Height + 20 + plusHeight,
                Margin = htmlLabel.Margin,
                VerticalOptions = LayoutOptions.Start,
                InputTransparent = true,
                Source = new HtmlWebViewSource() { Html = finalHtml },

            };


            var d = DeviceDisplay.MainDisplayInfo.Density;
            if (Device.RuntimePlatform == Device.Android)
            {
                if (d >= 2.7)
                {
                    webView.HeightRequest += webView.HeightRequest / 100 * 5;
                }
                else
                {
                    webView.HeightRequest += webView.HeightRequest / 100 * 6;
                }
            }


            if (Device.RuntimePlatform == Device.iOS)
            {
                webView.HeightRequest -= webView.HeightRequest / 100 * 19;
            }


            var descLabelIndex = layout.Children.IndexOf(htmlLabel);
            layout.Children.Remove(htmlLabel);
            layout.Children.Insert(descLabelIndex, webView);
        }

        private static double GetHtmlHDigit(XmlNode node)
        {
            double defaultFontSize = 16;

            double val = 0;
            if (node.NodeType == XmlNodeType.Element)
            {
                switch (node.Name)
                {
                    case "h1":
                    val += 32 + 8 - defaultFontSize;
                    break;
                    case "h2":
                    val += 24 + 8 - defaultFontSize;
                    break;
                    case "h3":
                    val += 19 + 8 - defaultFontSize;
                    break;
                    case "h4":
                    val += 16 + 8 - defaultFontSize;
                    break;
                    case "h5":
                    val += 14 + 8 - defaultFontSize;
                    break;
                    case "h6":
                    val += 19 + 8 - defaultFontSize;
                    break;
                    case "br":
                    val += 24 - defaultFontSize;
                    break;
                    case "p":
                    val += 16 + 8 - defaultFontSize;
                    break;
                    case "ol":
                    val += 16 + 8 - defaultFontSize;
                    break;
                    case "ul":
                    val += 16 + 8 - defaultFontSize;
                    break;
                    case "li":
                    val += 16 + 8 - defaultFontSize;
                    break;
                    default:
                    val += 16 + /*10*/ -defaultFontSize;
                    break;

                }
            }

            foreach (XmlNode child in node.ChildNodes)
            {
                val += GetHtmlHDigit(child);
            }

            return val;
        }

        /*
        private static async Task<MediaSource> DownloadMedia(string path)
        {
            string url = Triggero.Mobile.Constants.UrlContent + path;

            string filename = Path.GetFileName(url);
            if (!Directory.Exists(ApplicationState.DownloadsPath))
            {
                Directory.CreateDirectory(ApplicationState.DownloadsPath);
            }

            if (!string.IsNullOrEmpty(filename))
            {
                var imageFile = Path.Combine(ApplicationState.DownloadsPath, filename);
                if (!File.Exists(imageFile))
                {
                    try
                    {
                        await Download(url, Path.GetFullPath(imageFile));
                    }
                    catch (Exception ex) { }
                }
                return MediaSource.FromFile(imageFile);
            }
            return MediaSource.FromFile("");
        }
        */

        private static async Task Download(string url, string pathSaveImage)
        {
            using (HttpClient client = new HttpClient())
            {
                try
                {
                    var response = await client.GetAsync(url);
                    using (var fs = new FileStream(pathSaveImage, FileMode.CreateNew))
                    {
                        await response.Content.CopyToAsync(fs);
                    }
                }
                catch (Exception ex) { }
            }

        }
    }
}
