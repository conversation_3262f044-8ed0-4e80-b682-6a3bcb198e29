﻿using DrawnUi.Maui.Draw;
using System;
using System.Collections.Generic;
using System.Text;
using Triggero.Controls.Cards.TasksForToday;
using Triggero.MauiClient.Helpers;
using Triggero.MauiClient.Helpers.Modules;
using Triggero.Mobile.ViewModels;
using Triggero.Models;
using Triggero.Models.Practices.Categories;
using Xamarin.Forms;

namespace Triggero.Mobile.Models
{
    public class BaseTaskForTodayView : SkiaLayout
    {
        public BaseTaskForTodayView(TaskForTodayViewModel vm) : base()
        {
            _vm = vm;

            BindingContext = _vm;
        }

        protected readonly TaskForTodayViewModel _vm;

        public event EventHandler<TaskForToday> Tapped;
        public virtual void onTapped(object sender, EventArgs e)
        {
            Tapped?.Invoke(this, _vm.Model);
        }

        public event EventHandler<TaskForToday> CheckedChanged;
        public virtual void onChecked(object sender, CheckedChangedEventArgs e)
        {
            CheckedChanged?.Invoke(this, _vm.Model);
            ApplicationState.SaveToFile(ApplicationState.TodayData, TodayData.FilePath);
        }
        protected void FireCheckedChanged()
        {
            CheckedChanged?.Invoke(this, _vm.Model);
            ApplicationState.SaveToFile(ApplicationState.TodayData, TodayData.FilePath);
        }
    }
}
