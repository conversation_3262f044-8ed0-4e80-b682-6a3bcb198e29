﻿@using CRMWeb.Helpers.Html
@using Triggero.Models.Practices
@using Triggero.Models.Practices.Categories
@using Triggero.Web.ViewModels.Practices
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "СОЗДАНИЕ КАТЕГОРИИ УПРАЖНЕНИЙ";
}
@model ExerciseCategory

 <!-- CONTENT -->
<form asp-action="CreateExerciseCategoryPOST" asp-controller="ExerciseCategories" method="post" onsubmit="onSubmit(event)" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">


            <div class="mb-3">
                <label for="" class="formlabel">Загрузить изображение</label>
                <div class="content-load">
                    <div class="close-file" id="close_file"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image" for="pct" style="height: 6em;"><div class="text-fileupload">Загрузите файл</div></label>
                    <input class="pctfile" type="file" name="file" id="pct">
                </div>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Описание</label>
                <textarea asp-for="Description" required class="form-control" placeholder=""></textarea>
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>


<script>

   async function onSubmit(event){
        event.preventDefault();
        await cropFinal('pct', 'upload-image');
        event.target.submit();
    }

    $(document).ready(function () {
        initImageCroppieSized('upload-image', 'pct', 'close_file', 300, 300);
    });


</script>



