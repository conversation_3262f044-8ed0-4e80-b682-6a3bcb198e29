﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Domain.Models;
using Triggero.Models.MoodTracker.User;

namespace Triggero.Domain.Controllers.Rest.General.Users
{
    [ApiController]
    [Route("[controller]")]
    public class UserMoodTrackerController : ApiController
    {
        public UserMoodTrackerController(DatabaseContext db) : base(db)
        {
        }

        [HttpGet, Route("GetMoodTrackerItems")]
        public async Task<List<MoodtrackerItem>> GetMoodTrackerItems(int userId)
        {
            var user = DB.Users
                .Include(o => o.MoodtrackerItems).ThenInclude(o => o.Influences).ThenInclude(o => o.Factor)
                .Include(o => o.MoodtrackerItems).ThenInclude(o => o.Influences).ThenInclude(o => o.FactorDetails)
                .Include(o => o.MoodtrackerItems).ThenInclude(o => o.Note)
                .FirstOrDefault(o => o.Id == userId);

            RemoveMoodtrackerItemsLoop(user.MoodtrackerItems);

            return user.MoodtrackerItems;
        }
        [HttpGet, Route("GetMoodTrackerItemsByPeriod")]
        public async Task<List<MoodtrackerItem>> GetMoodTrackerItemsByPeriod(int userId, DateTime from, DateTime to)
        {
            var user = DB.Users
                .Include(o => o.MoodtrackerItems).ThenInclude(o => o.Influences).ThenInclude(o => o.Factor)
                .Include(o => o.MoodtrackerItems).ThenInclude(o => o.Influences).ThenInclude(o => o.FactorDetails)
                .Include(o => o.MoodtrackerItems).ThenInclude(o => o.Note)
                .FirstOrDefault(o => o.Id == userId);

            RemoveMoodtrackerItemsLoop(user.MoodtrackerItems);

            return user.MoodtrackerItems.Where(o => o.Date >= from && o.Date <= to)
                                        .ToList();
        }



        [HttpPut, Route("AddMoodTrackerItem")]
        public async Task AddMoodTrackerItem(int userId, MoodtrackerItem item)
        {
            var user = DB.Users.FirstOrDefault(o => o.Id == userId);
            user.MoodtrackerItems.Add(item);

            DB.Users.Update(user);
            DB.SaveChanges();
        }


        [HttpPost, Route("SendStatsToEmail")]
        public async Task SendStatsToEmail(int userId, string email, MoodTrackerReportSettings settings)
        {
            var user = DB.Users

                .Include(o => o.MoodtrackerItems)
                .ThenInclude(o => o.Influences)
                .ThenInclude(o => o.Factor)

                .Include(o => o.MoodtrackerItems)
                .ThenInclude(o => o.Influences)
                .ThenInclude(o => o.FactorDetails)

                .FirstOrDefault(o => o.Id == userId);


            //TODO: Сделать отправку отчета
        }



        #region Заметки

        [HttpGet, Route("GetMoodTrackerNotes")]
        public async Task<List<MoodtrackerNote>> GetMoodTrackerNotes(int userId)
        {
            var items = DB.MoodtrackerNotes.Where(o => o.UserId == userId).ToList();
            items.ForEach(o => o.User = null);
            return items;
        }
        [HttpPost, Route("AddMoodtrackerNote")]
        public async Task<MoodtrackerNote> AddMoodtrackerNote(int userId, string text)
        {
            var note = new MoodtrackerNote()
            {
                UserId = userId,
                Text = text
            };
            DB.MoodtrackerNotes.Add(note);
            DB.SaveChanges();

            return note;
        }
        [HttpPut, Route("UpdateMoodtrackerNote")]
        public async Task<MoodtrackerNote> UpdateMoodtrackerNote(int id, string text)
        {
            var note = DB.MoodtrackerNotes.FirstOrDefault(o => o.Id == id);
            note.Text = text;

            DB.MoodtrackerNotes.Update(note);
            DB.SaveChanges();

            return note;
        }
        #endregion

        #region Private methods
        private void RemoveMoodtrackerItemsLoop(List<MoodtrackerItem> items)
        {
            foreach (var item in items)
            {
                item.Note.User = null;
                foreach (var influence in item.Influences)
                {
                    influence.Factor.FactorDetails.ForEach(o => o.Factor = null);
                    influence.FactorDetails.ForEach(o => o.Factor = null);
                }
            }
        }


        #endregion

    }
}
