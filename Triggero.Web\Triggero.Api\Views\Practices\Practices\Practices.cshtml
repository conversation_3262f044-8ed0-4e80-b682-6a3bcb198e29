﻿@using Triggero.Models
@using Triggero.Models.Practices
@using Triggero.Web.Extensions;
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "ПРАКТИКИ";
}
@model List<Practice>
<!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                    <th scope="col">ID</th>
                  @*  <th scope="col">Изображение</th>*@
                    <th scope="col">Название</th>
                    <th scope="col">Краткое содержание</th>
                    <th scope="col">Дата добавления</th>
                    <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model)
                    {
                         <tr>
                            <th scope="row">#@item.Id</th>
                          @*  <th scope="row"><div class="imagetable" style="background: url('@item.ImgPath')"></div></th>*@
                            <td>@item.Title</td>
                            <td>@Html.Raw(item.Description.SafeSubstringWithStr(0,100,"..."))</td>
                            <td>@item.CreatedAt.ToString("dd.MM.yyyy")</td>
                            <td>
                                <div class="interect">
                                    <a asp-action="UpdatePractice" asp-controller="Practices" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    <a onclick="confirmDeleting('deleteItem(@item.Id)')" class="delete"><i class="fa-solid fa-trash"></i></a>
                                    @if (item.IsHidden)
                                    {
                                        <a asp-controller="Practices" asp-action="SetVisibility" asp-route-id="@item.Id" asp-route-hidden="@(!item.IsHidden)" class="delete"><i class="fa-solid fa-eye"></i></a>
                                    }
                                    else
                                    {
                                        <a asp-controller="Practices" asp-action="SetVisibility" asp-route-id="@item.Id" asp-route-hidden="@(!item.IsHidden)" class="delete"><i class="fa-solid fa-eye-slash"></i></a>
                                    }
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

            <div class="mb-3 position-right">
                <a asp-action="CreatePractice" asp-controller="Practices" class="button-classic">Добавить</a>
            </div>

            <script>
                $(document).ready( function () {
                    $('#tablemanager').DataTable();
                } );
            </script>

            <script>
                async function deleteItem(id) {
                    await fetch(document.location.origin + '/DeletePractice?id=' + id,
                        {
                            method: 'DELETE'
                        });
                }
            </script>
        </div>
    </div>
</div>