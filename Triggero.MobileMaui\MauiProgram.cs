﻿using Microsoft.Extensions.Logging;
using Triggero.MobileMaui.Services;
using DrawnUi.Draw;

namespace Triggero.MobileMaui
{
    public static class MauiProgram
    {
        public static MauiApp CreateMauiApp()
        {
            var builder = MauiApp.CreateBuilder();
            builder
                .UseMauiApp<App>()
                .UseDrawnUi(new()
                {
                    UseDesktopKeyboard = true,
                    DesktopWindow = new()
                    {
                        Width = 500,
                        Height = 700
                    }
                })
                .ConfigureFonts(fonts =>
                {
                    fonts.AddFont("OpenSans-Regular.ttf", "FontText");
                    fonts.AddFont("OpenSans-Bold.ttf", "FontTextBold");
                    fonts.AddFont("OpenSans-BoldItalic.ttf", "FontTextBoldItalic");
                    fonts.AddFont("OpenSans-ExtraBold.ttf", "FontTextExtraBold");
                    fonts.AddFont("OpenSans-ExtraBoldItalic.ttf", "FontTextExtraBoldItalic");
                    fonts.AddFont("OpenSans-Italic.ttf", "FontTextItalic");
                    fonts.AddFont("OpenSans-Light.ttf", "FontTextLight");
                    fonts.AddFont("OpenSans-LightItalic.ttf", "FontTextLightItalic");
                    fonts.AddFont("OpenSans-Medium.ttf", "FontTextMedium");
                    fonts.AddFont("OpenSans-MediumItalic.ttf", "FontTextMediumItalic");
                    fonts.AddFont("OpenSans-SemiBold.ttf", "FontTextSemiBold");
                    fonts.AddFont("OpenSans-SemiBoldItalic.ttf", "FontTextSemiBoldItalic");
                });

            // Register platform services
            builder.AddPlatformServices();

#if DEBUG
            builder.Logging.AddDebug();
#endif

            return builder.Build();
        }
    }
}
