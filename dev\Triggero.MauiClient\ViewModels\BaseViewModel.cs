﻿namespace Triggero.MauiClient.ViewModels;

public class BaseViewModel : BindableObject, IDisposable
{



    private bool _IsBusy;
    public bool IsBusy
    {
        get
        {
            return _IsBusy;
        }
        set
        {
            if (_IsBusy != value)
            {
                _IsBusy = value;
                OnPropertyChanged();
            }
        }
    }

    protected virtual void OnDisposing()
    {

    }

    public bool IsDisposed { get; protected set; }

    public void Dispose()
    {
        OnDisposing();
        IsDisposed = true;
    }
}