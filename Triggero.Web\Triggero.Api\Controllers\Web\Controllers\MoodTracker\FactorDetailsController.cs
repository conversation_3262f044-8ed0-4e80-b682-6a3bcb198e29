﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.MoodTracker;
using Triggero.Models.Practices.Categories;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Factors;
using Triggero.Web.ViewModels.Practices;

namespace Triggero.Web.Controllers.Practices
{
    [Authorize]
    public class FactorDetailsController : AbsController
    {
        public FactorDetailsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("FactorDetails")]
        public IActionResult FactorDetails()
        {
            var cats = DB.FactorDetails.Include(o => o.Factor)
                                       .Where(o => !o.IsDeleted)
                                       .ToList();
            cats.Reverse();
            return View(@"Views\MoodTracker\FactorDetails\FactorDetails.cshtml", cats);
        }


        //[Route("CreateFactorDetail")]
        public IActionResult CreateFactorDetail()
        {
            var vm = new CreateFactorDetailVM
            {
                Factors = DB.Factors.ToList()
            };
            return View(@"Views\MoodTracker\FactorDetails\CreateFactorDetail.cshtml", vm);
        }

        [HttpPost]
        //[Route("CreateFactorDetailPOST"), HttpPost]
        public async Task<IActionResult> CreateFactorDetailPOST([FromForm] CreateFactorDetailVM vm)
        {
            vm.NewFactorDetail.ImgPath = await SetAttachmentIfHas(vm.NewFactorDetail.ImgPath);
            DB.FactorDetails.Add(vm.NewFactorDetail);
            await DB.SaveChangesAsync();
            return RedirectToAction("FactorDetails", "FactorDetails");
        }

        [Route("UpdateFactorDetail")]
        public IActionResult UpdateFactorDetail(int id)
        {
            var factor = DB.FactorDetails.FirstOrDefault(o => o.Id == id);
            var vm = new CreateFactorDetailVM
            {
                Factors = DB.Factors.ToList(),
                NewFactorDetail = factor
            };
            return View(@"Views\MoodTracker\FactorDetails\UpdateFactorDetail.cshtml", vm);
        }

        [HttpPost]
        //[Route("UpdateFactorDetailPOST"), HttpPost]
        public async Task<IActionResult> UpdateFactorDetailPOST([FromForm] CreateFactorDetailVM vm)
        {
            vm.NewFactorDetail.ImgPath = await SetAttachmentIfHas(vm.NewFactorDetail.ImgPath);
            DB.FactorDetails.Update(vm.NewFactorDetail);
            await DB.SaveChangesAsync();
            return RedirectToAction("FactorDetails", "FactorDetails");
        }

        [HttpDelete]
        //[Route("DeleteFactorDetail"), HttpDelete]
        public async Task<IActionResult> DeleteExerciseCategory(int id)
        {
            var factor = DB.FactorDetails.FirstOrDefault(o => o.Id == id);
            factor.IsDeleted = true;
            DB.FactorDetails.Update(factor);
            await DB.SaveChangesAsync();
            return RedirectToAction("FactorDetails", "FactorDetails");
        }
    }
}
