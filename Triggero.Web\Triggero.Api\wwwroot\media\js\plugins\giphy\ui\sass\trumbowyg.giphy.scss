/**
 * Trumbowyg v2.26.0 - A lightweight WYSIWYG editor
 * Default stylesheet for Trumbowyg editor plugin
 * ------------------------
 * @link http://alex-d.github.io/Trumbowyg
 * @license MIT
 * <AUTHOR> (Alex-D)
 *         Twitter : @AlexandreDemode
 *         Website : alex-d.fr
 */

.trumbowyg-giphy-button svg {
    transform: scale(1.22);
}

.trumbowyg-giphy-search {
    display: block;
    width: 80%;
    margin: 5%;
    padding-left: 10px;
    padding-right: 150px;
}

.trumbowyg-giphy-close {
    position: absolute;
    top: calc(5% + 8px);
    right: calc(5% - 2px);
    width: 30px;
    height: 30px;
    background: none;
    border: 1px solid transparent;

    &:hover,
    &:focus {
        outline: none;
        background: #ecf0f1;
    }
    &:focus {
        border-color: rgba(0, 0, 0, 0.3);
    }
}

.trumbowyg-powered-by-giphy {
    position: absolute;
    top: calc(5% + 12px);
    right: calc(15% + 10px);
    pointer-events: none;
    user-select: none;

    span {
        text-transform: uppercase;
        font-weight: bold;
        font-size: 10px;
        opacity: 0.6;
    }

    svg {
        width: 66px;
        height: 15px;
        vertical-align: bottom;
        margin-left: 6px;
        opacity: 0.45;
    }
}

.trumbowyg-giphy-modal-scroll {
    overflow: auto;
    overflow-x: hidden;
    height: 240px;
}

.trumbowyg-giphy-modal {
    padding: 0 5%;
    columns: 3;
    column-gap: 10px;

  .trumbowyg-giphy-no-result {
    width: 250%;
    margin: 13% 0 0 29%;
  }

  .trumbowyg-giphy-offline {
    font-size: 18px;
    width: 305%;
    height: 600px;
    margin-top: 95px;
    text-align: center;
  }
}

.trumbowyg-giphy-modal .img-container {
    width: 100%;
    margin-bottom: 10px;
    background-color: #ecf0f1;

    img {
        width: 100%;
        cursor: pointer;
        opacity: 0;
        transition: opacity 150ms;

        &:hover,
        &:focus {
            border: #2ecc71 solid 3px;
        }
    }

    img.tbw-loaded {
        opacity: 1;
    }
}
