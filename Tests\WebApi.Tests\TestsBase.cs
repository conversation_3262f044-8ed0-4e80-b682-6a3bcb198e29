global using Microsoft.Extensions.DependencyInjection;
global using NUnit.Framework;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Hosting;
using Microsoft.Extensions.Logging;
using MySqlConnector;
using Respawn;
using Respawn.Graph;
using System.IO;
using System.Text.Json;
using System.Threading.Tasks;
using Triggero.Application.Abstractions;
using Triggero.Application.Extensions;
using Triggero.Application.Services;
using Triggero.Database;

namespace AppoMobi.Tests.Models;

public class SmsMock : ISmsService
{
    public Task<bool> SendSmsAsync(string phone, string text)
    {
        return Task.FromResult(true);
    }
}

public class TestsBase
{
    public IHost TestHost { get; }
    private IServiceScopeFactory _scopeFactory;
    private Respawner _respawner;
    private static string connectionString =
        "server=localhost;user=tests;password=XXXXXXXX;port=3306;database=triggero_tests;Convert Zero Datetime=true;";

    public TestsBase()
    {
        TestHost = CreateHostBuilder().Build();
        Task.Run(() => TestHost.RunAsync());
    }


    [OneTimeSetUp]
    public async Task RunBeforeAnyTests()
    {
        _scopeFactory = TestHost.Services.GetService<IServiceScopeFactory>();

        await EnsureDatabase();

        _connection = new MySqlConnection(connectionString);
        await _connection.OpenAsync();

        _respawner = await Respawner.CreateAsync(
            _connection,
            new RespawnerOptions
            {
                TablesToIgnore = new Table[] { "__EFMigrationsHistory" },
                DbAdapter = DbAdapter.MySql
            });
    }

    MySqlConnection _connection;

    public static IHostBuilder CreateHostBuilder(string[] args = null) =>
        Host.CreateDefaultBuilder(args)
            .ConfigureAppConfiguration((hostingContext, config) =>
            {
                config.SetBasePath(Directory.GetCurrentDirectory());
                config.AddJsonFile("appsettings.json", optional: true);
                config.AddJsonFile("secrets.json", optional: false);
                config.AddEnvironmentVariables();
                if (args != null)
                {
                    config.AddCommandLine(args);
                }
            })
            .ConfigureServices((hostContext, services) =>
            {
                services.AddOptions();

                services.AddApplication();

                services.AddTransient<IAccountManagerService, IdentityService>();

                services.AddTransient<ISmsService, SmsService>();

                services.AddScoped<DatabaseMigrator>();
                services.AddDbContext<DatabaseContext>(options =>
                    options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString)));

            })
            .ConfigureLogging((hostingContext, logging) =>
            {
                logging.AddConfiguration(hostingContext.Configuration.GetSection("Logging"));
                logging.AddConsole();
            });


    protected string Json(object value)
    {
        return JsonSerializer.Serialize(value, new JsonSerializerOptions
        {
            PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
            WriteIndented = true
        });
    }


    private async Task EnsureDatabase()
    {
        // Apply migrations
        using var scope = _scopeFactory.CreateScope();
        var seeder = scope.ServiceProvider.GetRequiredService<DatabaseMigrator>();
        await seeder.PrepareDatabase();
    }

    [SetUp]
    public async Task ResetDatabaseAsync()
    {
        await _respawner.ResetAsync(_connection);
    }

}
