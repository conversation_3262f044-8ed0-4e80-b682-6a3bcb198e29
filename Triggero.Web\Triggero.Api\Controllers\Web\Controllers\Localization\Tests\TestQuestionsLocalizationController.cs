﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Tests;
using Triggero.Models.Localization.Tests.Questions;
using Triggero.Models.Tests;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;
using Triggero.Web.ViewModels.Localization.Tests;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class TestQuestionsLocalizationController : AbsController
    {
        public TestQuestionsLocalizationController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }


        [Route("Localizations/Tests/CreateQuestionLocalization")]
        public IActionResult CreateQuestionLocalization(int id)
        {
            var vm = new TestQuestionLocalizationVM
            {
                Id = id,
                CurrentLanguage = GetCurrentLanguage(),
                Question = DB.Questions.Include(o => o.Options).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                       .FirstOrDefault(o => o.Id == id)
            };
            return View(@"Views\Localization\Tests\Questions\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/Tests/CreateQuestionLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateQuestionLocalizationPOST([FromForm] TestQuestionLocalizationVM vm)
        {
            vm.Localization.Language = GetCurrentLanguage();
            var post = DB.Questions.FirstOrDefault(o => o.Id == vm.Id);
            post.Localizations.Add(vm.Localization);

            DB.Questions.Update(post);
            await DB.SaveChangesAsync();
            return RedirectToAction("Tests", "TestStartPageLocalization");
        }





        [Route("Localizations/Tests/UpdateQuestionLocalization")]
        public IActionResult UpdateQuestionLocalization(int id)
        {
            var post = DB.Questions
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new TestQuestionLocalizationVM
            {
                Id = id,
                CurrentLanguage = GetCurrentLanguage(),
                Question = DB.Questions.Include(o => o.Options).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                       .FirstOrDefault(o => o.Id == id),
                Localization = post.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Tests\Questions\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/Tests/UpdateQuestionLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateQuestionLocalizationPOST([FromForm] TestQuestionLocalizationVM vm)
        {
            DB.QuestionLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("Tests", "TestStartPageLocalization");
        }

    }
}
