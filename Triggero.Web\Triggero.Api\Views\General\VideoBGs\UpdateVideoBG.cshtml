﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.General
@using Triggero.Models.Practices
@using Triggero.Models.Practices.Categories
@using Triggero.Web.ViewModels.Practices
@{
    ViewData["Title"] = "РЕДАКТИРОВАНИЕ ВИДЕО ФОНА";
}
@model VideoBg

 <!-- CONTENT -->
<form asp-action="UpdateVideoBGPOST" asp-controller="VideoBg" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">

            <input type="hidden" asp-for="Id" value="@Model.Id" />
            <input type="hidden" asp-for="VideoPath" value="@Model.VideoPath" />

            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Title" value="@Model.Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Видеозапись</label>
                <input type="file" name="file" class="form-control" placeholder="">
            </div>   

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>