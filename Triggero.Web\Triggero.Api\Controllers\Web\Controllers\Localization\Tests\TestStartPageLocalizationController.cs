﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Tests;
using Triggero.Models.Tests;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;
using Triggero.Web.ViewModels.Localization.Tests;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class TestStartPageLocalizationController : AbsController
    {
        public TestStartPageLocalizationController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/Tests")]
        public IActionResult Tests()
        {
            var posts = DB.Tests
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            posts.Reverse();

            var vm = new LocalizationListVM<Test>
            {
                Items = posts,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Tests\StartPage\Tests.cshtml", vm);
        }



        [Route("Localizations/Tests/CreateStartPageLocalization")]
        public IActionResult CreateStartPageLocalization(int id)
        {
            var vm = new TestStartPageLocalizationVM
            {
                Id = id,
                CurrentLanguage = GetCurrentLanguage(),
                Questions = DB.Tests.Include(o => o.Questions).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                    .FirstOrDefault(o => o.Id == id)
                                    .Questions,
                Results = DB.Tests.Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                    .FirstOrDefault(o => o.Id == id)
                                    .Results,
            };
            return View(@"Views\Localization\Tests\StartPage\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/Tests/CreateStartPageLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateStartPageLocalizationPOST([FromForm] TestStartPageLocalizationVM vm)
        {
            vm.Localization.Language = GetCurrentLanguage();
            var post = DB.Tests.FirstOrDefault(o => o.Id == vm.Id);
            post.Localizations.Add(vm.Localization);

            DB.Tests.Update(post);
            await DB.SaveChangesAsync();
            return RedirectToAction("Tests", "TestStartPageLocalization");
        }





        [Route("Localizations/Tests/UpdateStartPageLocalization")]
        public IActionResult UpdateStartPageLocalization(int id)
        {
            var post = DB.Tests
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new TestStartPageLocalizationVM
            {
                Id = id,
                CurrentLanguage = GetCurrentLanguage(),
                Questions = DB.Tests.Include(o => o.Questions).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                    .FirstOrDefault(o => o.Id == id)
                                    .Questions,
                Results = DB.Tests.Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                                    .FirstOrDefault(o => o.Id == id)
                                    .Results,
                Localization = post.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Tests\StartPage\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/Tests/UpdateStartPageLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateStartPageLocalizationPOST([FromForm] TestStartPageLocalizationVM vm)
        {
            DB.TestLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("Tests", "TestStartPageLocalization");
        }

    }
}
