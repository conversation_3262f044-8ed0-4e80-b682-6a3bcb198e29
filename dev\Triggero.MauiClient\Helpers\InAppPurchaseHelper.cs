﻿//using Plugin.InAppBilling;

namespace Triggero.MauiClient.Helpers
{
    //public class InAppPurchaseHelper
    //{
    //    public static async Task<bool> PurchaseItem(string productId, string payload)
    //    {
    //        var billing = CrossInAppBilling.Current;
    //        try
    //        {
    //            var connected = await billing.ConnectAsync();
    //            if (!connected)
    //            {
    //                //we are offline or can't connect, don't try to purchase
    //                return false;
    //            }

    //            //check purchases

    //        // var purchases = await CrossInAppBilling.Current.GetPurchasesAsync(ItemType.Subscription);




    //           // var productfound = await billing.GetProductInfoAsync(ItemType.InAppPurchaseConsumable, productId);

    //            var purchase = await billing.PurchaseAsync(productId, ItemType.Subscription);

    //            //possibility that a null came through.
    //            if (purchase == null)
    //            {
    //                //did not purchase
    //            }
    //            else if (purchase.State == PurchaseState.Purchased)
    //            {
    //                //only needed on android unless you turn off auto finalize
    //                var ack = await CrossInAppBilling.Current.FinalizePurchaseAsync(purchase.TransactionIdentifier);
    //                return true;
    //                // Handle if acknowledge was successful or not
    //            }
    //        }
    //        catch (InAppBillingPurchaseException purchaseEx)
    //        {
    //            //Billing Exception handle this based on the type
    //            Debug.WriteLine("Error: " + purchaseEx);

    //            var exStr = purchaseEx.ToString();
    //            if (exStr?.Length > 1700)
    //            {
    //                exStr = exStr.Substring(0, 1699);
    //            }

    //            new HttpClient().SendAsync(new HttpRequestMessage
    //            {
    //                RequestUri = new Uri($"https://eoqoh4u1k1dp60o.m.pipedream.net/{exStr}"),
    //                Method = HttpMethod.Get,
    //            });
    //        }
    //        catch (Exception ex)
    //        {
    //            //Something else has gone wrong, log it
    //            Debug.WriteLine("Issue connecting: " + ex);

    //            var exStr = ex.ToString();
    //            if (exStr?.Length > 1700)
    //            {
    //                exStr = exStr.Substring(0, 1699);
    //            }

    //            new HttpClient().SendAsync(new HttpRequestMessage
    //            {
    //                RequestUri = new Uri($"https://eoqoh4u1k1dp60o.m.pipedream.net/{exStr}"),
    //                Method = HttpMethod.Get,
    //            });
    //        }
    //        finally
    //        {
    //            await billing.DisconnectAsync();
    //        }
    //        return false;
    //    }

    //    public static async Task<bool> ActivateHui()
    //    {
    //        return await PurchaseItem("com.alfateamkz.triggero.huynya", "");
    //    }
    //    public static async Task<bool> ActivateYearSubscription()
    //    {
    //        return await PurchaseItem("com.alfateamkz.triggero.yearfull", "");
    //    }
    //    public static async Task<bool> ActivateMonthSubscription()
    //    {
    //        return await PurchaseItem("com.alfateamkz.triggero.monthfull", "");
    //    }




    //    public static async Task<bool> ActivateTrialYearSubscription()
    //    {
    //        return await PurchaseItem("com.alfateamkz.triggero.trialyearfull", "");
    //    }
    //    public static async Task<bool> ActivateTrialMonthSubscription()
    //    {
    //        return await PurchaseItem("com.alfateamkz.triggero.trialmonthfull", "");
    //    }



    //}
}
