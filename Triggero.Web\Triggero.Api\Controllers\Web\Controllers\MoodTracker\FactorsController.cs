﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.MoodTracker;
using Triggero.Models.Practices.Categories;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Practices;

namespace Triggero.Web.Controllers.Practices
{
    [Authorize]
    public class FactorsController : AbsController
    {
        public FactorsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Factors")]
        public IActionResult Factors()
        {
            var cats = DB.Factors
                .Where(o => !o.IsDeleted).ToList();
            cats.Reverse();
            return View(@"Views\MoodTracker\Factors\Factors.cshtml", cats);
        }


        //[Route("CreateFactor")]
        public IActionResult CreateFactor()
        {
            return View(@"Views\MoodTracker\Factors\CreateFactor.cshtml");
        }

        [HttpPost]
        //[Route("CreateFactorPOST"), HttpPost]
        public async Task<IActionResult> CreateFactorPOST([FromForm] Factor factor)
        {
            factor.ImgPath = await SetAttachmentIfHas(factor.ImgPath);
            DB.Factors.Add(factor);
            await DB.SaveChangesAsync();
            return RedirectToAction("Factors", "Factors");
        }


        //[Route("UpdateFactor")]
        public IActionResult UpdateFactor(int id)
        {
            var factor = DB.Factors.FirstOrDefault(o => o.Id == id);
            return View(@"Views\MoodTracker\Factors\UpdateFactor.cshtml", factor);
        }

        [HttpPost]
        //[Route("UpdateFactorPOST"), HttpPost]
        public async Task<IActionResult> UpdateFactorPOST([FromForm] Factor factor)
        {
            factor.ImgPath = await SetAttachmentIfHas(factor.ImgPath);
            DB.Factors.Update(factor);
            await DB.SaveChangesAsync();
            return RedirectToAction("Factors", "Factors");
        }

        [HttpDelete]
        //[Route("DeleteFactor"), HttpDelete]
        public async Task<IActionResult> DeleteExerciseCategory(int id)
        {
            var factor = DB.Factors.FirstOrDefault(o => o.Id == id);
            factor.IsDeleted = true;
            DB.Factors.Update(factor);
            await DB.SaveChangesAsync();
            return RedirectToAction("Factors", "Factors");
        }
    }
}
