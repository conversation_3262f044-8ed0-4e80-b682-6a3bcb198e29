﻿using Newtonsoft.Json;
using Odintcovo.API.Helpers;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Domain.Models;
using Triggero.Models.General;
using Triggero.Models.MoodTracker.User;
using Triggero.Models.Tests;

namespace MobileAPIWrapper.Methods.General.Users
{
    public class UserMoodTrackerMethods
    {
        public static string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("UserMoodTracker/");

        public async Task<List<MoodtrackerItem>> GetMoodTrackerItems(int userId)
        {
            string url = BASE_HOST + $"GetMoodTrackerItems?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<MoodtrackerItem>>(response.Content);
            return obj;
        }
        public async Task<List<MoodtrackerItem>> GetMoodTrackerItemsByPeriod(int userId, DateTime from, DateTime to)
        {
            string url = BASE_HOST + $"GetMoodTrackerItemsByPeriod?userId={userId}" +
                $"&from={from.ToString("yyyy-MM-ddTHH:mm:ss.000Z")}&to={to.ToString("yyyy-MM-ddTHH:mm:ss.000Z")}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<MoodtrackerItem>>(response.Content);
            return obj;
        }

        public async Task AddMoodTrackerItem(int userId, MoodtrackerItem item)
        {
            string url = BASE_HOST + $"AddMoodTrackerItem?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put, item);
        }

        public async Task SendStatsToEmail(int userId, string email, MoodTrackerReportSettings settings)
        {
            string url = BASE_HOST + $"SendStatsToEmail?userId={userId}&email={email}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Post, settings);
        }


        #region Заметки
        public async Task<List<MoodtrackerNote>> GetMoodTrackerNotes(int userId)
        {
            string url = BASE_HOST + $"GetMoodTrackerNotes?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<MoodtrackerNote>>(response.Content);
            return obj;
        }

        public async Task<MoodtrackerNote> AddMoodtrackerNote(int userId, string text)
        {
            string url = BASE_HOST + $"AddMoodtrackerNote?userId={userId}&text={text}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Post);

            var obj = JsonConvert.DeserializeObject<MoodtrackerNote>(response.Content);
            return obj;
        }
        public async Task<MoodtrackerNote> UpdateMoodtrackerNote(int id, string text)
        {
            string url = BASE_HOST + $"UpdateMoodtrackerNote?id={id}&text={text}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);

            var obj = JsonConvert.DeserializeObject<MoodtrackerNote>(response.Content);
            return obj;
        }
        #endregion
    }
}
