﻿using AppoMobi.Tests.Models;
using FluentAssertions;
using System.Threading.Tasks;
using Triggero.Application.Abstractions;
using Triggero.Application.Services;
using Triggero.Database;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Dto;
using Triggero.Domain.Models.Models;
using Triggero.Models.Enums;
using Triggero.Models.General;


namespace AppoMobi.Tests
{
    public class UnitTests : TestsBase
    {
        [Test]
        public async Task SendMail()
        {
            var service = TestHost.Services.GetService<IMailService>();

            var error = await service.SendEmailAsync("<EMAIL>", "From Tests", "Hello world!");

            error.Should().BeNull();
        }

        [Test]
        public async Task SendSms()
        {
            var service = TestHost.Services.GetService<ISmsService>();

            var error = await service.SendSmsAsync("***********", "0001");

            error.Should().BeTrue();
        }

        [Test]
        public async Task LoginWIthPhone()
        {
            var db = TestHost.Services.GetService<DatabaseContext>();
            var service = TestHost.Services.GetService<IAccountManagerService>();

            var user = new User
            {
                Phone = "***********",
                Name = "Test User"

            };
            db.Users.Add(user);
            await db.SaveChangesAsync();

            var code = await service.RequestSmsCode("***********");

            code.Should().NotBeEmpty();

            var login = await service.LoginAsync(new LoginWithPhoneNumberDto()
            {
                Code = code,
                PhoneNumber = "***********"
            });

            login.Succeeded.Should().BeTrue();
        }

        [Test]
        public async Task RegisterUserFailsEmailTaken()
        {
            var service = TestHost.Services.GetService<IAccountManagerService>();
            var db = TestHost.Services.GetService<DatabaseContext>();

            var user = new User
            {
                Phone = "***********",
                Name = "User Initial",
                Email = "<EMAIL>"
            };
            db.Users.Add(user);
            await db.SaveChangesAsync();

            var model = new RegisterUserDto
            {
                Phone = "***********",
                Name = "Test User",
                Email = "<EMAIL>",
            };

            ApiException catched = null;

            try
            {
                var result = await service.RegisterAsync(model);
            }
            catch (ApiException e)
            {
                catched = e;
            }

            catched.Message.Should().Be("Email already taken");
        }

        [Test]
        public async Task RegisterUserFailsWithoutPhone()
        {
            var service = TestHost.Services.GetService<IAccountManagerService>();

            var model = new RegisterUserDto
            {
                //                Phone = "***********",
                Name = "Test User",
                Email = "<EMAIL>",
            };

            ApiException catched = null;

            try
            {
                var result = await service.RegisterAsync(model);
            }
            catch (ApiException e)
            {
                catched = e;
            }

            catched.Message.Should().Be("Некорректный номер телефона");
        }

        [Test]
        public async Task RegisterUserWithTrial()
        {
            var service = TestHost.Services.GetService<IAccountManagerService>();

            var model = new RegisterUserDto
            {
                Phone = "***********",
                Name = "Test User",
                Email = "<EMAIL>",
                Referer = "youmi",
                ActivateTrial = true
            };

            var result = await service.RegisterAsync(model);

            result.Succeeded.Should().BeTrue();

            result.Data.UserData.Subscription.Type.Should().Be(SubscriptionType.Trial);
        }
    }
}
