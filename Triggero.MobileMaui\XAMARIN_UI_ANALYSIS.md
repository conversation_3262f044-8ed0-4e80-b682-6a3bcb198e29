# Xamarin UI Components Analysis

## Overview
This document catalogs all UI components in the original Xamarin Triggero project that need migration to MAUI, focusing on Phone screens only.

## Main Application Structure

### Shell Architecture (MainPage.xaml)
**Current Xamarin Structure**:
```xml
<Grid RowDefinitions="*,34,Auto" RowSpacing="0">
    <!-- HOME -->
    <Grid x:Name="MainViewGrid" Grid.Row="0" Grid.RowSpan="2" IsVisible="True" />
    
    <!-- LIBRARY -->
    <Grid x:Name="LibraryGrid" Grid.Row="0" Grid.RowSpan="2" IsVisible="False" />
    
    <!-- TESTS -->
    <Grid x:Name="TestsGrid" Grid.Row="0" Grid.RowSpan="2" IsVisible="False" />
    
    <!-- CHATBOT -->
    <Grid x:Name="ChatBotGrid" Grid.Row="0" Grid.RowSpan="2" IsVisible="False" />
    
    <!-- Footer Navigation -->
    <controls:Footer Grid.Row="2" />
</Grid>
```

**Key Features**:
- Grid-based view switching system
- Manual visibility management for different sections
- Footer navigation component
- Platform-specific margins for iOS

## Main Views

### 1. HomeView
**Location**: `Views/Tabs/HomeView.xaml`
**Key Components**:
- User data rendering section
- Recommendations display
- Tasks for today section
- "Need to handle" items with custom cards
- Navigation commands to other sections

**UI Patterns**:
- Dynamic content loading with `MainThread.BeginInvokeOnMainThread`
- Custom card components (`NeedToHandleItem`)
- Event-driven UI updates via `GlobalEvents`

### 2. LibraryView
**Location**: `Views/Tabs/LibraryView.xaml`
**Key Components**:
- Tab-based navigation (Exercises, Practices, Topics, Breath)
- DrawnUI components: `DrawnListCategories`, `DrawnBreathTab`
- Search functionality
- Dynamic tab visibility management

**DrawnUI Usage**:
```csharp
_exercises = new DrawnListCategories(new ListCategoriesExercisesViewModel(), 150);
_practices = new DrawnListCategories(new ListCategoriesPracticesViewModel(), 500);
_topics = new DrawnListCategories(new ListCategoriesTopicsViewModel(), 750);
_breath = new DrawnBreathTab();
```

### 3. TestsView
**Location**: `Views/Tabs/TestsView.xaml`
**Key Components**:
- Test categories display
- Collection view for test items
- Search integration

### 4. ChatBotView
**Location**: Referenced in MainPage.xaml.cs
**Key Components**:
- Chat interface (details need further investigation)

## Card Components (CardsView Usage)

### 1. FactorCard
**Location**: `Views/Tracker/Factors/FactorCard.xaml`
**Structure**:
```xml
<ContentView>
    <Frame CornerRadius="12" BackgroundColor="Transparent" HasShadow="False">
        <Frame.GestureRecognizers>
            <TapGestureRecognizer Tapped="onTapped"/>
        </Frame.GestureRecognizers>
        <!-- Selection styling with DataTrigger -->
        <!-- Content: Image + Label -->
    </Frame>
</ContentView>
```

**Features**:
- Selection state management
- Tap gesture handling
- Dynamic styling based on selection
- Image and text content

### 2. TestCard
**Location**: `Views/Tests/TestCard.xaml`
**Features**:
- Test information display
- Favorite toggle functionality
- Test completion status indicator
- Dynamic content loading

## DrawnUI Components Currently Used

### 1. TestsCategoriesDrawn
**Location**: `Views/Drawn/TestsCategoriesDrawn.xaml`
**Structure**:
```xml
<views:Canvas Gestures="Lock" HardwareAcceleration="Enabled">
    <views:SkiaLayout>
        <views:SkiaShape CornerRadius="16,0,16,0" UseCache="Image" />
        <views:SkiaScroll FrictionScrolled="0.35">
            <views:SkiaLayout 
                ItemTemplate="{Binding ItemTemplate}"
                ItemsSource="{Binding Items}"
                RecyclingTemplate="Disabled"
                Type="Column" />
        </views:SkiaScroll>
    </views:SkiaLayout>
</views:Canvas>
```

**Key Features**:
- Hardware acceleration enabled
- Gesture locking
- Item templating with data binding
- Recycling disabled for performance
- Column layout with spacing

### 2. ListTestsView
**Location**: `Views/Drawn/ListTestsView.xaml`
**Features**:
- DrawnUI namespace: `xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"`
- Integration with ViewModels
- Custom templating

## Custom Renderers Identified

### Platform Initializations (iOS)
From `AppDelegate.cs`:
```csharp
CardsViewRenderer.Preserve();
iOSMaterialFrameRenderer.Init();
SfRotatorRenderer.Init();
// Multiple Syncfusion renderers
FFImageLoading.Forms.Platform.CachedImageRenderer.Init();
SfTextInputLayoutRenderer.Init();
Sharpnado.Shades.iOS.iOSShadowsRenderer.Initialize();
DevExpress.XamarinForms.Editors.iOS.Initializer.Init();
```

### Platform Initializations (Android)
From `MainActivity.cs`:
```csharp
CardsViewRenderer.Preserve();
FFImageLoading.Forms.Platform.CachedImageRenderer.Init(enableFastRenderer: true);
FormsWebViewRenderer.Initialize();
// Multiple Syncfusion renderers
```

## Migration Priority Analysis

### High Priority (Core Functionality)
1. **MainPage Shell Structure** - Essential for app navigation
2. **HomeView** - Primary user interface
3. **FactorCard & TestCard** - Core content display components

### Medium Priority (Feature Complete)
1. **LibraryView** - Secondary navigation and content
2. **TestsView** - Test functionality
3. **DrawnUI Components** - Enhanced UI experience

### Low Priority (Enhancement)
1. **ChatBotView** - Additional feature
2. **Custom Renderers** - Platform-specific enhancements

## Package Dependencies to Replace

### CardsView Package
- **Current**: `CardsView` package in Xamarin
- **Target**: DrawnUI SkiaShape components
- **Usage**: FactorCard, TestCard, and other card-based UI

### Third-Party Renderers
- **Syncfusion Components**: Multiple chart, picker, and UI renderers
- **FFImageLoading**: Image caching and loading
- **Sharpnado**: Shadow and material effects
- **DevExpress**: Editor components

## Migration Strategy Recommendations

### Phase 2A: Core Shell Migration
1. Create MAUI MainPage with grid-based view switching
2. Implement basic navigation between views
3. Create placeholder views for each section

### Phase 2B: Card Component Migration
1. Replace FactorCard with DrawnUI SkiaShape equivalent
2. Replace TestCard with DrawnUI SkiaShape equivalent
3. Implement selection and tap gesture handling

### Phase 2C: DrawnUI Component Migration
1. Migrate TestsCategoriesDrawn to MAUI DrawnUI
2. Update namespace references and event handling
3. Test item templating and data binding

### Phase 2D: View Content Migration
1. Migrate HomeView content and functionality
2. Migrate LibraryView with tab system
3. Migrate TestsView with search functionality

## Technical Considerations

### Namespace Changes
- **Xamarin**: `xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"`
- **MAUI**: Same namespace, but different package implementation

### Event Handling Changes
- **Xamarin**: `TapGestureRecognizer` with `Tapped` event
- **MAUI DrawnUI**: `Tapped` event with `SkiaGesturesParameters`

### Data Binding Compatibility
- Most data binding patterns should remain compatible
- ViewModel patterns can be preserved
- Command binding should work without changes

## Next Steps
1. Start with MainPage shell structure migration
2. Create basic view placeholders
3. Implement navigation system
4. Begin card component migration with DrawnUI
