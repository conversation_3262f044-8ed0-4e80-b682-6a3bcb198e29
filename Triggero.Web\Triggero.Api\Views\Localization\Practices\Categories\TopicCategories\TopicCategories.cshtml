﻿@using Triggero.Models
@using Triggero.Models.Practices
@using Triggero.Models.Practices.Categories
@using Triggero.Web.ViewModels.Localization
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "КАТЕГОРИИ СТАТЕЙ";
    ViewData["ShowLang"] = true;
}
@model LocalizationListVM<TopicCategory>
<!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                    <th scope="col">ID</th>
                    <th scope="col">Название</th>
                    <th scope="col">Языки</th>
                    <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model.Items)
                    {
                         <tr>
                            <th scope="row">#@item.Id</th>
                            <td>@item.Title</td>         
                            <td>Русский @string.Join(' ',item.Localizations.Select(o => o.Language).Select(o => o.Title))</td>
                            <td>
                                <div class="interect">
                                    @if(item.Localizations.Any(o => o.LanguageId == Model.CurrentLanguage.Id))
                                    {
                                        <a asp-action="UpdateTopicCategoryLocalization" asp-controller="TopicCategoriesLocalizations" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    }
                                    else
                                    {
                                        <a asp-action="CreateTopicCategoryLocalization" asp-controller="TopicCategoriesLocalizations" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    } 
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

@*            <div class="mb-3 position-right">
                <a asp-action="CreatePost" asp-controller="Posts" class="button-classic">Добавить</a>
            </div>*@

            <script>
                $(document).ready( function () {
                    $('#tablemanager').DataTable();
                } );
            </script>
        </div>
    </div>
</div>