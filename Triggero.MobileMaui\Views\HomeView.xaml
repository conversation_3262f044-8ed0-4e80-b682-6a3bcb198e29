<?xml version="1.0" encoding="utf-8" ?>
<ContentView x:Class="Triggero.MobileMaui.Views.HomeView"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw">

    <draw:Canvas HorizontalOptions="Fill" VerticalOptions="Fill">
        <draw:SkiaLayout Type="Column" 
                         Padding="20" 
                         Spacing="16"
                         HorizontalOptions="Fill" 
                         VerticalOptions="Fill">

            <!-- Header -->
            <draw:SkiaLabel Text="Home"
                            FontSize="24"
                            FontAttributes="Bold"
                            TextColor="#007ACC"
                            HorizontalOptions="Center" />

            <!-- Placeholder content -->
            <draw:SkiaShape BackgroundColor="#F0F0F0"
                            CornerRadius="12"
                            Padding="16"
                            HorizontalOptions="Fill">
                
                <draw:SkiaLayout Type="Column" Spacing="8">
                    <draw:SkiaLabel Text="Welcome to Triggero!"
                                    FontSize="18"
                                    FontAttributes="Bold"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="This is the Home view placeholder. Content will be migrated from the original Xamarin HomeView."
                                    FontSize="14"
                                    TextColor="#666666"
                                    MaxLines="3" />
                </draw:SkiaLayout>
                
            </draw:SkiaShape>

            <!-- TODO sections placeholder -->
            <draw:SkiaShape BackgroundColor="#E8F4FD"
                            CornerRadius="12"
                            Padding="16"
                            HorizontalOptions="Fill">
                
                <draw:SkiaLayout Type="Column" Spacing="8">
                    <draw:SkiaLabel Text="📋 Coming Soon:"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    TextColor="#007ACC" />
                    
                    <draw:SkiaLabel Text="• User data rendering section"
                                    FontSize="12"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="• Recommendations display"
                                    FontSize="12"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="• Tasks for today section"
                                    FontSize="12"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="• Need to handle items with custom cards"
                                    FontSize="12"
                                    TextColor="#333333" />
                </draw:SkiaLayout>
                
            </draw:SkiaShape>

            <!-- Platform Services Test -->
            <draw:SkiaButton x:Name="TestButton"
                             Text="Test Platform Services"
                             BackgroundColor="#007ACC"
                             TextColor="White"
                             CornerRadius="8"
                             Padding="12,8"
                             HorizontalOptions="Center"
                             Tapped="OnTestButtonTapped" />

        </draw:SkiaLayout>
    </draw:Canvas>

</ContentView>
