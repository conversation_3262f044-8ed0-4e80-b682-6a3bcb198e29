﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Practices.Categories;
using Triggero.Models.Tests;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Practices;

namespace Triggero.Web.Controllers.Practices
{
    [Authorize]
    public class TestCategoriesController : AbsController
    {
        public TestCategoriesController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("TestCategories")]
        public IActionResult TestCategories()
        {
            var cats = DB.TestCategories.Where(o => !o.IsDeleted).ToList();
            cats.Reverse();
            return View(@"Views\Practices\Categories\TestCategories\TestCategories.cshtml", cats);
        }


        //[Route("CreateTestCategory")]
        public IActionResult CreateTestCategory()
        {
            return View(@"Views\Practices\Categories\TestCategories\CreateTestCategory.cshtml");
        }

        [HttpPost]
        //[Route("CreateTestCategoryPOST"), HttpPost]
        public async Task<IActionResult> CreateTestCategoryPOST([FromForm] TestCategory category)
        {
            category.ImgPath = await SetAttachmentIfHas(category.ImgPath);
            DB.TestCategories.Add(category);
            await DB.SaveChangesAsync();
            return RedirectToAction("TestCategories", "TestCategories");
        }


        //[Route("UpdateTestCategory")]
        public IActionResult UpdateTestCategory(int id)
        {
            var cat = DB.TestCategories.FirstOrDefault(o => o.Id == id);
            return View(@"Views\Practices\Categories\TestCategories\UpdateTestCategory.cshtml", cat);
        }

        [HttpPost]
        //[Route("UpdateTestCategoryPOST"), HttpPost]
        public async Task<IActionResult> UpdateTestCategoryPOST([FromForm] TestCategory category)
        {
            category.ImgPath = await SetAttachmentIfHas(category.ImgPath);
            DB.TestCategories.Update(category);
            await DB.SaveChangesAsync();
            return RedirectToAction("TestCategories", "TestCategories");
        }

        [HttpDelete]
        //[Route("DeleteTestCategory"), HttpDelete]
        public async Task<IActionResult> DeleteTestCategory(int id)
        {
            var cat = DB.TestCategories.FirstOrDefault(o => o.Id == id);
            cat.IsDeleted = true;
            DB.TestCategories.Update(cat);
            await DB.SaveChangesAsync();
            return RedirectToAction("TestCategories", "TestCategories");
        }
    }
}
