﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Practices;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class PracticeLocalizationsController : AbsController
    {
        public PracticeLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/Practices")]
        public IActionResult Practices()
        {
            var posts = DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            posts.Reverse();

            var vm = new LocalizationListVM<Practice>
            {
                Items = posts,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Practices\Practices\Practices.cshtml", vm);
        }





        [Route("Localizations/CreatePracticeLocalization")]
        public IActionResult CreatePracticeLocalization(int id)
        {
            var vm = new LocalizationVM<PracticeLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Practices\Practices\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreatePracticeLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreatePracticeLocalizationPOST([FromForm] LocalizationVM<PracticeLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();
            vm.Localization.AudioPath = await SetAttachmentIfHas(vm.Localization.AudioPath);

            var practice = DB.Practices.FirstOrDefault(o => o.Id == vm.Id);
            practice.Localizations.Add(vm.Localization);

            DB.Practices.Update(practice);
            await DB.SaveChangesAsync();
            return RedirectToAction("Practices", "PracticeLocalizations");
        }





        [Route("Localizations/UpdatePracticeLocalization")]
        public IActionResult UpdatePracticeLocalization(int id)
        {
            var practice = DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<PracticeLocalization>
            {
                Id = id,
                Localization = practice.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Practices\Practices\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdatePracticeLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdatePracticeLocalizationPOST([FromForm] LocalizationVM<PracticeLocalization> vm)
        {
            DB.PracticeLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("Practices", "PracticeLocalizations");
        }

    }
}
