﻿using System.ComponentModel;

namespace Triggero.Web.Enums.InterfaceTexts
{
    public enum UngroupedInterfaceTextType
    {
        [Description("Чат-бот")]
        ChatBotLocalization = 1,
        [Description("Юридические документы")]
        LegalLocalization = 2,
        [Description("Главная страница")]
        MainPageLocalization = 3,
        [Description("Страница поиска")]
        SearchPageLocalization =4,
        [Description("Поддержка")]
        SupportLocalization = 5,
        [Description("Тесты")]
        TestsLocalization = 6
    }
}
