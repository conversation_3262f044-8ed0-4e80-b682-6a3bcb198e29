﻿using MapsterMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Security.Claims;
using Triggero.Application.Abstractions;
using Triggero.Application.Extensions;
using Triggero.Application.Services;
using Triggero.Common.Helpers;
using Triggero.Database;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Dto;
using Triggero.Models.Enums;
using Triggero.Models.General;
using Triggero.Models.Tickets;

namespace Triggero.Domain.Controllers.Rest
{
    /// <summary>
    /// Авторизация и управление профилем
    /// </summary>
    [ApiController]
    [Route("[controller]/[action]")]
    public class AccountController : BaseApiController
    {
        private readonly IAccountManagerService _accountManagerService;
        private readonly SubscriptionService _subscriptions;
        private readonly PaymentsService _payments;
        private readonly UserService _users;


        public AccountController(
            IAccountManagerService accountManagers,
            IMapper mapper,
            SubscriptionService subs,
            UserService users,
            PaymentsService payments,
            DatabaseContext db) : base(db, mapper)
        {
            _subscriptions = subs;
            _accountManagerService = accountManagers;
            _payments = payments;
            _users = users;

        }

        [HttpGet]
        [Route("[controller]/[action]/{mode}")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult> SetTest(int mode)
        {
            var phone = "***********";
            var tel = PhoneConverter.ImportPhoneNumber(phone);

            var user = await DB.Users.FirstOrDefaultAsync(o => o.Phone == tel);

            switch (mode)
            {
                case 0:
                await _subscriptions.SetUserSubscriptionNotPayed(user.GlobalId);
                break;
                case 1:
                await _subscriptions.SetUserSubscriptionTrial(user.GlobalId, DateTime.UtcNow.AddDays(7));
                break;
                case 2:
                await _subscriptions.SetUserSubscriptionFull(user.GlobalId, DateTime.UtcNow.AddYears(50));
                break;
                case 3:
                await _subscriptions.SetUserSubscriptionCustom(user.GlobalId, DateTime.UtcNow.AddMonths(1),
                    new[] { PlanOptionType.Library });
                break;
            }
            return Ok();
        }

        private IQueryable<User> IncludeUserData()
        {
            return DB.Users.Include(o => o.Avatar)
                .Include(o => o.NotificationSettings)
                .Include(o => o.UserSubscription).ThenInclude(o => o.UserSubsctiptionOptions).ThenInclude(o => o.PlanOption).ThenInclude(o => o.Localizations)
                .Include(o => o.UserStatistics).ThenInclude(o => o.ExercisePassingResults)
                .Include(o => o.UserStatistics).ThenInclude(o => o.TestPassingResults)
                .Include(o => o.UserStatistics).ThenInclude(o => o.PracticePassingResults)
                .Include(o => o.UserStatistics).ThenInclude(o => o.BreathPracticePassingResults);
        }

        /// <summary>
        /// Легаси, будем убирать
        /// </summary>
        /// <returns></returns>
        [Authorize(Policy = "TokenOnly")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(User))]
        public async Task<ActionResult> MyData()
        {
            var globalIdClaim = User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(globalIdClaim))
                return Unauthorized();

            var user = await IncludeUserData().FirstOrDefaultAsync(x => x.GlobalId == globalIdClaim);

            FillPropertiesForOut(user);

            return Ok(user);
        }

        /// <summary>
        /// Современный метод
        /// </summary>
        /// <returns></returns>
        [Authorize(Policy = "TokenOnly")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(UserDataDto))]
        public async Task<ActionResult> MyProfile()
        {
            var user = await GetAuthorizedUser();
            if (user != null)
            {//
                return Ok(await ExportUserProfile(user.Id));
            }
            return BadRequest();
        }

        private void FillPropertiesForOut(User user)
        {
            user.Password = "";

            if (user.Avatar == null)
            {

            }

            if (user.UserStatistics != null)
            {
                user.UserStatistics.ExercisePassingResultsCount = user.UserStatistics.ExercisePassingResults.Count;
                user.UserStatistics.TestPassingResultsCount = user.UserStatistics.TestPassingResults.Count;
                user.UserStatistics.PracticePassingResultsCount = user.UserStatistics.PracticePassingResults.Count;
                user.UserStatistics.BreathPracticePassingResultsCount = user.UserStatistics.BreathPracticePassingResults.Count;

                user.UserStatistics.ExercisePassingResults.Clear();
                user.UserStatistics.TestPassingResults.Clear();
                user.UserStatistics.PracticePassingResults.Clear();
                user.UserStatistics.BreathPracticePassingResults.Clear();
            }
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(LoginResponseDto))]
        [HttpPost]
        public async Task<ActionResult> LoginWithEmail([FromBody] LoginWithEmailDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _accountManagerService.LoginAsync(model);

            return Ok(result.Data);
        }

        protected async Task<UserDataDto> ExportUserProfile(int userId)
        {
            await _payments.CheckAndApplyPayments(userId);

            var user = DB.Users
                .Include(i => i.Avatar)
                .Include(i => i.UserSubscription)
                .ThenInclude(t => t.UserSubsctiptionOptions)
                .ThenInclude(t => t.PlanOption)
                .Include(i => i.Payments)
                .First(f => f.Id == userId);

            return await Mapper.MapAsync<UserDataDto>(user);
        }

        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(LoginResponseDto))]
        [HttpPost]
        public async Task<ActionResult> LoginWithPhone([FromBody] LoginWithPhoneNumberDto model)
        {
            if (!ModelState.IsValid)
            {
                return BadRequest(ModelState);
            }

            var result = await _accountManagerService.LoginAsync(model);

            return Ok(result.Data);
        }

        [ProducesResponseType(StatusCodes.Status200OK)]
        [HttpGet]
        public async Task<ActionResult> RequestSms(string phone)
        {
            await _accountManagerService.RequestSmsCode(phone);

            return Ok();
        }

        [HttpPost]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(LoginResponseDto))]
        public async Task<ActionResult> Register([FromBody] RegisterUserDto model)
        {
            try
            {
                if (!ModelState.IsValid)
                {
                    return BadRequest(ModelState);
                }

                var result = await _accountManagerService.RegisterAsync(model);

                if (result.Succeeded)
                {
                    return Ok(result.Data);
                }

                return Ok(new ServerResponseDto()
                {
                    Error = result.Errors.FirstOrDefault()
                });
            }
            catch (Exception ex)
            {
                return BadRequest(ex.Message);
            }
        }

        [Authorize(Policy = "TokenOnly")]
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult> ChangeMyPassword(string password)
        {
            var user = await GetAuthorizedUser();
            if (user != null)
            {
                user.Password = password;
                DB.Users.Update(user);
                await DB.SaveChangesAsync();

                return Ok();
            }
            return Unauthorized();

        }

        [Authorize(Policy = "TokenOnly")]
        [HttpGet]
        [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(List<Ticket>))]
        public async Task<ActionResult> MyTickets()
        {
            var user = await GetAuthorizedUser();
            if (user == null)
                return Unauthorized();

            return Ok(DB.Users
                .Include(o => o.Tickets)
                .ThenInclude(o => o.Messages)
                .ThenInclude(o => o.Attachments)
                .FirstOrDefault(o => o.Id == user.Id && !o.IsDeleted)
                .Tickets);
        }

        [Authorize(Policy = "TokenOnly")]
        [HttpDelete]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult> MyAccount()
        {
            var user = await GetAuthorizedUser();
            if (user == null)
                return Unauthorized();

            user.IsDeleted = true;

            DB.Users.Update(user);
            await DB.SaveChangesAsync();

            return Ok();
        }

        [Authorize(Policy = "TokenOnly")]
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult> MyAvatar(int avatarId)
        {
            var user = await GetAuthorizedUser();
            if (user == null)
                return Unauthorized();

            user.AvatarId = avatarId;

            DB.Users.Update(user);
            await DB.SaveChangesAsync();

            return Ok();
        }

        [Authorize(Policy = "TokenOnly")]
        [HttpPut]
        [ProducesResponseType(StatusCodes.Status200OK)]
        public async Task<ActionResult> MyName(string name)
        {
            var user = await GetAuthorizedUser();
            if (user == null)
                return Unauthorized();

            user.Name = name;
            DB.Users.Update(user);
            await DB.SaveChangesAsync();

            return Ok();
        }

        [Authorize(Policy = "TokenOnly")]
        [ProducesResponseType(StatusCodes.Status200OK)]
        [HttpPut]
        public async Task<ActionResult> MyEmailToSendReports(string email)
        {
            var user = await GetAuthorizedUser();
            if (user == null)
                return Unauthorized();

            var result = await _users.SetEmailToSendReports(user.GlobalId, email);
            if (result.Succeeded)
                return Ok();

            return BadRequest(result.Errors.FirstOrDefault());
        }


    }

}
