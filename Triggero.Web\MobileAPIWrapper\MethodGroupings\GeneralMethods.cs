﻿using MobileAPIWrapper.Methods.General;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MobileAPIWrapper.MethodGroupings
{
    public class GeneralMethods
    {
        public UserMethods UserMethods { get; set; } = new UserMethods();
        public VerificationMethods VerificationMethods { get; set; } = new VerificationMethods();
    }
}
