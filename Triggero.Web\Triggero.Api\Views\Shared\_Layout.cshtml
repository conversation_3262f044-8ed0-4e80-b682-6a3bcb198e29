﻿<html>
    <head>
        <title>ADMIN - @ViewData["Title"]</title>
        
        <base href="~/admin/" />

        <!-- META -->
        <meta charset="UTF-8">
        <meta http-equiv="X-UA-Compatible" content="IE=edge">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">

        <!-- CSS -->
        <link rel="stylesheet" href="~/media/css/bootstrap.min.css">
    <link rel="stylesheet" href="~/media/css/main.css" asp-append-version="true">
        <link rel="stylesheet" href="~/media/css/jquery.dataTables.css">
        <link rel="stylesheet" href="~/media/css/trumbowyg.min.css">
        <link rel="stylesheet" href="~/media/css/style.css">
        <link rel="stylesheet" href="~/media/css/croppie.css">

        <!-- JS -->
        <script src="~/media/js/jquery-3.6.0.min.js"></script>
        <script src="https://cdn.jsdelivr.net/npm/@@popperjs/core@2.11.5/dist/umd/popper.min.js" integrity="sha384-Xe+8cL9oJa6tN/veChSP7q+mnSPaj5Bcu9mPX5F5xIGE0DVittaqT5lorf0EI7Vk" crossorigin="anonymous"></script>
        <script src="~/media/js/bootstrap.min.js"></script>
        <script src="~/media/js/jquery.dataTables.js"></script>
        <script src="~/media/js/trumbowyg.min.js"></script>
        <script src="~/media/js/plugins/noembed/trumbowyg.noembed.min.js"></script>
        <script src="~/media/js/croppie.js"></script>
        
        <link rel="stylesheet" href="~/Triggero.Api.styles.css" asp-append-version="true" />
        
        <link rel="stylesheet" href="~/css/MudBlazor.min.css" />

        <!-- FONT AWESOME 6 -->
        <link rel="stylesheet" href="~/media/fontawesome/css/all.min.css">
        <script src="~/media/fontawesome/js/all.min.js"></script>

        @RenderSection("css",false)

    </head>
    <body>
        <div class="container-fluid">
            <div class="row">
                <!-- SIDEBAR -->
                <div class="col-md-3 col-lg-2 sidebar flex-shrink-0 p-3 bg-white">
                    <a href="/" class="logotype-panel">ПАНЕЛЬ УПРАВЛЕНИЯ СЕРВИСОМ</a>
                <ul class="nav nav-pills flex-column mb-auto nav-center">

                    <li class="nav-item">
                        <a href="/" class="nav-link link-dark" aria-current="page">
                            <i class="fa-solid fa-house-chimney mnright-10"></i>
                            Главная
                        </a>
                    </li>
                    @*<li>
                            <a asp-action="Tickets" asp-controller="Tickets" class="nav-link active" aria-current="page">
                                <i class="fa-solid fa-comments mnright-10"></i>
                                Тикеты
                            </a>
                        </li>*@

                    <li class="nav-item">
                        <a asp-action="Users" asp-controller="Stats" class="nav-link link-dark">
                            <i class="fa-solid fa-users mnright-10"></i>
                            Пользователи
                        </a>
                    </li>

                    @* TARIFFS *@
                    <li>
                        <button class="btn btn-toggle d-inline-flex align-items-center rounded border-0 collapsed nav-link link-dark" data-bs-toggle="collapse" data-bs-target="#account-collapse3" aria-expanded="false">
                            <i class="fa-solid fa-language mnright-10"></i> Тарифы
                        </button>
                        <div class="collapse" id="account-collapse3">
                            <ul class="btn-toggle-nav list-unstyled fw-normal pb-1 small">
                                <li>
                                    <a asp-action="Plans" asp-controller="Plans" class="nav-link link-dark">
                                        @*  <i class="fa-solid fa-brain mnright-10"></i>*@
                                        Тарифы
                                    </a>
                                </li>
                                <li>
                                    <a asp-action="PlansOptions" asp-controller="PlansOptions" class="nav-link link-dark">
                                        @*     <i class="fa-solid fa-meteor mnright-10"></i>*@
                                        Тарифные опции
                                    </a>
                                </li>
                                @* <li>
                                        <a asp-action="Promocodes" asp-controller="Promocodes" class="nav-link link-dark">
                                            <i class="fa-solid fa-meteor mnright-10"></i>
                                            Промокоды
                                        </a>
                                    </li>*@
                            </ul>
                        </div>

                    </li>
                    @* STATS *@
                    <li>
                        <button class="btn btn-toggle d-inline-flex align-items-center rounded border-0 collapsed nav-link link-dark" data-bs-toggle="collapse" data-bs-target="#account-collapse-stats" aria-expanded="false">
                            <i class="fa-solid fa-language mnright-10"></i> Статистика
                        </button>
                        <div class="collapse" id="account-collapse-stats">
                            <ul class="btn-toggle-nav list-unstyled fw-normal pb-1 small">
                                <li>
                                    <a asp-action="Users" asp-controller="Stats" class="nav-link link-dark">
                                        @*  <i class="fa-solid fa-brain mnright-10"></i>*@
                                        Пользователи
                                    </a>
                                </li>
                                <li>
                                    <a asp-action="Registrations" asp-controller="Stats" class="nav-link link-dark">
                                        @*     <i class="fa-solid fa-meteor mnright-10"></i>*@
                                        Регистрации
                                    </a>
                                </li>
                                @* <li>
                                <a asp-action="Promocodes" asp-controller="Promocodes" class="nav-link link-dark">
                                <i class="fa-solid fa-meteor mnright-10"></i>
                                Промокоды
                                </a>
                                </li>*@
                            </ul>
                        </div>

                    </li>
                    @* LIBRARY *@
                    <li>
                        <button class="btn btn-toggle d-inline-flex align-items-center rounded border-0 collapsed nav-link link-dark" data-bs-toggle="collapse" data-bs-target="#account-collapse2" aria-expanded="false">
                            <i class="fa-solid fa-language mnright-10"></i> Библиотека
                        </button>
                        <div class="collapse" id="account-collapse2">
                            <ul class="btn-toggle-nav list-unstyled fw-normal pb-1 small">
                                <li>
                                    <a asp-action="Exercises" asp-controller="Exercises" class="nav-link link-dark">
                                        @*  <i class="fa-solid fa-brain mnright-10"></i>*@
                                        Упражнения
                                    </a>
                                </li>
                                <li>
                                    <a asp-action="Practices" asp-controller="Practices" class="nav-link link-dark">
                                        @*     <i class="fa-solid fa-meteor mnright-10"></i>*@
                                        Практики
                                    </a>
                                </li>
                                <li>
                                    <a asp-action="Topics" asp-controller="Topic" class="nav-link link-dark">
                                        @* <i class="fa-solid fa-meteor mnright-10"></i>*@
                                        Статьи
                                    </a>
                                </li>
                                <li>
                                    <a asp-action="BreathPractices" asp-controller="BreathPractices" class="nav-link link-dark">
                                        @*                <i class="fa-solid fa-meteor mnright-10"></i>*@
                                        "Дыши"
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    @* CATS *@
                    <li>
                        <button class="btn btn-toggle d-inline-flex align-items-center rounded border-0 collapsed nav-link link-dark" data-bs-toggle="collapse" data-bs-target="#account-collapse" aria-expanded="false">
                            <i class="fa-solid fa-language mnright-10"></i> Категории
                        </button>
                        <div class="collapse" id="account-collapse">
                            <ul class="btn-toggle-nav list-unstyled fw-normal pb-1 small">
                                <li>
                                    <a asp-action="ExerciseCategories" asp-controller="ExerciseCategories" class="nav-link link-dark">
                                        @* <i class="fa-solid fa-newspaper mnright-10"></i>*@
                                        Категории упражнений
                                    </a>
                                </li>
                                <li>
                                    <a asp-action="PracticeCategories" asp-controller="PracticeCategories" class="nav-link link-dark">
                                        @*  <i class="fa-solid fa-newspaper mnright-10"></i>*@
                                        Категории практик
                                    </a>
                                </li>
                                <li>
                                    <a asp-action="TopicCategories" asp-controller="TopicCategories" class="nav-link link-dark">
                                        @* <i class="fa-solid fa-newspaper mnright-10"></i>*@
                                        Категории статей
                                    </a>
                                </li>
                                <li>
                                    <a asp-action="TestCategories" asp-controller="TestCategories" class="nav-link link-dark">
                                        @* <i class="fa-solid fa-newspaper mnright-10"></i>*@
                                        Категории тестов
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    <li>
                        <button class="btn btn-toggle d-inline-flex align-items-center rounded border-0 collapsed nav-link link-dark" data-bs-toggle="collapse" data-bs-target="#account-collapse4" aria-expanded="false">
                            <i class="fa-solid fa-language mnright-10"></i> Трекер настроения
                        </button>
                        <div class="collapse" id="account-collapse4">
                            <ul class="btn-toggle-nav list-unstyled fw-normal pb-1 small">
                                <li>
                                    <a asp-action="Factors" asp-controller="Factors" class="nav-link link-dark">
                                        @* <i class="fa-solid fa-newspaper mnright-10"></i>*@
                                        Факторы
                                    </a>
                                </li>
                                <li>
                                    <a asp-action="FactorDetails" asp-controller="FactorDetails" class="nav-link link-dark">
                                        @*  <i class="fa-solid fa-newspaper mnright-10"></i>*@
                                        Подпункты факторов
                                    </a>
                                </li>
                            </ul>
                        </div>
                    </li>
                    @* <li>
                            <a asp-action="Posts" asp-controller="Posts" class="nav-link link-dark">
                                <i class="fa-solid fa-newspaper mnright-10"></i>
                                Новости
                            </a>
                        </li>*@
                    <li>
                        <a asp-action="Tests" asp-controller="Tests" class="nav-link link-dark">
                            <i class="fa-solid fa-newspaper mnright-10"></i>
                            Тесты
                        </a>
                    </li>
                    <li>
                        <a asp-action="ToDoListItems" asp-controller="ToDoListItems" class="nav-link link-dark">
                            <i class="fa-solid fa-newspaper mnright-10"></i>
                            TODO лист
                        </a>
                    </li>
                   @*  <li>
                        <a href="/users.html" class="nav-link link-dark">
                            <i class="fa-solid fa-users mnright-10"></i>
                            Пользователи
                        </a>
                    </li> *@

                    <li>
                        <a asp-action="UserAvatars" asp-controller="UserAvatars" class="nav-link link-dark">
                            <i class="fa-solid fa-users mnright-10"></i>
                            Аватары для пользователей
                        </a>
                    </li>
                    @*   <li>
                            <a asp-action="VideoBGs" asp-controller="VideoBg" class="nav-link link-dark">
                                <i class="fa-solid fa-users mnright-10"></i>
                                Видеофоны
                            </a>
                        </li>*@
                    <li class="nav-item">
                        <a asp-action="Chats" asp-controller="ChatBot" class="nav-link link-dark" aria-current="page">
                            <i class="fa-solid fa-house-chimney mnright-10"></i>
                            Чаты из чат-ботов
                        </a>
                    </li>
                    <li class="nav-item">
                        <a asp-action="Chats" asp-controller="SupportChat" class="nav-link link-dark" aria-current="page">
                            <i class="fa-solid fa-house-chimney mnright-10"></i>
                            Чаты из техподдержки
                        </a>
                    </li>
                    <li>
                        <a asp-action="AllPages" asp-controller="AllPagesInterface" class="nav-link link-dark">
                            <i class="fa-solid fa-language mnright-10"></i>
                            Текста интерфейсов (Русский)
                        </a>
                    </li>
                    <li>
                        <a asp-action="Languages" asp-controller="Languages" class="nav-link link-dark">
                            <i class="fa-solid fa-language mnright-10"></i>
                            Локализация
                        </a>
                    </li>
                </ul>

                   @* <div class="sidebar-button">
                        <div class="mb-3">
                            <button class="button-classic button-white button-width"><i class="fa-solid fa-gears"></i> Настройки</button>
                        </div>
                    </div>*@
                </div>

                <!-- CONTENT -->
                <main class="col-md-9 ms-sm-auto col-lg-10 px-md-5">
                    <!-- NAVIGATION -->
                    <nav class="navbar navbar-expand-lg">
                        <div class="container-fluid content-margin">
                          <h4 class="navbar-brand">@ViewData["Title"]</h4>
                          <div class="collapse navbar-collapse" id="navbarText">
                            <ul class="navbar-nav me-auto mb-2 mb-lg-0">
                              
                            </ul>
                            <span class="navbar-text">
                                <a asp-action="Logout" asp-controller="Auth" class="button-exit">Выход</a>
                            </span>
                          </div>
                        </div>

                    </nav>
                     @if(ViewData.ContainsKey("ShowLang")){
                        <h6>Текущий язык: <span id="lang_name_span"></span></h6>
                     }
                
                    @RenderBody()
                  
                </main>
            </div>
        </div>

        
        <!-- Удаление элемента -->
        <div class="modal fade" id="close" data-bs-backdrop="static" data-bs-keyboard="false" tabindex="-1" aria-labelledby="staticBackdropLabel" aria-hidden="true">
            <div class="modal-dialog modal-dialog-centered">
              <div class="modal-content">
                <div class="modal-header">
                    Удаление записи
                </div>
                <div class="modal-body">
                    <div class="content">
                        <div class="icon-content">
                            <i class="fa-solid fa-trash"></i>
                        </div>
                        <div class="text-content" id="delete_msg">
                            Вы действительно хотите удалить запись?
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="button-classic" id="delete_item_yes_btn">Да</button>
                    <button class="button-classic" data-bs-dismiss="modal" aria-label="Close">Нет</button>
                </div>
              </div>
            </div>
        </div>

    @*Модальное окно подверждения удаления*@
    <script>

        async function confirmDeleting(deleteFunction) {
            $("#close").modal('show');
            //document.getElementById('deleting_main_block').removeAttribute('hidden');
            //document.getElementById('deleting_success_block').style.setProperty('display', 'none');
            //document.getElementById('deleting_error_block').style.setProperty('display', 'none');

            document.getElementById('delete_item_yes_btn').setAttribute('onclick', `deleteDirectly(${deleteFunction})`);
        }
        async function confirmDeletingWithRedirect(deleteFunction, message, redirectTo) {
            $("#close").modal('show');
            //document.getElementById('deleting_main_block').removeAttribute('hidden');
            //document.getElementById('deleting_success_block').style.setProperty('display', 'none');
            //document.getElementById('deleting_error_block').style.setProperty('display', 'none');

            document.getElementById('delete_item_yes_btn').setAttribute('onclick', `deleteDirectlyWithRedirect(${deleteFunction},'${redirectTo}')`);
            document.getElementById('delete_msg').innerHTML = message;
        }




        async function deleteDirectly(deleteFunction) {
            await deleteFunction;

            await new Promise(r => setTimeout(r, 750));
            document.location.reload();
        }

        async function deleteDirectlyWithRedirect(deleteFunction, redirectTo) {
           await deleteFunction;
           await new Promise(r => setTimeout(r, 750));

           document.location.href = document.location.origin + redirectTo;
        }


    </script>


    <script>
        function getCurrentLocalizationLang(){
            fetch(document.location.origin + `/GetLanguageCookie`)
                .then((response) => response.json())
                .then((data) =>{
                    if(data){
                    document.getElementById('lang_name_span').innerHTML = data.title;
                    }
                });
        }
         
        @if(ViewData.ContainsKey("ShowLang")){
            @("getCurrentLocalizationLang();")
        }
   

    </script>

    @RenderSection("js",false)

        

    <script>

         //Croppie

         let croppies = new Map();


        function initImageCroppie(cropId, inputFileId, closeFileId) {

            let image_crop = $(`#${cropId}`).croppie({
                enableExif: true,
                viewport: {
                    width: document.getElementById(cropId).clientWidth - 20,
                    height: document.getElementById(cropId).clientHeight - 20,
                    type: 'square'
                },
                boundary: {
                    width: 100000,
                    height: 100000,
                },
                enforceBoundary : false,
                showZoomer: false,
                enableResize: true,
                enableOrientation: true,
                mouseWheelZoom: 'ctrl'
            });

            __initImageCroppie(image_crop, cropId, inputFileId, closeFileId);
        }

        function initImageCroppieSized(cropId, inputFileId, closeFileId,width,height) {

            let image_crop = $(`#${cropId}`).croppie({
                enableExif: true,
                viewport: {
                    width: width,
                    height: height,
                    type: 'square'
                },
                boundary: {
                    width: 100000,
                    height: 100000,
                },
                enforceBoundary: false,
                showZoomer: false,
                enableResize: true,
                enableOrientation: true,
                mouseWheelZoom: 'ctrl'
            });

            __initImageCroppie(image_crop,cropId, inputFileId, closeFileId);
        }
        
        function __initImageCroppie(image_crop, cropId, inputFileId, closeFileId) {


            $(`#${inputFileId}`).change(function (ev) {
                //    $(`#${cropId}`).css('pointer-events', 'none');

                $(`#${closeFileId}`).css('display', 'inline-flex');

                $(`#${cropId} > .cr-boundary`).css('display', 'flex');
                $(`#${cropId} > .text-fileupload`).css('display', 'none');
            });


            document.querySelector(`#${inputFileId}`).addEventListener("change", function () {
                if (this.files[0]) {
                    var reader = new FileReader();
                    reader.onload = function (e) {
                        bindCrop(cropId, e.target.result);
                    }
                    reader.readAsDataURL(this.files[0]);
                    $(`#${cropId}`).attr('type', 'text');
                }
            });


            $(`#${closeFileId}`).on('click', function () {
                $(`#${inputFileId}`).val('');
                $(`#${cropId}`).css('pointer-events', 'auto');
                $(`#${cropId} > .text-fileupload`).css('display', 'flex');

                $(`#${cropId}`).attr('type', 'file');

                $(`#${cropId}`).croppie('destroy');
                image_crop = $(`#${cropId}`).croppie({
                    enableExif: true,
                    viewport: {
                        width: document.getElementById(cropId).clientWidth - 20,
                        height: 370,
                        type: 'square'
                    },
                    boundary: {
                        width: 3300,
                        height: 3300
                    },
                    enforceBoundary: true,
                    showZoomer: true,
                    enableResize: true,
                    enableOrientation: true,
                    mouseWheelZoom: 'ctrl'
                });
            });


            croppies[cropId] = image_crop;

        }


        function bindCrop(cropId,url) {

            console.log(url);
            croppies[cropId].croppie('bind', {
                url: url
            }).then(function () {

                $(`#${cropId}`).css('background', 'url()');

                console.log('jQuery bind complete');
            });
        }

        async function bindCropStart(cropId,url) {


            let base64 = await suckDickAndGetBase64(url);

            console.log(base64);
            croppies[cropId].croppie('bind', {
                url: base64
            }).then(function () {

                console.log($(`#${cropId}`).croppie('get'));

                $(`#${cropId}`).css('background', 'url()');

                console.log('jQuery bind complete');
            });
        }

        async function suckDickAndGetBase64(url) {
            const data = await fetch(url);
            const blob = await data.blob();
            const reader = new FileReader();

            return new Promise((resolve) => {
                const reader = new FileReader();
                reader.readAsDataURL(blob);
                reader.onloadend = () => {
                    const base64data = reader.result;
                    resolve(base64data);
                };
            });
        }


        async function cropFinal(inputFileId, cropId) {

            let result = await croppies[cropId].croppie('result',
                {
                    type: 'base64',
                    size: 'viewport',
                    format: 'png',
                    quality: 1,
                    circle: false
                });
            console.log(result);

            let file = await urltoFile(result, 'up.png', 'image/png');
            console.log(file);

            // Создаем коллекцию файлов:
            var dt = new DataTransfer();
            dt.items.add(file);
            var file_list = dt.files;

            document.getElementById(inputFileId).files = file_list;
        }

        function urltoFile(url, filename, mimeType) {
            return (fetch(url)
                .then(function (res) { return res.arrayBuffer(); })
                .then(function (buf) { return new File([buf], filename, { type: mimeType }); })
            );
        }

        function setImgToOtherInput(inputFileId, newInputFileId) {

            var files = document.getElementById(inputFileId).files;


            // Создаем коллекцию файлов:
            var dt = new DataTransfer();
            for(let i=0;i<files.length;i++){
               
                dt.items.add(files[i]);
            }
            document.getElementById(newInputFileId).files = dt.files;
        }



    </script>

    <script src="~/js/MudBlazor.min.js"></script>

    </body>
</html>