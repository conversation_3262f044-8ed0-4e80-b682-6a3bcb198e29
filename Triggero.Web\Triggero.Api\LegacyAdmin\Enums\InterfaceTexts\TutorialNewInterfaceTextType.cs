﻿using System.ComponentModel;

namespace Triggero.Web.Enums.InterfaceTexts
{
    public enum TutorialNewInterfaceTextType
    {
        [Description("Полное обучение - страница 1")]
        TutorialNewPage1Localization = 1,
        [Description("Полное обучение - страница 2")]
        TutorialNewPage2Localization = 2,
        [Description("Полное обучение - страница 3")]
        TutorialNewPage3Localization = 3,
        [Description("Полное обучение - страница 4")]
        TutorialNewPage4Localization = 4,
        [Description("Полное обучение - страница 5")]
        TutorialNewPage5Localization = 5,
        [Description("Полное обучение - страница 6")]
        TutorialNewPage6Localization = 6,
        [Description("Полное обучение - страница 7")]
        TutorialNewPage7Localization = 7,
        [Description("Полное обучение - страница 8")]
        TutorialNewPage8Localization = 8,
        [Description("Полное обучение - страница 9")]
        TutorialNewPage9Localization = 9,
        [Description("Полное обучение - страница 10")]
        TutorialNewPage10Localization = 10,
        [Description("Полное обучение - страница 11")]
        TutorialNewPage11Localization = 11,
        [Description("Полное обучение - страница 12")]
        TutorialNewPage12Localization = 12,
    }
}
