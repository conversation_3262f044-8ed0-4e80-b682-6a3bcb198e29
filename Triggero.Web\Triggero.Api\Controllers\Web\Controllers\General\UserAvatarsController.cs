﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models.General;
using Triggero.Web.Abstractions;

namespace Triggero.Web.Controllers.General
{
    [Authorize]
    public class UserAvatarsController : AbsController
    {
        public UserAvatarsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("UserAvatars")]
        public IActionResult UserAvatars()
        {
            var avatars = DB.UserAvatars.Where(o => !o.IsDeleted).ToList();
            avatars.Reverse();
            return View(@"Views\General\UserAvatars\UserAvatars.cshtml", avatars);
        }


        [Route("CreateUserAvatar")]
        public IActionResult CreateUserAvatar()
        {
            return View(@"Views\General\UserAvatars\CreateAvatar.cshtml");
        }
        [Route("CreateUserAvatarPOST"), HttpPost]
        public async Task<IActionResult> CreateUserAvatarPOST([FromForm] UserAvatar avatar)
        {
            avatar.AvatarPath = await SetAttachmentIfHas(avatar.AvatarPath);
            DB.UserAvatars.Add(avatar);
            await DB.SaveChangesAsync();
            return RedirectToAction("UserAvatars", "UserAvatars");
        }



        [Route("DeleteUserAvatar"), HttpDelete]
        public async Task<IActionResult> DeleteUserAvatar(int id)
        {
            var avatar = DB.UserAvatars.FirstOrDefault(o => o.Id == id);
            avatar.IsDeleted = true;
            DB.UserAvatars.Update(avatar);
            await DB.SaveChangesAsync();
            return RedirectToAction("UserAvatars", "UserAvatars");
        }
    }
}
