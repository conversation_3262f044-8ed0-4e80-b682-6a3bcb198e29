/**
 * Trumbowyg v2.26.0 - A lightweight WYSIWYG editor
 * Trumbowyg plugin stylesheet
 * ------------------------
 * @link http://alex-d.github.io/Trumbowyg
 * @license MIT
 * <AUTHOR> (Alex-D)
 *         Twitter : @AlexandreDemode
 *         Website : alex-d.fr
 */

[formulas] {
  position: relative;
  display: inline-block;
  pointer-events: none; }
  [formulas][inline="false"] {
    display: block;
    width: 100%; }
  [formulas]::after {
    content: '\270E';
    position: absolute;
    top: 0;
    right: 0;
    bottom: 0;
    left: 0;
    opacity: 0;
    background-color: rgba(255, 255, 255, 0.83);
    -webkit-box-shadow: 0 0 5px 5px rgba(255, 255, 255, 0.83);
            box-shadow: 0 0 5px 5px rgba(255, 255, 255, 0.83);
    cursor: pointer;
    pointer-events: auto; }
  [formulas]:hover::after {
    opacity: 1; }
