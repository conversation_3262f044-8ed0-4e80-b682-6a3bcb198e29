﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Localization
@using Triggero.Models.Localization.Practices
@using Triggero.Web.ViewModels.Localization
@using Triggero.Web.ViewModels.Localization.Tests;
@{
    ViewData["Title"] =  $"ЛОКАЛИЗАЦИЯ ТЕСТА";
    ViewData["ShowLang"] = true;
}
@model TestStartPageLocalizationVM

 <!-- CONTENT -->
<form asp-action="CreateStartPageLocalizationPOST" asp-controller="TestStartPageLocalization" method="post" enctype="multipart/form-data" class="content">

    <input type="hidden" asp-for="Id" value="@Model.Id" />


    <div class="row">
        <div class="page">
            
            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Localization.Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Текст</label>
                <textarea id="editor-input" required asp-for="Localization.Description" rows="3"></textarea>
                <script>
                    $('#editor-input')
                        .trumbowyg({
                            btnsDef: {
                                // Create a new dropdown
                                image: {
                                    dropdown: ['insertImage', 'noembed'],
                                    ico: 'insertImage'
                                }
                            },
                            // Redefine the button pane
                            btns: [
                                ['viewHTML'],
                                ['formatting'],
                                ['strong', 'em', 'del'],
                                ['superscript', 'subscript'],
                                ['link'],
                                ['image'], // Our fresh created dropdown
                                ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
                                ['unorderedList', 'orderedList'],
                                ['horizontalRule'],
                                ['removeformat'],
                                ['fullscreen']
                            ]
                        });
                </script>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Основные теги</label>
                <input type="text" asp-for="Localization.MainTags" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Вторичные теги</label>
                <input type="text" asp-for="Localization.SecondaryTags" required class="form-control" placeholder="">
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>

            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                        <th scope="col">Вопрос</th>
                        <th scope="col">Языки</th>
                        <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model.Questions)
                    {
                        <tr>
                            <td>@item.Text</td>
                            <td>Русский @string.Join(' ',item.Localizations.Select(o => o.Language).Select(o => o.Title))</td>
                            <td>
                                <div class="interect">
                                    @if (item.Localizations.Any(o => o.LanguageId == Model.CurrentLanguage.Id))
                                    {
                                        <a asp-action="UpdateQuestionLocalization" asp-controller="TestQuestionsLocalization" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    }
                                    else
                                    {
                                        <a asp-action="CreateQuestionLocalization" asp-controller="TestQuestionsLocalization" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    }
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

             <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                        <th scope="col">Результат</th>
                        <th scope="col">Описание</th>
                        <th scope="col">Языки</th>
                        <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model.Results)
                    {
                        <tr>
                            <td>@item.Title</td>
                            <td>@item.Text</td>
                            <td>Русский @string.Join(' ',item.Localizations.Select(o => o.Language).Select(o => o.Title))</td>
                            <td>
                                <div class="interect">
                                    @if (item.Localizations.Any(o => o.LanguageId == Model.CurrentLanguage.Id))
                                    {
                                        <a asp-action="UpdateResultLocalization" asp-controller="TestResultsLocalization" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    }
                                    else
                                    {
                                        <a asp-action="CreateResultLocalization" asp-controller="TestResultsLocalization" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    }
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>


            <script>
                $(document).ready(function () {
                    $('.table').DataTable();
                });
            </script>


        </div>
    </div>

   

</form>