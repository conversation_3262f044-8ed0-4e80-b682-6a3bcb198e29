﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;

namespace Triggero.Domain.Controllers.Rest.Library
{
    [ApiController]
    [Route("[controller]")]
    public class PracticesController : ApiController
    {
        public PracticesController(DatabaseContext db) : base(db)
        {
        }

        [ResponseCache(Duration = 120)]
        [HttpGet, Route("GetPracticeCategories")]
        public async Task<List<PracticeCategory>> GetPracticeCategories()
        {
            return DB.PracticeCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .ToList();
        }


        [ResponseCache(Duration = 120)]
        [HttpGet, Route("GetPractices")]
        public async Task<List<Practice>> GetPractices()
        {
            return DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .ToList();
        }

        [ResponseCache(VaryByQueryKeys = new[] { "count", "offset" }, Duration = 120)]
        [HttpGet, Route("GetPracticesChunk")]
        public async Task<List<Practice>> GetPracticesChunk(int count, int offset)
        {
            return DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Skip(offset).Take(count)
                .ToList();
        }


        [ResponseCache(VaryByQueryKeys = new[] { "categoryId" }, Duration = 120)]
        [HttpGet, Route("GetPracticesByCategory")]
        public async Task<List<Practice>> GetPracticesByCategory(int categoryId)
        {
            return DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden && o.CategoryId == categoryId)
                .ToList();
        }

        [ResponseCache(VaryByQueryKeys = new[] { "categoryId", "count", "offset" }, Duration = 120)]
        [HttpGet, Route("GetPracticesByCategoryChunk")]
        public async Task<List<Practice>> GetPracticesByCategoryChunk(int categoryId, int count, int offset)
        {
            return DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden && o.CategoryId == categoryId)
                .Skip(offset).Take(count)
                .ToList();
        }


        [ResponseCache(VaryByQueryKeys = new[] { "tag" }, Duration = 120)]
        [HttpGet, Route("GetPracticesByTag")]
        public async Task<List<Practice>> GetPracticesByTag(string tag)
        {
            return DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.MainTags.Equals(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Equals(tag, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }


        [ResponseCache(VaryByQueryKeys = new[] { "id" }, Duration = 120)]
        [HttpGet, Route("GetPractice")]
        public async Task<Practice> GetPractice(int id)
        {
            return await DB.Practices
                .Include(o => o.Localizations)
                .ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .FirstOrDefaultAsync(o => o.Id == id);
        }


        [HttpPut, Route("AddWatch")]
        public async Task AddWatch(int practiceId)
        {
            var practice = DB.Practices.FirstOrDefault(o => o.Id == practiceId);
            practice.Watches++;

            DB.Practices.Update(practice);
            await DB.SaveChangesAsync();
        }
    }
}
