﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;
using System.Collections.Generic;
using System.Dynamic;
using Triggero.Database;
using Triggero.Models.Abstractions;
using Triggero.Models.Localization.Interface;
using Triggero.Models.Localization.Interface.Auth;
using Triggero.Models.Localization.Interface.Library;
using Triggero.Models.Localization.Interface.MoodTracker;
using Triggero.Models.Localization.Interface.Profile;
using Triggero.Models.Localization.Interface.Start;
using Triggero.Models.Localization.Interface.Subscriptions;
using Triggero.Models.Localization.Interface.Tutorial;
using Triggero.Models.Localization.Interface.TutorialNew;
using Triggero.Web.Abstractions;
using Triggero.Web.Enums.InterfaceTexts;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{

    /// <summary>
    /// Эта хуйня работает так
    /// К методу обращаемся. Если локализация с id == 0, то создаем локализацию
    /// Если нет, то делаем апдейт.
    /// Универсальная хуйня, хуле
    /// </summary>
    [Authorize]
    public class CreateUpdInterfaceController : AbsController
    {
        public CreateUpdInterfaceController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }


        [Route("Interface/AuthPOST"), HttpPost]
        public async Task<IActionResult> AuthPOST(AuthInterfaceTextType type)
        {
            JToken item = (JToken)GenerateObjectFromForm();

            switch (type)
            {
            case AuthInterfaceTextType.AccountRegisteredLocalization:
            DB.AccountRegisteredLocalizations.Update(item.ToObject<AccountRegisteredLocalization>());
            break;
            case AuthInterfaceTextType.CreateAccountLocalization:
            DB.CreateAccountLocalizations.Update(item.ToObject<CreateAccountLocalization>());
            break;
            case AuthInterfaceTextType.EmailForgotPasswordCodePageLocalization:
            DB.EmailForgotPasswordCodePageLocalizations.Update(item.ToObject<EmailForgotPasswordCodePageLocalization>());
            break;
            case AuthInterfaceTextType.EmailForgotPasswordLocalization:
            DB.EmailForgotPasswordLocalizations.Update(item.ToObject<EmailForgotPasswordLocalization>());
            break;
            case AuthInterfaceTextType.EnterSMSCodeLocalization:
            DB.EnterSMSCodeLocalizations.Update(item.ToObject<EnterSMSCodeLocalization>());
            break;
            case AuthInterfaceTextType.LoginByEmailLocalization:
            DB.LoginByEmailLocalizations.Update(item.ToObject<LoginByEmailLocalization>());
            break;
            case AuthInterfaceTextType.LoginByPhoneLocalization:
            DB.LoginByPhoneLocalizations.Update(item.ToObject<LoginByPhoneLocalization>());
            break;
            case AuthInterfaceTextType.LoginMainPageLocalization:
            DB.LoginMainPageLocalizations.Update(item.ToObject<LoginMainPageLocalization>());
            break;
            }

            return await SaveAndRedirectToStartPage();
        }


        [Route("Interface/LibraryPOST"), HttpPost]
        public async Task<IActionResult> LibraryPOST(LibraryInterfaceTextType type)
        {
            JToken item = (JToken)GenerateObjectFromForm();

            switch (type)
            {
            case LibraryInterfaceTextType.BreathPracticeInterfaceLocalization:
            DB.BreathPracticeInterfaceLocalizations.Update(item.ToObject<BreathPracticeInterfaceLocalization>());
            break;
            case LibraryInterfaceTextType.EmotionsLocalization:
            DB.EmotionsLocalizations.Update(item.ToObject<EmotionsLocalization>());
            break;
            case LibraryInterfaceTextType.LibraryLocalization:
            DB.LibraryLocalizations.Update(item.ToObject<LibraryLocalization>());
            break;
            }
            return await SaveAndRedirectToStartPage();
        }


        [Route("Interface/MoodtrackerPOST"), HttpPost]
        public async Task<IActionResult> MoodtrackerPOST(MoodTrackerInterfaceTextType type)
        {
            JToken item = (JToken)GenerateObjectFromForm();

            switch (type)
            {
            case MoodTrackerInterfaceTextType.TrackerFactorsDetailsPageLocalization:
            DB.TrackerFactorsDetailsPageLocalizations.Update(item.ToObject<TrackerFactorsDetailsPageLocalization>());
            break;
            case MoodTrackerInterfaceTextType.TrackerFactorsPageLocalization:
            DB.TrackerFactorsPageLocalizations.Update(item.ToObject<TrackerFactorsPageLocalization>());
            break;
            case MoodTrackerInterfaceTextType.TrackerFinalPageLocalization:
            DB.TrackerFinalPageLocalizations.Update(item.ToObject<TrackerFinalPageLocalization>());
            break;
            case MoodTrackerInterfaceTextType.TrackerGeneralLocalization:
            DB.TrackerGeneralLocalizations.Update(item.ToObject<TrackerGeneralLocalization>());
            break;
            case MoodTrackerInterfaceTextType.TrackerHowAreYouLocalization:
            DB.TrackerHowAreYouLocalizations.Update(item.ToObject<TrackerHowAreYouLocalization>());
            break;
            case MoodTrackerInterfaceTextType.TrackerMainPageLocalization:
            DB.TrackerMainPageLocalizations.Update(item.ToObject<TrackerMainPageLocalization>());
            break;
            case MoodTrackerInterfaceTextType.TrackerStartLocalization:
            DB.TrackerStartLocalizations.Update(item.ToObject<TrackerStartLocalization>());
            break;
            }
            return await SaveAndRedirectToStartPage();
        }


        [Route("Interface/ProfilePOST"), HttpPost]
        public async Task<IActionResult> ProfilePOST(ProfileInterfaceTextType type)
        {
            JToken item = (JToken)GenerateObjectFromForm();

            switch (type)
            {
            case ProfileInterfaceTextType.ProfileDownloadDataLocalization:
            DB.ProfileDownloadDataLocalizations.Update(item.ToObject<ProfileDownloadDataLocalization>());
            break;
            case ProfileInterfaceTextType.ProfileEnterEmailLocalization:
            DB.ProfileEnterEmailLocalizations.Update(item.ToObject<ProfileEnterEmailLocalization>());
            break;
            case ProfileInterfaceTextType.ProfileMainLocalization:
            DB.ProfileMainLocalizations.Update(item.ToObject<ProfileMainLocalization>());
            break;
            case ProfileInterfaceTextType.ProfileNotificationsLocalization:
            DB.ProfileNotificationsLocalizations.Update(item.ToObject<ProfileNotificationsLocalization>());
            break;
            case ProfileInterfaceTextType.ProfileSelectPeriodLocalization:
            DB.ProfileSelectPeriodLocalizations.Update(item.ToObject<ProfileSelectPeriodLocalization>());
            break;
            case ProfileInterfaceTextType.ProfileSelectTimeLocalization:
            DB.ProfileSelectTimeLocalizations.Update(item.ToObject<ProfileSelectTimeLocalization>());
            break;

            }
            return await SaveAndRedirectToStartPage();
        }


        [Route("Interface/StartPOST"), HttpPost]
        public async Task<IActionResult> StartPOST(StartInterfaceTextType type)
        {
            JToken item = (JToken)GenerateObjectFromForm();

            switch (type)
            {
            case StartInterfaceTextType.HelloPageLocalization:
            DB.HelloPageLocalizations.Update(item.ToObject<HelloPageLocalization>());
            break;
            case StartInterfaceTextType.HelloSliderPageLocalization:
            DB.HelloSliderPageLocalizations.Update(item.ToObject<HelloSliderPageLocalization>());
            break;
            case StartInterfaceTextType.LetsStartPageLocalization:
            DB.LetsStartPageLocalizations.Update(item.ToObject<LetsStartPageLocalization>());
            break;
            }
            return await SaveAndRedirectToStartPage();
        }


        [Route("Interface/SubscriptionPOST"), HttpPost]
        public async Task<IActionResult> SubscriptionPOST(SubscriptionInterfaceTextType type)
        {
            JToken item = (JToken)GenerateObjectFromForm();

            switch (type)
            {
            case SubscriptionInterfaceTextType.SelectSubscriptionLocalization:
            DB.SelectSubscriptionLocalizations.Update(item.ToObject<SelectSubscriptionLocalization>());
            break;
            case SubscriptionInterfaceTextType.SubscriptionEndedLocalization:
            DB.SubscriptionEndedLocalizations.Update(item.ToObject<SubscriptionEndedLocalization>());
            break;
            case SubscriptionInterfaceTextType.SubscriptionMainLocalization:
            DB.SubscriptionMainLocalizations.Update(item.ToObject<SubscriptionMainLocalization>());
            break;
            case SubscriptionInterfaceTextType.TrialPageLocalization:
            DB.TrialPageLocalizations.Update(item.ToObject<TrialPageLocalization>());
            break;
            case SubscriptionInterfaceTextType.TrialStartedLocalization:
            DB.TrialStartedLocalizations.Update(item.ToObject<TrialStartedLocalization>());
            break;
            }
            return await SaveAndRedirectToStartPage();
        }


        [Route("Interface/TutorialPOST"), HttpPost]
        public async Task<IActionResult> TutorialPOST(TutorialInterfaceTextType type)
        {
            JToken item = (JToken)GenerateObjectFromForm();

            switch (type)
            {
            case TutorialInterfaceTextType.StartTutorialPageLocalization:
            DB.StartTutorialPageLocalizations.Update(item.ToObject<StartTutorialPageLocalization>());
            break;
            case TutorialInterfaceTextType.TutorialCompletedPageLocalization:
            DB.TutorialCompletedPageLocalizations.Update(item.ToObject<TutorialCompletedPageLocalization>());
            break;
            }
            return await SaveAndRedirectToStartPage();
        }



        [Route("Interface/TutorialNewPOST"), HttpPost]
        public async Task<IActionResult> TutorialNewPOST(TutorialNewInterfaceTextType type)
        {
            JToken item = (JToken)GenerateObjectFromForm();

            switch (type)
            {
            case TutorialNewInterfaceTextType.TutorialNewPage1Localization:
            DB.TutorialNewPage1Localizations.Update(item.ToObject<TutorialNewPage1Localization>());
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage2Localization:
            DB.TutorialNewPage2Localizations.Update(item.ToObject<TutorialNewPage2Localization>());
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage3Localization:
            DB.TutorialNewPage3Localizations.Update(item.ToObject<TutorialNewPage3Localization>());
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage4Localization:
            DB.TutorialNewPage4Localizations.Update(item.ToObject<TutorialNewPage4Localization>());
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage5Localization:
            DB.TutorialNewPage5Localizations.Update(item.ToObject<TutorialNewPage5Localization>());
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage6Localization:
            DB.TutorialNewPage6Localizations.Update(item.ToObject<TutorialNewPage6Localization>());
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage7Localization:
            DB.TutorialNewPage7Localizations.Update(item.ToObject<TutorialNewPage7Localization>());
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage8Localization:
            DB.TutorialNewPage8Localizations.Update(item.ToObject<TutorialNewPage8Localization>());
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage9Localization:
            DB.TutorialNewPage9Localizations.Update(item.ToObject<TutorialNewPage9Localization>());
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage10Localization:
            DB.TutorialNewPage10Localizations.Update(item.ToObject<TutorialNewPage10Localization>());
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage11Localization:
            DB.TutorialNewPage11Localizations.Update(item.ToObject<TutorialNewPage11Localization>());
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage12Localization:
            DB.TutorialNewPage12Localizations.Update(item.ToObject<TutorialNewPage12Localization>());
            break;
            }
            return await SaveAndRedirectToStartPage();
        }




        [Route("Interface/UngroupedPOST"), HttpPost]
        public async Task<IActionResult> UngroupedPOST(UngroupedInterfaceTextType type)
        {
            JToken item = (JToken)GenerateObjectFromForm();

            switch (type)
            {
            case UngroupedInterfaceTextType.ChatBotLocalization:
            DB.ChatBotLocalizations.Update(item.ToObject<ChatBotLocalization>());
            break;
            case UngroupedInterfaceTextType.LegalLocalization:
            DB.LegalLocalizations.Update(item.ToObject<LegalLocalization>());
            break;
            case UngroupedInterfaceTextType.MainPageLocalization:
            DB.MainPageLocalizations.Update(item.ToObject<MainPageLocalization>());
            break;
            case UngroupedInterfaceTextType.SearchPageLocalization:
            DB.SearchPageLocalizations.Update(item.ToObject<SearchPageLocalization>());
            break;
            case UngroupedInterfaceTextType.SupportLocalization:
            DB.SupportLocalizations.Update(item.ToObject<SupportLocalization>());
            break;
            case UngroupedInterfaceTextType.TestsLocalization:
            DB.TestsLocalizations.Update(item.ToObject<TestsLocalization>());
            break;
            }
            return await SaveAndRedirectToStartPage();
        }




        private object GenerateObjectFromForm()
        {
            ExpandoObject jsonExpando = new ExpandoObject();

            foreach (var key in Request.Form.Keys)
            {
                jsonExpando.TryAdd(key, Request.Form[key].ToString());
            }

            JObject jObj = JObject.FromObject(jsonExpando);

            return JsonConvert.DeserializeObject(jObj.ToString());
        }

        private async Task<IActionResult> SaveAndRedirectToStartPage()
        {
            await DB.SaveChangesAsync();

            if (Request.Form.Keys.Contains("LanguageId"))
            {
                var langFromForm = Request.Form["LanguageId"].ToString();
                if (!string.IsNullOrEmpty(langFromForm))
                {
                    return RedirectToAction("AllPages", "AllPagesInterface");
                }
            }
            return RedirectToAction("AllPagesByLang", "AllPagesInterface");
        }


    }
}
