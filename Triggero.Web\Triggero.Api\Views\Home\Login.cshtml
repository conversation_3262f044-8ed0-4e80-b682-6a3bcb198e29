﻿@using CRMWeb.ViewModels.Home
@{
    Layout = null;
}
@model LoginVM

<html>
<head>
    <title>ADMIN - АВТОРИЗАЦИЯ</title>

    <!-- META -->
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

    <!-- CSS -->
    <link rel="stylesheet" href="~/media/css/bootstrap.min.css">
    <link rel="stylesheet" href="~/media/css/main.css">

    <!-- JS -->
    <script src="~/media/js/jquery-3.6.0.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/@@popperjs/core@2.11.5/dist/umd/popper.min.js" integrity="sha384-Xe+8cL9oJa6tN/veChSP7q+mnSPaj5Bcu9mPX5F5xIGE0DVittaqT5lorf0EI7Vk" crossorigin="anonymous"></script>
    <script src="~/media/js/bootstrap.min.js"></script>
</head>
<body>
    <div class="container">
        <form id="authForm" asp-action="Auth" asp-controller="Auth" method="post" class="auth">
            <div class="titleauth">
                АВТОРИЗАЦИЯ В СИСТЕМЕ
            </div>
            <div class="mb-3">
                <label for="" class="formlabel">Логин</label>
                <input type="text" id="authLogin" asp-for="Login" class="form-control" placeholder="Введите логин">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Пароль</label>
                <input type="password" id="password-input" asp-for="Password" class="form-control" placeholder="Введите пароль">
            </div>

           @* <div class="mb-3">
                <div class="form-check">
                    <input class="form-check-input" type="checkbox" value="" id="flexCheckDefault">
                    <label class="form-check-label" for="flexCheckDefault">
                        Default checkbox
                    </label>
                </div>
            </div>*@

            <div class="mb-3">
                <button type="button" onclick="tryAuth()" class="button-classic button-width">Войти</button>
            </div>

            <h5 id="authError" style="text-align:center;color:red;"></h5>
        </form>
    </div>


    <script>
        async function tryAuth() {

            let login = document.getElementById('authLogin').value;
            let password = document.getElementById('password-input').value;

            await fetch(`Auth/HasUser?login=${login}&password=${password}`)
                .then((response) => {
                    return response.json();
                })
                .then((data) => {
                    console.log(data);
                    if (data === true) {
                        document.getElementById('authForm').submit();
                    }
                    else {
                        document.getElementById('authError').innerHTML = "Неверный логин или пароль";
                    }
                });
        }

    </script>

</body>
</html>