﻿
@{
    ViewData["Title"] = "ЛОКАЛИЗАЦИЯ";
    ViewData["ShowLang"] = true;
}
 <!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="col-md-2">
            <a href="/" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-house"></i>
                    </div>
                    <div class="name">Главные текста</div>
                </div>
            </a>
        </div>

     @*   <div class="col-md-2">
            <a href="/" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-comments"></i>
                    </div>
                    <div class="name">Чат</div>
                </div>
            </a>
        </div>*@

        <div class="col-md-2">
            <a asp-controller="PlanLocalizations" asp-action="Plans" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-bolt"></i>
                    </div>
                    <div class="name">Тарифы</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="PlanOptionsLocalizations" asp-action="PlanOptions" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-bolt"></i>
                    </div>
                    <div class="name">Тарифные опции</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="ExerciseLocalizations" asp-action="Exercises" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-brain"></i>
                    </div>
                    <div class="name">Упражнения</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="PracticeLocalizations" asp-action="Practices" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-meteor"></i>
                    </div>
                    <div class="name">Практики</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="TopicLocalizations" asp-action="Topics" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-meteor"></i>
                    </div>
                    <div class="name">Статьи</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="BreathPracticeLocalizations" asp-action="BreathPractices" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-meteor"></i>
                    </div>
                    <div class="name">Дыши</div>
                </div>
            </a>
        </div>


        <div class="col-md-2">
            <a asp-controller="PostLocalizations" asp-action="Posts" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-newspaper"></i>
                    </div>
                    <div class="name">Новости</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="ExerciseCategoriesLocalizations" asp-action="ExercisesCategories" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-users"></i>
                    </div>
                    <div class="name">Категории упраженений</div>
                </div>
            </a>
        </div>

         <div class="col-md-2">
            <a asp-controller="PracticeCategoriesLocalizations" asp-action="PracticeCategories" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-users"></i>
                    </div>
                    <div class="name">Категории практик</div>
                </div>
            </a>
        </div>

          <div class="col-md-2">
            <a asp-controller="TopicCategoriesLocalizations" asp-action="TopicCategories" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-users"></i>
                    </div>
                    <div class="name">Категории статей</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="TestCategoriesLocalizations" asp-action="TestCategories" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-users"></i>
                    </div>
                    <div class="name">Категории тестов</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="TestStartPageLocalization" asp-action="Tests" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-users"></i>
                    </div>
                    <div class="name">Тесты</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="NeedToHandleLocalizations" asp-action="NeedToHandle" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-users"></i>
                    </div>
                    <div class="name">Пункты 'Нужно проработать'</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="FactorLocalizations" asp-action="Factors" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-users"></i>
                    </div>
                    <div class="name">Факторы</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="FactorDetailLocalizations" asp-action="FactorDetails" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-users"></i>
                    </div>
                    <div class="name">Подпункты факторов</div>
                </div>
            </a>
        </div>

        <div class="col-md-2">
            <a asp-controller="AllPagesInterface" asp-action="AllPagesByLang" class="lang">
                <div class="content">
                    <div class="iconlang">
                        <i class="fa-solid fa-users"></i>
                    </div>
                    <div class="name">Текста интерфейсов</div>
                </div>
            </a>
        </div>

    </div>
</div>