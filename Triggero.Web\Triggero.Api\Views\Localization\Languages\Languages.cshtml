﻿@using Triggero.Models
@using Triggero.Models.General
@using Triggero.Models.Practices
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "ЛОКАЛИЗАЦИЯ";
}
@model List<Language>
<!-- CONTENT -->
<div class="content">
    <div class="row">


        @foreach (var item in Model)
        {
             <div class="col-md-2">
                <a asp-action="LangMainPage" asp-controller="Languages" asp-route-langId="@item.Id" class="lang">
                    <div class="content">
                        <div class="flag" style="background: url('@item.ImgPath'); background-size: 100% 100%;"></div>
                        <div class="name">@item.Title</div>
                    </div>
                </a>
            </div>
        }

        <div class="col-md-2">
            <a asp-action="CreateLanguage" asp-controller="Languages" class="lang">
                <div class="content">
                    <div class="flag"></div>
                    <div class="name">Новый язык</div>
                </div>
            </a>
        </div>
    </div>
</div>

<script>

</script>