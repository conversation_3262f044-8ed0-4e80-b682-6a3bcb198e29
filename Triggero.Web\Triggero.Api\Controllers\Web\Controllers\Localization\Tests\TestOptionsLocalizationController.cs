﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Tests;
using Triggero.Models.Localization.Tests.QuestionOptions;
using Triggero.Models.Tests;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;
using Triggero.Web.ViewModels.Localization.Tests;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class TestOptionsLocalizationController : AbsController
    {
        public TestOptionsLocalizationController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        #region SimpleOptions

        [Route("Localizations/Tests/CreateSimpleOptionLocalization")]
        public IActionResult CreateSimpleOptionLocalization(int id)
        {
            var vm = new LocalizationVM<SimpleQuestionOptionLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Tests\Options\Simple\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/Tests/CreateSimpleOptionLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateSimpleOptionLocalizationPOST([FromForm] LocalizationVM<SimpleQuestionOptionLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();
            var post = DB.SimpleQuestionOptions.FirstOrDefault(o => o.Id == vm.Id);
            post.Localizations.Add(vm.Localization);

            DB.SimpleQuestionOptions.Update(post);
            await DB.SaveChangesAsync();
            return RedirectToAction("Tests", "TestStartPageLocalization");
        }





        [Route("Localizations/Tests/UpdateSimpleOptionLocalization")]
        public IActionResult UpdateSimpleOptionLocalization(int id)
        {
            var post = DB.SimpleQuestionOptions
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<SimpleQuestionOptionLocalization>
            {
                Id = id,
                Localization = post.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id) as SimpleQuestionOptionLocalization
            };
            return View(@"Views\Localization\Tests\Options\Simple\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/Tests/UpdateSimpleOptionLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateSimpleOptionLocalizationPOST([FromForm] LocalizationVM<SimpleQuestionOptionLocalization> vm)
        {
            DB.SimpleQuestionOptionLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("Tests", "TestStartPageLocalization");
        }
        #endregion
    }
}
