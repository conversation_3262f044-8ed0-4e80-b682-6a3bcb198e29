﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.General.Influence;
using Triggero.Models.Localization;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class NeedToHandleLocalizationsController : AbsController
    {
        public NeedToHandleLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Localizations/NeedToHandle")]
        public IActionResult NeedToHandle()
        {
            var items = DB.NeedToHandle
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            items.Reverse();

            var vm = new LocalizationListVM<NeedToHandle>
            {
                Items = items,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\NeedToHandle\NeedToHandle.cshtml", vm);
        }

        //[Route("Localizations/CreateNeedToHandleLocalization")]
        public IActionResult CreateNeedToHandleLocalization(int id)
        {
            var vm = new LocalizationVM<NeedToHandleLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\NeedToHandle\CreateLocalization.cshtml", vm);
        }

        [HttpPost]
        //[Route("Localizations/CreateNeedToHandleLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateNeedToHandleLocalizationPOST([FromForm] LocalizationVM<NeedToHandleLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();
            var item = DB.NeedToHandle.FirstOrDefault(o => o.Id == vm.Id);
            item.Localizations.Add(vm.Localization);

            DB.NeedToHandle.Update(item);
            await DB.SaveChangesAsync();
            return RedirectToAction("NeedToHandle", "NeedToHandleLocalizations");
        }


        //[Route("Localizations/UpdateNeedToHandleLocalization")]
        public IActionResult UpdateNeedToHandleLocalization(int id)
        {
            var item = DB.NeedToHandle
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<NeedToHandleLocalization>
            {
                Id = id,
                Localization = item.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\NeedToHandle\UpdateLocalization.cshtml", vm);
        }

        [HttpPost]
        //[Route("Localizations/UpdateNeedToHandleLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateNeedToHandleLocalizationPOST([FromForm] LocalizationVM<NeedToHandleLocalization> vm)
        {
            DB.NeedToHandleLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("NeedToHandle", "NeedToHandleLocalizations");
        }

    }
}
