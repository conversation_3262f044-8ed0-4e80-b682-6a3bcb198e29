﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc.ApplicationModels;
using Microsoft.AspNetCore.Mvc.Controllers;
using Microsoft.OpenApi.Models;
using Swashbuckle.AspNetCore.SwaggerGen;
using System.Globalization;
using System.Reflection;
using Triggero.Api;
using Triggero.Domain.Models.Dto;

namespace Triggero.Domain.Extensions
{
    public class ApiExplorerConvention : IActionModelConvention
    {
        public void Apply(ActionModel action)
        {
            var included = action.Controller.DisplayName.Contains(".Rest");
            action.ApiExplorer.IsVisible = included;
        }
    }

    public static class SwaggerExtensions
    {
        static DateTime GetLinkerTime(Assembly assembly)
        {
            const string BuildVersionMetadataPrefix = "+build";
            var attribute = assembly.GetCustomAttribute<AssemblyInformationalVersionAttribute>();
            if (attribute?.InformationalVersion != null)
            {
                var value = attribute.InformationalVersion;
                var index = value.IndexOf(BuildVersionMetadataPrefix);
                if (index > 0)
                {
                    value = value[(index + BuildVersionMetadataPrefix.Length)..];
                    return DateTime.ParseExact(value, "yyyy-MM-ddTHH:mm:ss:fffZ", CultureInfo.InvariantCulture);
                }
            }

            return default;
        }



        public static IServiceCollection AddSwaggerForApi(this IServiceCollection services)
        {
            // Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
            services.AddEndpointsApiExplorer();


            services.AddSwaggerGen(c =>
            {
                c.DocInclusionPredicate((docName, apiDesc) =>
                {
                    var ns = ((ControllerActionDescriptor)apiDesc.ActionDescriptor).ControllerTypeInfo.Namespace;

                    return !ns.Contains("Triggero.Web") && !ns.Contains("Pages");
                });

                c.UseInlineDefinitionsForEnums();
                c.UseAllOfToExtendReferenceSchemas();

                c.SwaggerDoc("v1", new OpenApiInfo
                {
                    Title = "Triggero API",
                    Version = "v1",
                    Description = $"Обновлено {GetLinkerTime(Assembly.GetExecutingAssembly()).ToLocalTime():f}",
                });

                // To Enable authorization using Swagger (JWT)    
                c.AddSecurityDefinition("Bearer", new OpenApiSecurityScheme()
                {
                    Name = "Authorization",
                    Type = SecuritySchemeType.ApiKey,
                    Scheme = "Bearer",
                    BearerFormat = "JWT",
                    In = ParameterLocation.Header,
                    Description = "Введите 'Bearer' [пробел] и затем вставьте JWT-токен.\r\n\r\nПример: \"Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9\"",
                });

                c.AddSecurityRequirement(new OpenApiSecurityRequirement
                {
            {
                new OpenApiSecurityScheme
                {
                    Reference = new OpenApiReference
                    {
                        Type = ReferenceType.SecurityScheme,
                        Id = "Bearer"
                    }
                },
                Array.Empty<string>()
            }
                });

                // Filter out controllers from the web project assembly
                var apiAssembly = typeof(Program).Assembly;
                c.DocInclusionPredicate((docName, apiDesc) =>
                {
                    if (apiDesc.RelativePath != null && apiDesc.RelativePath.Contains("hydro"))
                        return false;

                    var assemblyName = ((ControllerActionDescriptor)apiDesc.ActionDescriptor).ControllerTypeInfo.Assembly.GetName().Name;
                    return assemblyName == apiAssembly.GetName().Name;
                });

                //// Optionally filter by namespace if assemblies are the same
                //c.TagActionsBy(apiDesc =>
                //{
                //    var controllerActionDescriptor = (ControllerActionDescriptor)apiDesc.ActionDescriptor;
                //    return new[] { controllerActionDescriptor.ControllerTypeInfo.Namespace };
                //});

                // include API xml documentation
                c.IncludeXmlComments(GetXmlDocumentationFileFor(apiAssembly), includeControllerXmlComments: true);

                // include models xml documentation
                var modelsAssembly = typeof(ListRequestDto).Assembly;
                c.IncludeXmlComments(GetXmlDocumentationFileFor(modelsAssembly));
            });

            return services;
        }

        private static string GetXmlDocumentationFileFor(Assembly assembly)
        {
            var documentationFile = $"{assembly.GetName().Name}.xml";
            var path = Path.Combine(AppContext.BaseDirectory, documentationFile);
            return path;
        }

    }

    public class AuthorizationOperationFilter : IOperationFilter
    {
        public void Apply(OpenApiOperation operation, OperationFilterContext context)
        {
            // Check if the controller or the action have the [Authorize] attribute
            var isAuthorized = context.MethodInfo.DeclaringType.GetCustomAttributes(true).OfType<AuthorizeAttribute>().Any()
                               || context.MethodInfo.GetCustomAttributes(true).OfType<AuthorizeAttribute>().Any();

            if (!isAuthorized) return;

            // If the [Authorize] attribute exists, add the "Authorization" header to the Swagger description
            operation.Responses.Add("401", new OpenApiResponse { Description = "Unauthorized" });
            operation.Responses.Add("403", new OpenApiResponse { Description = "Forbidden" });

            operation.Security = new List<OpenApiSecurityRequirement>
            {
                new OpenApiSecurityRequirement
                {
                    [new OpenApiSecurityScheme { Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "Bearer" } }] = new string[] { }
                }
            };
        }
    }

}
