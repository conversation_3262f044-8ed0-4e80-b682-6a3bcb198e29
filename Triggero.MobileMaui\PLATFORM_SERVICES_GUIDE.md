# Platform Services Migration Guide

## Overview
This guide covers the completed migration from Xamarin DependencyService to MAUI Dependency Injection pattern for platform services.

## Architecture Pattern

### Before (Xamarin)
```csharp
// Registration
[assembly: Dependency(typeof(PlatformUi))]

// Usage
var platformUi = DependencyService.Get<IPlatformUi>();
```

### After (MAUI)
```csharp
// Registration (in MauiProgram.cs)
builder.AddPlatformServices();

// Usage
var platformUi = ServiceHelper.PlatformUi;
// OR via DI
var platformUi = serviceProvider.GetService<IPlatformUi>();
```

## Service Interfaces

### IPlatformUi
**Location**: `Abstractions/IPlatformUi.cs`

**Purpose**: Platform-specific UI operations
- Screen metrics and safe area calculations
- Status bar control
- URL opening
- Theme application
- Navigation bar styling

**Key Properties**:
- `Screen Screen { get; }` - DrawnUI screen information
- Uses `DrawnUi.Models.Screen` for consistent screen data

**Key Methods**:
- `void Init(params object[] args)` - Platform initialization
- `void ApplyTheme()` - Apply platform theme
- `bool OpenUrl(string url)` - Open external URLs
- `void HideStatusBar()` / `void ShowStatusBar()` - Status bar control
- `void Command(string command)` - Platform-specific commands

### IToastMessage
**Location**: `Abstractions/IToastMessage.cs`

**Purpose**: Cross-platform toast notifications
- `void ShortAlert(string message)` - Short duration toast
- `void LongAlert(string message, ToastPosition position)` - Long duration with positioning

**ToastPosition Enum**: Top, Center, Bottom

## Platform Implementations

### Windows Platform
**Files**: 
- `Platforms/Windows/PlatformUi.cs`
- `Platforms/Windows/ToastMessage.cs`

**Characteristics**:
- Debug logging for development
- Reasonable defaults for screen metrics
- No-op implementations for mobile-specific features (status bar)
- TODO comments for future Windows-specific implementations

**Screen Defaults**:
```csharp
Screen.Density = 1.0f;
Screen.WidthDip = 1920;
Screen.HeightDip = 1080;
Screen.TopInset = 0; // No status bar
```

### Android Platform
**Files**:
- `Platforms/Android/PlatformUi.cs`
- `Platforms/Android/ToastMessage.cs`

**Key Features**:
- Native Android Toast implementation
- Status bar and navigation bar styling
- Safe area inset calculations using WindowInsets
- Color conversion using `.ToPlatform()` extension

**Important Patterns**:
```csharp
// Color conversion
Activity.Window.SetNavigationBarColor(colorBar.ToPlatform());

// Application context (avoid namespace conflicts)
Toast.MakeText(global::Android.App.Application.Context, message, ToastLength.Short);
```

### iOS Platform
**Files**:
- `Platforms/iOS/PlatformUi.cs`
- `Platforms/iOS/ToastMessage.cs`

**Key Features**:
- UIAlertController-based toast with timer management
- UIApplication status bar control
- Safe area detection using UIApplication.SharedApplication
- Toast queue system for multiple alerts

## Service Registration

### PlatformServicesExtensions.cs
**Location**: `Services/PlatformServicesExtensions.cs`

**Pattern**: Conditional compilation for platform-specific registration
```csharp
public static MauiAppBuilder AddPlatformServices(this MauiAppBuilder builder)
{
#if ANDROID
    builder.Services.AddSingleton<IPlatformUi, Platforms.Android.PlatformUi>();
    builder.Services.AddSingleton<IToastMessage, Platforms.Android.ToastMessage>();
#elif IOS
    builder.Services.AddSingleton<IPlatformUi, Platforms.iOS.PlatformUi>();
    builder.Services.AddSingleton<IToastMessage, Platforms.iOS.ToastMessage>();
#elif WINDOWS
    builder.Services.AddSingleton<IPlatformUi, Platforms.Windows.PlatformUi>();
    builder.Services.AddSingleton<IToastMessage, Platforms.Windows.ToastMessage>();
#endif
    return builder;
}
```

### ServiceHelper.cs
**Purpose**: Compatibility layer providing DependencyService-like access

```csharp
public static class ServiceHelper
{
    public static IPlatformUi PlatformUi => 
        Application.Current?.Handler?.MauiContext?.Services?.GetService<IPlatformUi>()
        ?? throw new InvalidOperationException("IPlatformUi service not registered");
        
    public static IToastMessage ToastMessage => 
        Application.Current?.Handler?.MauiContext?.Services?.GetService<IToastMessage>()
        ?? throw new InvalidOperationException("IToastMessage service not registered");
}
```

## Integration Points

### MauiProgram.cs
```csharp
public static MauiApp CreateMauiApp()
{
    var builder = MauiApp.CreateBuilder();
    builder
        .UseMauiApp<App>()
        .AddPlatformServices(); // Add this line
    
    return builder.Build();
}
```

### App.xaml.cs
```csharp
public partial class App : Application
{
    // Compatibility property for existing code
    public static IPlatformUi PlatformUi => ServiceHelper.PlatformUi;
}
```

## Migration Checklist

### For Future Code Migration:
1. **Find DependencyService.Get<T>() calls** throughout codebase
2. **Replace with ServiceHelper.ServiceName** or DI injection
3. **Update service interfaces** if needed for MAUI compatibility
4. **Test on all target platforms** (Windows, Android, iOS)
5. **Verify service registration** in MauiProgram.cs

### Common Issues:
- **Namespace conflicts**: Use `global::` prefix for platform types
- **Color conversion**: Use `.ToPlatform()` instead of `.ToAndroid()/.ToiOS()`
- **Application context**: Be specific about Android vs MAUI Application
- **Read-only properties**: Some Xamarin patterns need adjustment for MAUI

## Testing
- **Windows**: Fast development and testing
- **Android**: Cross-platform verification
- **iOS**: Final platform validation
- **Build commands**: Use specific target framework builds for testing
