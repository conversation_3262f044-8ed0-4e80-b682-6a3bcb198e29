# Platform Services Migration Guide

## Overview
This document outlines the migration from Xamarin.Forms DependencyService pattern to .NET MAUI dependency injection for platform services.

## What Was Migrated

### 1. IPlatformUi Service
**Original Xamarin Usage:**
```csharp
var platformUi = DependencyService.Get<IPlatformUi>();
platformUi.HideStatusBar();
```

**New MAUI Usage:**
```csharp
// Option 1: Direct service access
var platformUi = ServiceHelper.PlatformUi;
platformUi.HideStatusBar();

// Option 2: Via App property (maintains compatibility)
App.PlatformUi.HideStatusBar();

// Option 3: Constructor injection (recommended for new code)
public class MyViewModel
{
    private readonly IPlatformUi _platformUi;
    
    public MyViewModel(IPlatformUi platformUi)
    {
        _platformUi = platformUi;
    }
}
```

### 2. IToastMessage Service
**Original Xamarin Usage:**
```csharp
DependencyService.Get<IToastMessage>().Short<PERSON>lert("Hello!");
```

**New MAUI Usage:**
```csharp
ServiceHelper.ToastMessage.ShortAlert("Hello!");
```

## Files Created

### Abstractions
- `Abstractions/IPlatformUi.cs` - Platform UI interface
- `Abstractions/IToastMessage.cs` - Toast message interface  
- `Abstractions/ToastPosition.cs` - Toast position enum

### Platform Implementations
- `Platforms/Android/PlatformUi.cs` - Android platform UI implementation
- `Platforms/Android/ToastMessage.cs` - Android toast implementation
- `Platforms/iOS/PlatformUi.cs` - iOS platform UI implementation
- `Platforms/iOS/ToastMessage.cs` - iOS toast implementation

### Service Registration
- `Services/PlatformServicesExtensions.cs` - MAUI service registration and helper

## Key Changes Made

### 1. Service Registration (MauiProgram.cs)
```csharp
public static MauiApp CreateMauiApp()
{
    var builder = MauiApp.CreateBuilder();
    builder
        .UseMauiApp<App>()
        .AddPlatformServices() // <- New platform services registration
        .ConfigureFonts(fonts => { ... });
    
    return builder.Build();
}
```

### 2. Platform-Specific Registration
Services are automatically registered based on compilation target:
- Android: Uses Android implementations
- iOS: Uses iOS implementations  
- MacCatalyst: not using
- Windows: TODO - needs implementation

### 3. Service Access Helper
Created `ServiceHelper` class to provide easy access similar to DependencyService:
```csharp
public static class ServiceHelper
{
    public static IPlatformUi PlatformUi => GetService<IPlatformUi>();
    public static IToastMessage ToastMessage => GetService<IToastMessage>();
}
```

## TODO Items for Further Development

### Android Platform (PlatformUi.cs)
- [ ] Verify Color.ToAndroid() extension method availability in MAUI
- [ ] Test safe area inset calculations on various Android devices
- [ ] Implement proper command handling for "ExternalPaymentLink"
- [ ] Verify navigation bar styling works correctly

### iOS Platform (PlatformUi.cs)  
- [ ] Verify UIApplication.SharedApplication.OpenUrl still works in MAUI
- [ ] Test status bar hide/show functionality
- [ ] Implement Super.StatusBarHeight and Super.NavBarHeight equivalents
- [ ] Verify safe area calculations work on notched devices

### iOS Platform (ToastMessage.cs)
- [ ] Test GetCurrentViewController() method with MAUI window management
- [ ] Verify NSTimer behavior in MAUI context
- [ ] Test toast positioning (currently only supports default positioning)

### Missing Implementations
- [ ] Windows platform implementations needed
- [ ] MacCatalyst testing (currently uses iOS implementations)
- [ ] Tizen platform support if needed

### Migration Tasks
- [ ] Find and replace all `DependencyService.Get<IPlatformUi>()` calls with `App.PlatformUi`
- [ ] Find and replace all `DependencyService.Get<IToastMessage>()` calls with `ServiceHelper.ToastMessage`
- [ ] Update any code that directly references platform-specific implementations
- [ ] Test all platform services on physical devices

## Testing Recommendations

1. **Test on Physical Devices**: Especially important for:
   - Safe area calculations (notched devices)
   - Status bar hide/show functionality
   - Toast message positioning
   - URL opening functionality

2. **Test Platform-Specific Features**:
   - Android: Navigation bar styling, theme application
   - iOS: Status bar control, safe area insets
   - Cross-platform: Toast messages, URL opening

3. **Performance Testing**: 
   - Verify service resolution performance vs DependencyService
   - Test memory usage with service instances

## Migration Benefits

1. **Better Performance**: MAUI DI is faster than Xamarin DependencyService
2. **Type Safety**: Compile-time checking vs runtime resolution
3. **Testability**: Easy to mock services for unit testing
4. **Modern Pattern**: Follows .NET dependency injection conventions
5. **Flexibility**: Supports constructor injection, scoped services, etc.
