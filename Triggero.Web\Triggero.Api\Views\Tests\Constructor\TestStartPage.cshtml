﻿@using CRMWeb.Helpers.Html
@using CRMWeb.Helpers;
@using Triggero.Models.Enums;
@using Triggero.Models.Practices.Categories
@using Triggero.Models.Tests
@using Triggero.Web.ViewModels.Tests
@model TestStartPageVM

<div class="steps">
    <div class="step active">
        <div class="circle">1</div>
        <div class="text">НАСТРОЙКА СТРАНИЦЫ</div>
        <div class="arrow"><i class="fa-solid fa-arrow-right"></i></div>
    </div>

    <div class="step">
        <div class="circle">2</div>
        <div class="text">ВОПРОСЫ</div>
        <div class="arrow"><i class="fa-solid fa-arrow-right"></i></div>
    </div>

    <div class="step">
        <div class="circle">3</div>
        <div class="text">РЕЗУЛЬТАТЫ</div>
    </div>
</div>

<div class="row">
    <form asp-action="SaveTestStartPage" asp-controller="Tests" method="post" onsubmit="onSubmit(event)" enctype="multipart/form-data">

        <input type="hidden" asp-for="Test.Id" value="@Model.Test.Id" />
        <input type="hidden" asp-for="Test.IsHidden" value="@Model.Test.IsHidden" />
        <input type="hidden" asp-for="Test.ImgPath" value="@Model.Test.ImgPath" />
        <input type="hidden" asp-for="Test.IconImgPath" value="@Model.Test.IconImgPath" />
        <input type="hidden" asp-for="Test.ResultImgPath" value="@Model.Test.ResultImgPath" />
        <input type="hidden" asp-for="Test.Watches" value="@Model.Test.Watches" />

        <div class="row">

            <div class="mb-3">
                <label for="" class="formlabel">Превью</label>
                <div class="content-load">
                    <div class="close-file" id="close_file3"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image3" for="pct3" style="height: 6em;background-image: url('@Model.Test.IconImgPath');background-size: cover;background-repeat: no-repeat;">
                        <div class="text-fileupload" id="uploadText3">
                            @if (string.IsNullOrEmpty(Model.Test.IconImgPath))
                            {
                                @("Загрузите файл")
                            }
                        </div>
                    </label>
                    <input class="pctfile" type="file" name="imgPreview" id="pct3">
                </div>
            </div>

            <div class="mb-3 mt-3">
                <label for="" class="formlabel">Загрузить изображение</label>
                <div class="content-load" style="">
                    <div class="close-file" id="close_file"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image" for="pct" style="height: 6em;background-image: url('@Model.Test.ImgPath');background-size: cover;background-repeat: no-repeat;">
                        <div class="text-fileupload">
                            @if (string.IsNullOrEmpty(@Model.Test.ImgPath))
                            {
                                @("Загрузите файл")
                            }
                        </div>
                    </label>
                    <input class="pctfile" type="file" name="file" id="pct">
                </div>
            </div>


            <div class="col-md-12 content-box">
                <div class="mb-3">
                    <label for="" class="formlabel">Заголовок</label>
                    <input type="text" class="form-control" asp-for="Test.Title" placeholder="Введите заголовок">
                </div>

                <div class="mb-3">
                    <label for="" class="formlabel">Текст</label>
                    <textarea id="editor-input" required asp-for="Test.Description" rows="3"></textarea>
                    <script>
                        $('#editor-input')
                            .trumbowyg({
                                btnsDef: {
                                    // Create a new dropdown
                                    image: {
                                        dropdown: ['insertImage', 'noembed'],
                                        ico: 'insertImage'
                                    }
                                },
                                // Redefine the button pane
                                btns: [
                                    ['viewHTML'],
                                    ['formatting'],
                                    ['strong', 'em', 'del'],
                                    ['superscript', 'subscript'],
                                    ['link'],
                                    ['image'], // Our fresh created dropdown
                                    ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
                                    ['unorderedList', 'orderedList'],
                                    ['horizontalRule'],
                                    ['removeformat'],
                                    ['fullscreen']
                                ]
                            });
                    </script>
                </div>
                
                <div class="mb-3">
                    <label for="" class="formlabel">Ссылка на источник</label>
                    <input type="url" asp-for="Test.ReferenceLink" class="form-control" placeholder="">
                </div>

                <div class="mb-3">
                    <label for="" class="formlabel">Время прохождения в минутах (информативно)</label>
                    <input type="number" class="form-control" asp-for="Test.PassingTimeInMinutes" placeholder="Введите время">
                </div>

                <div class="mb-3">
                    <label for="" class="formlabel">Основные теги</label>
                    <input type="text" asp-for="Test.MainTags" required class="form-control" placeholder="">
                </div>

                <div class="mb-3">
                    <label for="" class="formlabel">Вторичные теги</label>
                    <input type="text" asp-for="Test.SecondaryTags" required class="form-control" placeholder="">
                </div>


                <div class="mb-3">
                    <label for="" class="formlabel">Категория</label>
                    <select class="form-select" asp-for="Test.CategoryId" required>
                        @Html.Raw(@ClassesToHtmlHelper<TestCategory>.Convert(Model.Categories,Model.Test.CategoryId))
                    </select>
                </div>


                <div class="mb-3">
                    <label for="" class="formlabel">Тип теста</label>
                    <select class="form-select" asp-for="Test.Type" required>
                        @Html.Raw(EnumToHtmlHelper<TestType>.Convert(Model.Test.Type))
                    </select>
                </div>

                <div class="mb-3">
                    <label for="" class="formlabel">Шкалы (для теста со шкалами)</label>
                    <table class="table" id="tablemanager">
                        <thead class="tableheader">
                            <tr>
                                <th scope="col">Название</th>
                                <th scope="col">Максимальный балл</th>
                                <th scope="col">
                                    <button type="button" onclick="addScale()" style="background: transparent"><i class="fa-solid fa-plus"></i></button>
                                </th>
                            </tr>
                        </thead>
                        <tbody id="scalesTableBody">
                            @foreach(var scale in Model.Test.Scales) {
                                <tr data-id="@scale.Id">
                                    <td>
                                        <input type="text" value="@scale.Title" required class="form-control" placeholder="">
                                    </td>
                                    <td>
                                        <input type="number" value="@scale.MaximumScore" required class="form-control" placeholder="">
                                    </td>
                                    <td>
                                        <button type="button" onclick="removeScale(event)" style="background: transparent;border: 0;" class="edit">
                                            <i class="fa-sharp fa-solid fa-trash"></i>
                                        </button>
                                    </td>
                                </tr>
                            }

                        </tbody>
                    </table>
                </div>

                <div class="mb-3 position-right">
                    <button type="submit" class="button-classic">Далее</button>
                </div>
            </div>
        </div>
    </form>
</div>

<script>

    let newTestScalesCounter = 0;

    let scaleIdsToDelete = [];

    function addScale(){

        let row = document.createElement('tr');
        row.setAttribute('data-id','0');

        let testId = @Model.Test.Id;
        if(testId == 0){
            row.innerHTML = `<td>
                                  <input type="text" name="Test.Scales[${newTestScalesCounter}].Title" required class="form-control" placeholder="">
                             </td>
                             <td>
                                  <input type="number" name="Test.Scales[${newTestScalesCounter}].MaximumScore" value="10" required class="form-control" placeholder="">
                             </td>
                             <td>
                                 <button type="button" onclick="removeScale(event)" style="background: transparent;border: 0;" class="edit">
                                     <i class="fa-sharp fa-solid fa-trash"></i>
                                 </button>
                             </td>`;
            newTestScalesCounter++;
        }
        else{
            row.innerHTML = `<td>
                                 <input type="text" required class="form-control" placeholder="">
                             </td>
                             <td>
                                 <input type="number" value="10" required class="form-control" placeholder="">
                             </td>
                             <td>
                                 <button type="button" onclick="removeScale(event)" style="background: transparent;border: 0;" class="edit">
                                     <i class="fa-sharp fa-solid fa-trash"></i>
                                 </button>
                             </td>`;
        }
       
        document.getElementById('scalesTableBody').appendChild(row);



    }

    function removeScale(event){
        let row = event.target.parentNode.parentNode.parentNode.parentNode;

        let testId = @Model.Test.Id;
        if(testId == 0){
            newTestScalesCounter--;
        }


        let scaleId = row.getAttribute('data-id');
        if (scaleId != 0) {
            scaleIdsToDelete.push(new Number(scaleId));
        }

        document.getElementById('scalesTableBody').removeChild(row);
    }



</script>

<script>

    async function onSubmit(event) {
        event.preventDefault();

        await cropFinal('pct', 'upload-image');
        await cropFinal('pct3', 'upload-image3');

        let testId = @Model.Test.Id;
        if(testId != 0){

            console.log(scaleIdsToDelete);

            await fetch(document.location.origin + `/RemoveTestScales?testId=${testId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8'
                },
                body: JSON.stringify(scaleIdsToDelete)
            });


            //Апдейт шкал
            let scales = [];         
            let rows = document.getElementById('scalesTableBody').children;

            for(let i=0;i<rows.length;i++){

                let row = rows[i];

                let scale = {
                    Id : new Number(row.getAttribute('data-id')),
                    Title: row.children[0].children[0].value, //td -> input
                    MaximumScore: new Number(row.children[1].children[0].value), //td -> input
                }
                scales.push(scale);
            }

            await fetch(document.location.origin + `/UpdateTestScales?testId=${testId}`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json;charset=utf-8'
                },
                body: JSON.stringify(scales)
            });

        }


        event.target.submit();
    }

    $(document).ready(function () {
        initImageCroppieSized('upload-image', 'pct', 'close_file', 335, 220);
        initImageCroppieSized('upload-image3', 'pct3', 'close_file3', 300, 300);
    });


</script>
