﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models.Enums;
using Triggero.Models.Tickets;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Tickets;

namespace Triggero.Web.Controllers
{
    [Authorize]
    public class TicketsController : AbsController
    {
        public TicketsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Tickets")]
        public IActionResult Tickets()
        {
            var tickets = DB.Tickets.Include(o => o.CreatedBy).ToList();
            tickets.Reverse();
            return View(@"Views\Tickets\Tickets.cshtml", tickets);
        }

        //[Route("TicketPage")]
        public IActionResult TicketPage(int id)
        {
            var ticket = DB.Tickets
                .Include(o => o.CreatedBy)
                .Include(o => o.Messages).ThenInclude(o => o.Attachments)
                .Include(o => o.Messages).ThenInclude(o => o.SentBy)
                .FirstOrDefault(o => o.Id == id);
            var vm = new TicketPageVM
            {
                Ticket = ticket
            };
            return View(@"Views\Tickets\TicketPage.cshtml", vm);
        }

        [HttpPost]
        //[Route("TicketPage/SendMessage"), HttpPost]
        public async Task<IActionResult> SendMessage(TicketPageVM vm)
        {
            var ticket = DB.Tickets.FirstOrDefault(o => o.Id == vm.Ticket.Id);

            foreach (var file in Request.Form.Files)
            {
                var filePath = "/uploads/" + Guid.NewGuid().ToString();
                string path = filePath + file.FileName;
                using (var fileStream = new FileStream(WebRootPath + path, FileMode.Create))
                {
                    await file.CopyToAsync(fileStream);
                }

                var attachment = new TicketAttachment()
                {
                    Path = path
                };
                vm.NewMessage.Attachments.Add(attachment);
            }

            ticket.Messages.Add(vm.NewMessage);


            DB.Tickets.Update(ticket);
            return RedirectToAction("Tickets", "Tickets");
        }


        //[Route("TicketPage/GetAttachment")]
        public async Task<IActionResult> GetAttachment(string path)
        {
            return File(path, "application/octet-stream");
        }


        //[Route("SetTicketStatus")]
        public async Task<IActionResult> SetTicketStatus(int ticketId, TicketStatus status)
        {
            var ticket = DB.Tickets.FirstOrDefault(o => o.Id == ticketId);
            ticket.Status = status;
            DB.Tickets.Update(ticket);
            await DB.SaveChangesAsync();
            return RedirectToAction("Tickets", "Tickets");
        }

    }
}
