﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Practices.Categories;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Practices;

namespace Triggero.Web.Controllers.Practices
{
    [Authorize]
    public class ExerciseCategoriesController : AbsController
    {
        public ExerciseCategoriesController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("ExerciseCategories")]
        public IActionResult ExerciseCategories()
        {
            var cats = DB.ExerciseCategories.Where(o => !o.IsDeleted).ToList();
            cats.Reverse();
            return View(@"Views\Practices\Categories\ExerciseCategories\ExerciseCategories.cshtml", cats);
        }

        //[Route("CreateExerciseCategory")]
        public IActionResult CreateExerciseCategory()
        {
            return View(@"Views\Practices\Categories\ExerciseCategories\CreateExerciseCategory.cshtml");
        }

        [HttpPost]
        //[Route("CreateExerciseCategoryPOST"), HttpPost]
        public async Task<IActionResult> CreateExerciseCategoryPOST([FromForm] ExerciseCategory category)
        {
            category.ImgPath = await SetAttachmentIfHas(category.ImgPath);
            DB.ExerciseCategories.Add(category);
            await DB.SaveChangesAsync();
            return RedirectToAction("ExerciseCategories", "ExerciseCategories");
        }


        //[Route("UpdateExerciseCategory")]
        public IActionResult UpdateExerciseCategory(int id)
        {
            var cat = DB.ExerciseCategories.FirstOrDefault(o => o.Id == id);
            return View(@"Views\Practices\Categories\ExerciseCategories\UpdateExerciseCategory.cshtml", cat);
        }

        [HttpPost]
        //[Route("UpdateExerciseCategoryPOST"), HttpPost]
        public async Task<IActionResult> UpdateExerciseCategoryPOST([FromForm] ExerciseCategory category)
        {
            category.ImgPath = await SetAttachmentIfHas(category.ImgPath);
            DB.ExerciseCategories.Update(category);
            await DB.SaveChangesAsync();
            return RedirectToAction("ExerciseCategories", "ExerciseCategories");
        }

        [HttpDelete]
        //[Route("DeleteExerciseCategory"), HttpDelete]
        public async Task<IActionResult> DeleteExerciseCategory(int id)
        {
            var cat = DB.ExerciseCategories.FirstOrDefault(o => o.Id == id);
            cat.IsDeleted = true;
            DB.ExerciseCategories.Update(cat);
            await DB.SaveChangesAsync();
            return RedirectToAction("ExerciseCategories", "ExerciseCategories");
        }
    }
}
