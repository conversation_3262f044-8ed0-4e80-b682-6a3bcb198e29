﻿using System;
using System.Collections.Generic;
using System.Text;
using Triggero.MauiClient.Helpers;
using Triggero.Mobile.Enums;
using Triggero.Models.General.Influence;
using Triggero.Models.Practices;
using Triggero.Models.Tests;

namespace Triggero.Models
{
    /// <summary>
    /// Рекомендация на сегодня. Будет что-то одно. Остальное будет == null
    /// </summary>
    public class TaskForToday
    {
        public TaskForToday()
        {

        }


        public TaskForToday(ToDoListItem todo)
        {
            IElementDetails item = null;
            if (todo.Test != null)
            {
                Test = todo.Test;
                item = todo.Test;
            }
            else if (todo.Practice != null)
            {
                Practice = todo.Practice;
                item = todo.Practice;
            }
            else
            if (todo.Topic != null)
            {
                Topic = todo.Topic;
                item = todo.Topic;
            }
            else if (todo.Exercise != null)
            {
                Exercise = todo.Exercise;
                item = todo.Exercise;
            }
            else
            {
                Title = todo.GetLocalizedTitle(LanguageHelper.LangCode);
                ImgPath = todo.ImgPath;
                PassingTimeInMinutes = todo.MinutesCount;
            }

            Initialize(item);
        }

        void Initialize(IElementDetails item)
        {
            if (item != null)
            {
                Title = item.GetLocalizedTitle(LanguageHelper.LangCode);
                ImgPath = item.ImgPath;
                PassingTimeInMinutes = item.PassingTimeInMinutes;
            }
        }

        public TaskForToday(IElementDetails item)
        {
            if (item is Test test)
            {
                Test = test;
            }
            else if (item is Practice practice)
            {
                Practice = practice;
            }
            else
            if (item is Topic topic)
            {
                Topic = topic;
            }
            else if (item is Exercise exercise)
            {
                Exercise = exercise;
            }

            Initialize(item);
        }

        public Test Test { get; set; }
        public Practice Practice { get; set; }
        public Topic Topic { get; set; }
        public Exercise Exercise { get; set; }


        public string Title { get; set; }
        public string ImgPath { get; set; }
        public int PassingTimeInMinutes { get; set; }



        public bool IsCompleted { get; set; }

        public IElementDetails GetTaskModel()
        {
            if (Test != null) return Test;
            if (Practice != null) return Practice;
            if (Topic != null) return Topic;
            if (Exercise != null) return Exercise;
            return null;
        }

        public RecomendationType GetTaskType()
        {
            if (Test != null) return RecomendationType.Test;
            if (Practice != null) return RecomendationType.Practice;
            if (Topic != null) return RecomendationType.Topic;
            if (Exercise != null) return RecomendationType.Exercise;
            return RecomendationType.Unknown;
        }


    }
}
