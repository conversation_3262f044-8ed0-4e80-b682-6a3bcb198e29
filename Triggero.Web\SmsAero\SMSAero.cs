﻿using RestSharp;
using System;
using System.Net;
using System.Text;
using System.Threading;
using System.Threading.Tasks;

namespace SmsAero
{
    public class SMSAero
    {
        public SMSAero() { }
        public SMSAero(string email, string apikey)
        {
            Email = email;
            ApiKey = apikey;
        }

        public string Email { get; set; } = "<EMAIL>";
        public string ApiKey { get; set; } = "Zddh4tIPex7v2TIFe9AHng7g9GM";

        public async Task SendSMS(string number, string text, string sign)
        {
            try
            {
                string url = $"https://gate.smsaero.ru/v2/sms/send?number={number}&text={text}&sign={sign}";

                RestClient client = new RestClient(url);
                //var request = new RestRequest(Method.GET);

                var request = new RestRequest(url, Method.Get);
                var credentials = Convert.ToBase64String(Encoding.UTF8.GetBytes($"{Email}:{ApiKey}"));
                request.AddHeader("Authorization", $"Basic {credentials}");
                //request.Credentials = new NetworkCredential(Em<PERSON>, ApiKey);

                request.AddHeader("Accept", "application/json");
                request.AddHeader("Content-Type", "application/json");
                CancellationToken cancellationToken = new CancellationToken();
                var response = await client.ExecuteAsync(request, cancellationToken);

            }
            catch (Exception ex)
            {

            }

        }
    }
}
