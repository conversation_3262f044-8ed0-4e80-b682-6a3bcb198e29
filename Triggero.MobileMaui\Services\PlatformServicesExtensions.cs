using Triggero.Mobile.Abstractions;

namespace Triggero.MobileMaui.Services
{
    public static class PlatformServicesExtensions
    {
        public static MauiAppBuilder AddPlatformServices(this MauiAppBuilder builder)
        {
            // Register platform-specific services
#if ANDROID
            builder.Services.AddSingleton<IPlatformUi, Platforms.Android.PlatformUi>();
            builder.Services.AddSingleton<IToastMessage, Platforms.Android.ToastMessage>();
#elif IOS
            builder.Services.AddSingleton<IPlatformUi, Platforms.iOS.PlatformUi>();
            builder.Services.AddSingleton<IToastMessage, Platforms.iOS.ToastMessage>();
#elif WINDOWS
            builder.Services.AddSingleton<IPlatformUi, Platforms.Windows.PlatformUi>();
            builder.Services.AddSingleton<IToastMessage, Platforms.Windows.ToastMessage>();
#endif

            return builder;
        }
    }

    // Helper class to access services (similar to Xamarin DependencyService)
    public static class ServiceHelper
    {
        public static T GetService<T>() where T : class
        {
            var service = Application.Current?.Handler?.MauiContext?.Services?.GetService<T>();
            if (service == null)
            {
                throw new InvalidOperationException($"Service of type {typeof(T).Name} is not registered.");
            }
            return service;
        }

        public static IPlatformUi PlatformUi => GetService<IPlatformUi>();
        public static IToastMessage ToastMessage => GetService<IToastMessage>();
    }
}
