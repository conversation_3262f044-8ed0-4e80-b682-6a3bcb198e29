<?xml version="1.0" encoding="utf-8" ?>
<ContentView x:Class="Triggero.MobileMaui.Views.LibraryView"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw">

    <draw:Canvas HorizontalOptions="Fill" VerticalOptions="Fill">
        <draw:SkiaLayout Type="Column" 
                         Padding="20" 
                         Spacing="16"
                         HorizontalOptions="Fill" 
                         VerticalOptions="Fill">

            <!-- Header -->
            <draw:SkiaLabel Text="Library"
                            FontSize="24"
                            FontAttributes="Bold"
                            TextColor="#007ACC"
                            HorizontalOptions="Center" />

            <!-- Tab placeholder -->
            <draw:SkiaShape BackgroundColor="#F0F0F0"
                            CornerRadius="12"
                            Padding="16"
                            HorizontalOptions="Fill">
                
                <draw:SkiaLayout Type="Column" Spacing="8">
                    <draw:SkiaLabel Text="📚 Library Content"
                                    FontSize="18"
                                    FontAttributes="Bold"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="This is the Library view placeholder. Will include tab-based navigation for Exercises, Practices, Topics, and Breath sections."
                                    FontSize="14"
                                    TextColor="#666666"
                                    MaxLines="3" />
                </draw:SkiaLayout>
                
            </draw:SkiaShape>

            <!-- TODO sections placeholder -->
            <draw:SkiaShape BackgroundColor="#E8F4FD"
                            CornerRadius="12"
                            Padding="16"
                            HorizontalOptions="Fill">
                
                <draw:SkiaLayout Type="Column" Spacing="8">
                    <draw:SkiaLabel Text="🔄 Migration TODO:"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    TextColor="#007ACC" />
                    
                    <draw:SkiaLabel Text="• DrawnListCategories for exercises"
                                    FontSize="12"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="• DrawnListCategories for practices"
                                    FontSize="12"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="• DrawnListCategories for topics"
                                    FontSize="12"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="• DrawnBreathTab component"
                                    FontSize="12"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="• Tab visibility management"
                                    FontSize="12"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="• Search functionality"
                                    FontSize="12"
                                    TextColor="#333333" />
                </draw:SkiaLayout>
                
            </draw:SkiaShape>

        </draw:SkiaLayout>
    </draw:Canvas>

</ContentView>
