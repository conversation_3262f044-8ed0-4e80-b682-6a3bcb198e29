# DrawnUI Migration Guide

## Overview
This guide covers the migration from Xamarin DrawnUI to MAUI DrawnUI, including package changes and integration patterns.

## Package Migration

### Before (Xamarin)
```xml
<PackageReference Include="AppoMobi.Xamarin.DrawnUi" Version="1.9.3.170" />
```

### After (MAUI)
```xml
<PackageReference Include="AppoMobi.Maui.DrawnUi" Version="1.5.1.4" />
```

**Status**: ✅ Package installed and verified in builds

## Key Changes from Xamarin to MAUI

### Namespace Consistency
- **Xamarin**: Mixed namespaces between Xamarin.Forms and DrawnUI
- **MAUI**: Unified Microsoft.Maui.* namespaces with DrawnUI integration

### Screen Information
**Current Implementation**: Uses `DrawnUi.Models.Screen` in platform services
```csharp
public Screen Screen { get; } = new();
```

**Integration Points**:
- Platform services populate Screen properties
- Screen density, dimensions, and safe area insets
- Cross-platform screen information access

## Migration Strategy

### CardsView Replacement
**Xamarin Pattern**:
```xml
<cards:CardsView ItemsSource="{Binding Items}">
    <cards:CardsView.ItemTemplate>
        <!-- Card content -->
    </cards:CardsView.ItemTemplate>
</cards:CardsView>
```

**MAUI Target** (DrawnUI SkiaShape):
```xml
<!-- TODO: Replace with DrawnUI SkiaShape equivalent -->
<drawnui:SkiaShape>
    <!-- Card content using DrawnUI components -->
</drawnui:SkiaShape>
```

### Custom Renderers → MAUI Handlers
**Migration Pattern**:
1. **Identify Xamarin custom renderers** using DrawnUI
2. **Convert to MAUI handlers** with DrawnUI integration
3. **Update registration** from renderer to handler pattern
4. **Test cross-platform compatibility**

## Integration Requirements

### MauiProgram.cs Setup
**Required**: DrawnUI initialization in MAUI app builder
```csharp
public static MauiApp CreateMauiApp()
{
    var builder = MauiApp.CreateBuilder();
    builder
        .UseMauiApp<App>()
        .UseDrawnUi() // Add DrawnUI integration
        .AddPlatformServices();
    
    return builder.Build();
}
```

### Platform-Specific Considerations

#### Windows Platform
- **Development Focus**: Primary platform for UI development, used for fast compilation and reliable HotReload. App will NOT be published on this platform, used for development only.

#### Android Platform
- **Compatibility**: Test across Android API levels

#### iOS Platform
- **Safe Areas**: Integration with iOS safe area handling

## Screen Migration Priority

### Phone Screens (INCLUDE)
- All phone-specific layouts and components
- Phone navigation patterns
- Phone-optimized DrawnUI components

### Tablet Screens (EXCLUDE)
- **Explicitly omit** tablet-specific implementations
- **Add TODO comments** when skipping tablet code
- **Focus resources** on phone experience

## Code Migration Patterns

### TODO Comment Strategy
When encountering Xamarin DrawnUI code:
```csharp
// TODO: MAUI Migration - Replace Xamarin DrawnUI with MAUI DrawnUI
// Original Xamarin code:
// var xamarinComponent = new XamarinDrawnUiComponent();
// 
// MAUI equivalent needed:
// var mauiComponent = new MauiDrawnUiComponent();
```

### Preservation Strategy
**Prefer copying uncompilable code with comments** over silent omission:
```csharp
/*
// Original Xamarin DrawnUI implementation
public class XamarinCustomRenderer : ViewRenderer<CustomView, NativeView>
{
    protected override void OnElementChanged(ElementChangedEventArgs<CustomView> e)
    {
        // Xamarin-specific logic
    }
}
*/

// TODO: Convert to MAUI handler
public class MauiCustomHandler : Microsoft.Maui.Handlers.ViewHandler<CustomView, NativeView>
{
    // MAUI handler implementation needed
}
```

## Testing Strategy

### Development Workflow
1. **Windows Development**: Implement DrawnUI components on Windows first
2. **Cross-Platform Build**: Regular Android/iOS build verification
3. **Visual Testing**: Verify rendering across platforms
4. **Performance Testing**: Ensure smooth animations and interactions

### Build Verification
```bash
# Test Windows DrawnUI integration
dotnet build -f net9.0-windows10.0.19041.0 --configuration Debug

# Verify Android compatibility
dotnet build -f net9.0-android --configuration Debug

# Validate iOS integration
dotnet build -f net9.0-ios --configuration Debug
```

## Known Considerations

### Performance
- **SkiaSharp Backend**: Ensure optimal GPU utilization
- **Memory Management**: Monitor for memory leaks in complex UI
- **Animation Performance**: Verify smooth 60fps animations

### Platform Differences
- **Windows**: Desktop interaction patterns
- **Android**: Touch and gesture handling
- **iOS**: Platform-specific UI guidelines

### Compatibility
- **API Changes**: Some DrawnUI APIs may have changed between versions
- **Breaking Changes**: Document any breaking changes encountered
- **Workarounds**: Document temporary solutions for migration issues

## Next Steps

### Immediate Tasks
1. **Setup DrawnUI Integration**: Add `.UseDrawnUi()` to MauiProgram.cs
2. **Verify Basic Rendering**: Test simple DrawnUI components on Windows
3. **Cross-Platform Testing**: Ensure DrawnUI works on all target platforms
4. **Document API Differences**: Compare Xamarin vs MAUI DrawnUI APIs

### Future Migration Work
1. **CardsView Replacement**: Systematic replacement with SkiaShape
2. **Custom Renderer Migration**: Convert to MAUI handlers
3. **Screen-by-Screen Migration**: Phone screens only
4. **Performance Optimization**: Platform-specific optimizations

## Resources
- **DrawnUI Documentation**: Check for MAUI-specific documentation
- **Sample Projects**: Look for MAUI DrawnUI examples
- **Community Support**: DrawnUI community for migration guidance
- **Performance Profiling**: Use platform profilers for optimization
