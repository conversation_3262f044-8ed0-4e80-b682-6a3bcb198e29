﻿using MapsterMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using System.Diagnostics;
using System.Linq;
using Triggero.Application.Extensions;
using Triggero.Database;
using Triggero.Domain.Models.Dto;
using Triggero.Models.General;
using Triggero.Web.Abstractions;
using Triggero.Web.Models;
using Triggero.Web.ViewModels.Stats;

namespace Triggero.Web.Controllers
{
    [Authorize]
    public class StatsController : AbsController
    {
        private readonly IMapper _mapper;


        public StatsController(DatabaseContext db, IWebHostEnvironment appEnv, IMapper mapper) : base(db, appEnv)
        {
            _mapper = mapper;
        }

        //[Route("Registrations")]
        public IActionResult Registrations()
        {
            var vm = new RegistrationsVM();

            var regs = DB.Users.Where(o => !o.IsDeleted).OrderByDescending(o => o.RegisteredAt).GroupBy(o => o.RegisteredAt.Date);
            foreach (var regsGroup in regs)
            {
                vm.Registrations.Add(new RegistrationsPerDay
                {
                    Date = regsGroup.Key,
                    Count = regsGroup.Count()
                });
            }

            vm.Total = DB.Users.Where(o => !o.IsDeleted).Count();
            return View(@"Views\Stats\Registrations.cshtml", vm);
        }

        //[Route("Users")]
        public async Task<IActionResult> Users()
        {
            var vm = new UsersVM();

            var originals = await DB.Users
                .Include(i => i.UserSubscription)
                .ThenInclude(t => t.UserSubsctiptionOptions)
                .ThenInclude(t => t.PlanOption)
                .Include(i => i.Payments)
                .Where(o => !o.IsDeleted)
                .OrderByDescending(x => x.CreatedAt)
                .ToListAsync();

            var dtos = new List<UserDataDto>();
            foreach (var item in originals)
            {
                dtos.Add(await _mapper.MapAsync<UserDataDto>(item));

                if (item.Email == "<EMAIL>")
                {
                    var check = item.Payments.LastOrDefault();
                }



            }

            vm.Users = dtos;
            vm.Total = vm.Users.Count;

            return View(@"Views\Stats\Users.cshtml", vm);
        }
    }
}