﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Practices.Categories
@using Triggero.Web.ViewModels.Practices
@{
    ViewData["Title"] = "СОЗДАНИЕ СТАТЬИ";
}
@model CreateTopicVM

 <!-- CONTENT -->
<form asp-action="CreateTopicPOST" asp-controller="Topic" onsubmit="onSubmit(event)" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">


            <div class="mb-3">
                <label for="" class="formlabel">Превью</label>
                <div class="content-load">
                    <div class="close-file" id="close_file3"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image3" for="pct3" style="height: 6em;"><div class="text-fileupload" id="uploadText3">Загрузите файл</div></label>
                    <input class="pctfile" type="file" name="imgPreview" id="pct3">
                </div>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Изображение</label>
                <div class="content-load">
                    <div class="close-file" id="close_file"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image" for="pct" style="height: 6em;"><div class="text-fileupload">Загрузите файл</div></label>
                    <input class="pctfile" type="file" name="file" id="pct">
                </div>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="NewTopic.Title" required class="form-control" placeholder="">
            </div>

     @*       <div class="mb-3">
                <label for="" class="formlabel">Автор</label>
                <input type="text" asp-for="NewTopic.Author" required class="form-control" placeholder="">
            </div>*@


            <div class="mb-3">
                <label for="" class="formlabel">Текст</label>
                <textarea id="editor-input" required asp-for="NewTopic.Description" rows="3"></textarea>
                <script>
                    $('#editor-input')
                        .trumbowyg({
                            btnsDef: {
                                // Create a new dropdown
                                image: {
                                    dropdown: ['insertImage', 'noembed'],
                                    ico: 'insertImage'
                                }
                            },
                            // Redefine the button pane
                            btns: [
                                ['viewHTML'],
                                ['formatting'],
                                ['strong', 'em', 'del'],
                                ['superscript', 'subscript'],
                                ['link'],
                                ['image'], // Our fresh created dropdown
                                ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
                                ['unorderedList', 'orderedList'],
                                ['horizontalRule'],
                                ['removeformat'],
                                ['fullscreen']
                            ]
                        });
                </script>
            </div>

           
            <div class="mb-3">
                <label for="" class="formlabel">Ссылка на источник</label>
                <input type="url" asp-for="NewTopic.ReferenceLink" class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Время в минутах на чтение</label>
                <input type="number" asp-for="NewTopic.PassingTimeInMinutes" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Основные теги</label>
                <input type="text" asp-for="NewTopic.MainTags" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Вторичные теги</label>
                <input type="text" asp-for="NewTopic.SecondaryTags" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Категория</label>
                <select class="form-select" asp-for="NewTopic.CategoryId"  required>
                    @Html.Raw(@ClassesToHtmlHelper<TopicCategory>.Convert(Model.Categories,Model.NewTopic.CategoryId))
                </select>
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>

<script>

    async function onSubmit(event) {
        event.preventDefault();
        await cropFinal('pct', 'upload-image');
        await cropFinal('pct3', 'upload-image3');
        event.target.submit();
    }

    $(document).ready(function () {
        initImageCroppieSized('upload-image', 'pct', 'close_file', 350, 240);
        initImageCroppieSized('upload-image3', 'pct3', 'close_file3', 300, 300);
    });


</script>