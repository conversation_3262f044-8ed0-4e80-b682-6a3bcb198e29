﻿using Newtonsoft.Json;
using Odintcovo.API.Helpers;
using RestSharp;
using Triggero.Domain.Enums;
using Triggero.Domain.Models;
using Triggero.Models.General;
using Triggero.Models.General.Settings;
using Triggero.Models.Tickets;

namespace MobileAPIWrapper.Methods.General.Users
{
    public class UsersMethods
    {
        public static string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("Users/");

        #region Получения пользователя

        public async Task<User> GetUserById(int id, OSType OS)
        {
            string url = BASE_HOST + $"GetUserById?id={id}&OS={(int)OS}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<User>(response.Content);
            return obj;
        }
        public async Task<User> GetUserByPhone(string phone, OSType OS)
        {
            string url = BASE_HOST + $"GetUserByPhone?phone={phone.Replace("+", "")}&OS={(int)OS}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<User>(response.Content);
            return obj;
        }
        public async Task<User> GetUserByEmail(string email, OSType OS)
        {
            string url = BASE_HOST + $"GetUserByEmail?email={email}&OS={(int)OS}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<User>(response.Content);
            return obj;
        }
        public async Task<User> GetUserByEmailAndPassword(string email, string password, OSType OS)
        {
            var authModel = new AuthUserModel
            {
                Email = email,
                Password = password
            };

            string url = BASE_HOST + $"GetUserByEmailAndPassword?OS={(int)OS}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Post, authModel);

            var obj = JsonConvert.DeserializeObject<User>(response.Content);
            return obj;
        }
        #endregion

        public string LastError { get; set; }

        public async Task<bool> RegisterUser(RegisterUserDto model)
        {
            var ret = false;
            try
            {
                string url = BASE_HOST + $"RegisterUser";
                var response = await RequestHelper.ExecuteRequestAsync(url, Method.Post, model);
                ret = JsonConvert.DeserializeObject<bool>(response.Content);
            }
            catch (Exception e)
            {
                LastError = e.Message;
                Console.WriteLine(e);
            }
            return ret;
        }

        public async Task<User> ChangePassword(string email, string password, OSType OS)
        {
            string url = BASE_HOST + $"ChangePassword?email={email}&password={password}&OS={(int)OS}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);

            var obj = JsonConvert.DeserializeObject<User>(response.Content);
            return obj;
        }


        public async Task<List<Ticket>> GetUserTickets(int id)
        {
            try
            {
                string url = BASE_HOST + $"GetUserTickets?id={id}";
                var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);
                var ret = JsonConvert.DeserializeObject<List<Ticket>>(response.Content);
                return ret;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return null;
        }

        public async Task<bool> DeleteUserAccount(int id)
        {
            try
            {
                string url = BASE_HOST + $"DeleteUserAccount?id={id}";
                var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);
                return true;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return false;
        }

        #region Редактирование основных данных пользователя
        public async Task<bool> SetUserAvatar(int userId, int avatarId)
        {
            try
            {
                string url = BASE_HOST + $"SetUserAvatar?userId={userId}&avatarId={avatarId}";
                var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);
                return true;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return false;
        }
        public async Task<bool> SetName(int userId, string name)
        {
            try
            {
                string url = BASE_HOST + $"SetName?userId={userId}&name={name}";
                var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);
                return true;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return false;
        }
        public async Task<bool> SetEmailToSendReports(int userId, string email)
        {
            try
            {
                string url = BASE_HOST + $"SetEmailToSendReports?userId={userId}&email={email}";
                var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);
                return true;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return false;
        }

        #endregion

        public async Task<NotificationSettings> UpdateNotificationSettings(NotificationSettings settings)
        {
            try
            {
                string url = BASE_HOST + $"UpdateNotificationSettings";
                var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put, settings);
                var ret = JsonConvert.DeserializeObject<NotificationSettings>(response.Content);
                return ret;
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }
            return null;
        }


    }
}
