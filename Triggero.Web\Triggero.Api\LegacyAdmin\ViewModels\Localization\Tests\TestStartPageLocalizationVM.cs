﻿using Triggero.Models.Abstractions;
using Triggero.Models.General;
using Triggero.Models.Localization.Tests;
using Triggero.Models.Tests;

namespace Triggero.Web.ViewModels.Localization.Tests
{
    public class TestStartPageLocalizationVM
    {
        public int Id { get; set; }
        public Language CurrentLanguage { get; set; }
        public TestLocalization Localization { get; set; } = new TestLocalization();

        public List<Question> Questions { get; set; } = new List<Question>();
        public List<TestResult> Results { get; set; } = new List<TestResult>();
    }
}
