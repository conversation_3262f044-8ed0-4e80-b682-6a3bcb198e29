﻿using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Collections.Generic;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Enums;
using Triggero.Models.General;
using Triggero.Models.Practices;
using Triggero.Models.Tests;
using Triggero.PushNotifications;

namespace Triggero.Domain.Jobs {
    public class PushNotifications
    {

        public static async Task SendNewTestNotification(Test test)
        {
            try
            {
                using (var db = new DatabaseContext())
                {


                    var users = db.Users.Include(o => o.NotificationSettings)
                                        .Include(o => o.Devices)
                            .Where(o => o.NotificationSettings.ShouldNotifyNewTests)
                            .ToList();

                    foreach (var user in users)
                    {

                        var userLocalTime = DateTime.UtcNow.Add(user.NotificationSettings.BaseUtcOffset);


                        if (CheckTime(userLocalTime, user.NotificationSettings.NotifyTrackerFillingTime))
                        {
                            foreach (var device in user.Devices)
                            {
                                try
                                {
                                    PushNotificationsHelper.SendNotification(device.FirebaseToken, "Уведомление", $"Вышел новый тест {test.Title}. Скорее пройдите его");
                                }
                                catch (Exception ex) { }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
            }
        }
        public static async Task SendNewExerciseNotification(Exercise exercise)
        {
            try
            {
                using (var db = new DatabaseContext())
                {


                    var users = db.Users.Include(o => o.NotificationSettings)
                                        .Include(o => o.Devices)
                            .Where(o => o.NotificationSettings.ShouldNotifyNewExercises)
                            .ToList();

                    foreach (var user in users)
                    {

                        var userLocalTime = DateTime.UtcNow.Add(user.NotificationSettings.BaseUtcOffset);


                        if (CheckTime(userLocalTime, user.NotificationSettings.NotifyTrackerFillingTime))
                        {
                            foreach (var device in user.Devices)
                            {
                                try
                                {
                                    PushNotificationsHelper.SendNotification(device.FirebaseToken, "Уведомление", $"Вышло новое упражение {exercise.Title}. Скорее сделайте его");
                                }
                                catch (Exception ex) { }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
            }
        }
        public static async Task SendNewPracticeNotification(Practice practice)
        {
            try
            {
                using (var db = new DatabaseContext())
                {


                    var users = db.Users.Include(o => o.NotificationSettings)
                                        .Include(o => o.Devices)
                            .Where(o => o.NotificationSettings.ShouldNotifyNewPractices)
                            .ToList();

                    foreach (var user in users)
                    {

                        var userLocalTime = DateTime.UtcNow.Add(user.NotificationSettings.BaseUtcOffset);


                        if (CheckTime(userLocalTime, user.NotificationSettings.NotifyTrackerFillingTime))
                        {
                            foreach (var device in user.Devices)
                            {
                                try
                                {
                                    PushNotificationsHelper.SendNotification(device.FirebaseToken, "Уведомление", $"Вышла новая практика {practice.Title}. Скорее прослушайте её");
                                }
                                catch (Exception ex) { }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
            }
        }
        public static async Task SendNewTopicNotification(Topic topic)
        {
            try
            {
                using (var db = new DatabaseContext())
                {


                    var users = db.Users.Include(o => o.NotificationSettings)
                                        .Include(o => o.Devices)
                            .Where(o => o.NotificationSettings.ShouldNotifyNewPosts)
                            .ToList();

                    foreach (var user in users)
                    {

                        var userLocalTime = DateTime.UtcNow.Add(user.NotificationSettings.BaseUtcOffset);


                        if (CheckTime(userLocalTime, user.NotificationSettings.NotifyTrackerFillingTime))
                        {
                            foreach (var device in user.Devices)
                            {
                                try
                                {
                                    PushNotificationsHelper.SendNotification(device.FirebaseToken, "Уведомление", $"Вышла новая статья {topic.Title}. Скорее прочтите её");
                                }
                                catch (Exception ex) { }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
            }
        }



        private static bool CheckTime(DateTime userLocalTime,TimeSpan time) {

            return time.Hours == userLocalTime.Hour
                && time.Minutes >= time.Minutes;

        }



    }
}
