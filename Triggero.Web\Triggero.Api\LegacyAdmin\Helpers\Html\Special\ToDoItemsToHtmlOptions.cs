﻿using System.Collections.Generic;
using Triggero.Models;
using Triggero.Models.General.Influence;
using Triggero.Models.Practices;
using Triggero.Models.Tests;

namespace CRMWeb.Helpers.Html.Specific
{
    public class ToDoItemsToHtmlOptions
    {
        public static string Convert(List<Test> tests, List<Exercise> exercises, List<Topic> topics, List<Practice> practices)
        {
            string html = "";

            foreach (var value in tests)
            {
                html += $"<option data-test-id=\"{value.Id}\">{value.Title}</option>\r\n";
            }
            foreach (var value in exercises)
            {
                html += $"<option data-exercise-id=\"{value.Id}\">{value.Title}</option>\r\n";
            }
            foreach (var value in topics)
            {
                html += $"<option data-topic-id=\"{value.Id}\">{value.Title}</option>\r\n";
            }
            foreach (var value in practices)
            {
                html += $"<option data-practice-id=\"{value.Id}\">{value.Title}</option>\r\n";
            }
            return html;
        }
        public static string Convert(List<Test> tests, List<Exercise> exercises, List<Topic> topics, List<Practice> practices,ToDoListItem item)
        {
            string html = "<option>Не выбрано</option>";

            foreach (var value in tests)
            {
                if(item.Test?.Id == value.Id)
                {
                    html += $"<option selected data-test-id=\"{value.Id}\">Тест: {value.Title}</option>\r\n";
                }
                else
                {
                    html += $"<option data-test-id=\"{value.Id}\">Тест: {value.Title}</option>\r\n";
                }  
            }
            foreach (var value in exercises)
            {
                if (item.Exercise?.Id == value.Id)
                {
                    html += $"<option selected data-exercise-id=\"{value.Id}\">Упражнение: {value.Title}</option>\r\n";
                }
                else
                {
                    html += $"<option data-exercise-id=\"{value.Id}\">Упражнение: {value.Title}</option>\r\n";
                }
            }
            foreach (var value in topics)
            {
                if (item.Topic?.Id == value.Id)
                {
                    html += $"<option selected data-topic-id=\"{value.Id}\">Статья: {value.Title}</option>\r\n";
                }
                else
                {
                    html += $"<option data-topic-id=\"{value.Id}\">Статья: {value.Title}</option>\r\n";
                }
            }
            foreach (var value in practices)
            {
                if (item.Practice?.Id == value.Id)
                {
                    html += $"<option selected data-practice-id=\"{value.Id}\">Практика: {value.Title}</option>\r\n";
                }
                else
                {
                    html += $"<option data-practice-id=\"{value.Id}\">Практика: {value.Title}</option>\r\n";
                }
            }
            return html;
        }
    }
}
