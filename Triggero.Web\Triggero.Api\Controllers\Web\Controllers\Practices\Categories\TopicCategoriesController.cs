﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Practices.Categories;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Practices;

namespace Triggero.Web.Controllers.Practices
{
    [Authorize]
    public class TopicCategoriesController : AbsController
    {
        public TopicCategoriesController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("TopicCategories")]
        public IActionResult TopicCategories()
        {
            var cats = DB.TopicCategories.Where(o => !o.IsDeleted).ToList();
            cats.Reverse();
            return View(@"Views\Practices\Categories\TopicCategories\TopicCategories.cshtml", cats);
        }


        //[Route("CreateTopicCategory")]
        public IActionResult CreateTopicCategory()
        {
            return View(@"Views\Practices\Categories\TopicCategories\CreateTopicCategory.cshtml");
        }

        [HttpPost]
        //[Route("CreateTopicCategoryPOST"), HttpPost]
        public async Task<IActionResult> CreateTopicCategoryPOST([FromForm] TopicCategory category)
        {
            category.ImgPath = await SetAttachmentIfHas(category.ImgPath);
            DB.TopicCategories.Add(category);
            await DB.SaveChangesAsync();
            return RedirectToAction("TopicCategories", "TopicCategories");
        }


        //[Route("UpdateTopicCategory")]
        public IActionResult UpdateTopicCategory(int id)
        {
            var cat = DB.TopicCategories.FirstOrDefault(o => o.Id == id);
            return View(@"Views\Practices\Categories\TopicCategories\UpdateTopicCategory.cshtml", cat);
        }

        [HttpPost]
        //[Route("UpdateTopicCategoryPOST"), HttpPost]
        public async Task<IActionResult> UpdateTopicCategoryPOST([FromForm] TopicCategory category)
        {
            category.ImgPath = await SetAttachmentIfHas(category.ImgPath);
            DB.TopicCategories.Update(category);
            await DB.SaveChangesAsync();
            return RedirectToAction("TopicCategories", "TopicCategories");
        }

        [HttpDelete]
        //[Route("DeleteTopicCategory"), HttpDelete]
        public async Task<IActionResult> DeleteTopicCategory(int id)
        {
            var cat = DB.TopicCategories.FirstOrDefault(o => o.Id == id);
            cat.IsDeleted = true;
            DB.TopicCategories.Update(cat);
            await DB.SaveChangesAsync();
            return RedirectToAction("TopicCategories", "TopicCategories");
        }
    }
}
