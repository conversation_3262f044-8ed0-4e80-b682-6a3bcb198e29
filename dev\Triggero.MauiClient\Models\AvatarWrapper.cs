﻿using System;
using System.Collections.Generic;
using System.Text;
using Triggero.Controls;
using Triggero.Mobile.Enums;
using Triggero.Models.General;
using Triggero.Models.Practices;
using Xamarin.Forms;

namespace Triggero.Models
{
    public class AvatarWrapper : BindableObject
    {

        public AvatarWrapper(UserAvatar avatar)
        {
            Avatar = avatar;
            Load();
        }
        ~AvatarWrapper()
        {
            Image = null;
        }

        private async void Load()
        {
            //Image = await ResorcesHelper.GetImageSource(avatar.AvatarPath);
            Image = Mobile.App.GetFullImageUrl(avatar.AvatarPath, ThumbnailSize.Small, ThumbnailType.Png);
        }

        private ImageSource image;
        public ImageSource Image
        {
            get { return image; }
            set { image = value; OnPropertyChanged(nameof(Image)); }
        }

        private UserAvatar avatar;
        public UserAvatar Avatar
        {
            get { return avatar; }
            set { avatar = value; OnPropertyChanged(nameof(Avatar)); }
        }
    }
}
