﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Localization
@using Triggero.Models.Localization.Practices
@using Triggero.Web.ViewModels.Localization
@{
    ViewData["Title"] = "РЕДАКТИРОВАНИЕ ПЕРЕВОДА";
    ViewData["ShowLang"] = true;
}
@model LocalizationVM<BreathPracticeLocalization>

 <!-- CONTENT -->
<form asp-action="UpdateBreathPracticeLocalizationPOST" asp-controller="BreathPracticeLocalizations" method="post" enctype="multipart/form-data" class="content">

    <input type="hidden" asp-for="Id" value="@Model.Id" />
    <input type="hidden" asp-for="Localization.Id" value="@Model.Localization.Id" />
    <input type="hidden" asp-for="Localization.LanguageId" value="@Model.Localization.LanguageId" />

    <div class="row">
        <div class="page">
            
            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Localization.Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Текст</label>
                <textarea class="form-control" required asp-for="Localization.Description" rows="3" placeholder=""></textarea>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Аудиозапись</label>
                <input type="file" name="file" class="form-control" placeholder="">
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>