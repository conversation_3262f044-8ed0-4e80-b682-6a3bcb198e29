﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Models;
using Triggero.Models.Enums;
using Triggero.Models.Practices.Categories;
using Triggero.Models.Practices.Other;

namespace Triggero.Domain.Controllers.Rest.Library
{
    [ApiController]
    [Route("[controller]")]
    public class TopicsController : ApiController
    {
        public TopicsController(DatabaseContext db) : base(db)
        {
        }


        #region Получение статей и категорий

        [ResponseCache(Duration = 120)]
        [HttpGet, Route("GetTopicCategories")]
        public async Task<List<TopicCategory>> GetTopicCategories()
        {
            return DB.TopicCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .ToList();
        }

        [ResponseCache(VaryByQueryKeys = new[] { "count", "offset" }, Duration = 120)]
        [HttpGet, Route("GetTopicCategoriesChunk")]
        public async Task<List<TopicCategory>> GetTopicCategoriesChunk(int count, int offset)
        {
            return DB.TopicCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Skip(offset).Take(count)
                .ToList();
        }

        [ResponseCache(Duration = 120)]
        [HttpGet, Route("GetTopics")]
        public async Task<List<Topic>> GetTopics()
        {
            var topics = DB.Topics
                            .Include(o => o.Localizations).ThenInclude(o => o.Language)
                            .Include(o => o.Category)
                            .Include(o => o.TopicRates)
                            .Where(o => !o.IsDeleted && !o.IsHidden)
                            .ToArray();

            SetRates(topics);
            return topics.ToList();
        }

        [ResponseCache(VaryByQueryKeys = new[] { "count", "offset" }, Duration = 120)]
        [HttpGet, Route("GetTopicsChunk")]
        public async Task<List<Topic>> GetTopicsChunk(int count, int offset)
        {
            var topics = DB.Topics
                            .Include(o => o.Localizations).ThenInclude(o => o.Language)
                            .Include(o => o.Category)
                            .Include(o => o.TopicRates)
                            .Where(o => !o.IsDeleted && !o.IsHidden)
                            .Skip(offset).Take(count)
                            .ToArray();

            SetRates(topics);
            return topics.ToList();
        }


        [ResponseCache(VaryByQueryKeys = new[] { "categoryId" }, Duration = 120)]
        [HttpGet, Route("GetTopicsByCategory")]
        public async Task<List<Topic>> GetTopicsByCategory(int categoryId)
        {
            var topics = DB.Topics
                            .Include(o => o.Localizations).ThenInclude(o => o.Language)
                            .Include(o => o.Category)
                            .Include(o => o.TopicRates)
                            .Where(o => !o.IsDeleted && !o.IsHidden && o.CategoryId == categoryId)
                            .ToArray();
            SetRates(topics);
            return topics.ToList();
        }


        [HttpGet, Route("GetTopicsByCategoryChunk")]
        public async Task<List<Topic>> GetTopicsByCategoryChunk(int categoryId, int count, int offset)
        {
            var topics = DB.Topics
                            .Include(o => o.Localizations).ThenInclude(o => o.Language)
                            .Include(o => o.Category)
                            .Include(o => o.TopicRates)
                            .Where(o => !o.IsDeleted && !o.IsHidden && o.CategoryId == categoryId)
                            .Skip(offset).Take(count)
                            .ToArray();
            SetRates(topics);
            return topics.ToList();
        }


        [ResponseCache(VaryByQueryKeys = new[] { "tag" }, Duration = 120)]
        [HttpGet, Route("GetTopicsByTag")]
        public async Task<List<Topic>> GetTopicsByTag(string tag)
        {
            var topics = DB.Topics
                            .Include(o => o.Localizations).ThenInclude(o => o.Language)
                            .Include(o => o.Category)
                            .Include(o => o.TopicRates)
                            .Where(o => !o.IsDeleted && !o.IsHidden)
                            .Where(o => o.MainTags.Equals(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Equals(tag, StringComparison.OrdinalIgnoreCase))
                            .ToArray();
            SetRates(topics);
            return topics.ToList();
        }



        [ResponseCache(VaryByQueryKeys = new[] { "id" }, Duration = 120)]
        [HttpGet, Route("GetTopic")]
        public async Task<Topic> GetTopic(int id)
        {
            var topic = DB.Topics
                            .Include(o => o.Localizations).ThenInclude(o => o.Language)
                            .Include(o => o.Category)
                            .Include(o => o.TopicRates)
                            .FirstOrDefault(o => o.Id == id);

            SetRates(topic);
            return topic;
        }

        private void SetRates(params Topic[] topics)
        {
            foreach (var topic in topics)
            {
                topic.Likes = topic.TopicRates.Count(o => o.RateType == RateType.Like);
                topic.Dislikes = topic.TopicRates.Count(o => o.RateType == RateType.Dislike);
                topic.TopicRates.Clear();
            }
        }

        #endregion


        #region Лайк/Дизлайк, просмотры

        [HttpPut, Route("ToggleRate")]
        public async Task<bool> ToggleRate(int userId, int topicId, RateType rateType)
        {
            var topic = DB.Topics.Include(o => o.TopicRates)
                                 .FirstOrDefault(o => o.Id == topicId);

            var found = topic.TopicRates.FirstOrDefault(o => o.UserId == userId);
            if (found != null)
            {
                if (found.RateType == rateType)
                {
                    topic.TopicRates.Remove(found);
                    DB.Topics.Update(topic);
                    await DB.SaveChangesAsync();

                    return false;
                }
                else
                {
                    DB.TopicRates.Update(found);
                    await DB.SaveChangesAsync();
                    return true;
                }
            }
            else
            {
                topic.TopicRates.Add(new TopicRate
                {
                    UserId = userId,
                    RateType = rateType
                });
                DB.Topics.Update(topic);
                await DB.SaveChangesAsync();
                return true;
            }
        }


        [HttpGet, Route("GetRate")]
        public async Task<RateType> GetRate(int userId, int topicId)
        {
            var topic = DB.Topics.Include(o => o.TopicRates)
                                 .FirstOrDefault(o => o.Id == topicId);

            var found = topic.TopicRates.FirstOrDefault(o => o.UserId == userId);
            if (found != null)
            {
                return found.RateType;
            }
            else
            {
                return RateType.None;
            }
        }

        [HttpPut, Route("AddWatch")]
        public async Task AddWatch(int topicId)
        {
            var topic = DB.Topics.Include(o => o.TopicRates)
                                 .FirstOrDefault(o => o.Id == topicId);
            topic.Watches++;

            DB.Topics.Update(topic);
            await DB.SaveChangesAsync();
        }
        #endregion

    }
}
