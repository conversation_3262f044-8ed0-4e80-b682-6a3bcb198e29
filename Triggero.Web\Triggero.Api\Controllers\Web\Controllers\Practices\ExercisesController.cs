﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models.Practices;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Practices;

namespace Triggero.Web.Controllers.Practices
{
    [Authorize]
    public class ExercisesController : AbsController
    {
        public ExercisesController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Exercises")]
        public IActionResult Exercises()
        {
            var exercises = DB.Exercises.Where(o => !o.IsDeleted).ToList();
            exercises.Reverse();
            return View(@"Views\Practices\Exercises\Exercises.cshtml", exercises);
        }


        //[Route("CreateExercise")]
        public IActionResult CreateExercise()
        {
            var vm = new CreateExerciseVM
            {
                Categories = DB.ExerciseCategories.ToList()
            };
            return View(@"Views\Practices\Exercises\CreateExercise.cshtml", vm);
        }

        [HttpPost]
        //[Route("CreateExercisePOST"), HttpPost]
        public async Task<IActionResult> CreateExercisePOST([FromForm] CreateExerciseVM vm)
        {
            vm.NewExercise.ImgPath = await SetAttachmentIfHas(vm.NewExercise.ImgPath, "img");
            vm.NewExercise.HeaderImgPath = await SetAttachmentIfHas(vm.NewExercise.HeaderImgPath, "headerImg");
            vm.NewExercise.IconImgPath = await SetAttachmentIfHas(vm.NewExercise.IconImgPath, "imgPreview");

            if (vm.NewExercise.Subtext == null) vm.NewExercise.Subtext = "";

            Triggero.Domain.Jobs.PushNotifications.SendNewExerciseNotification(vm.NewExercise);

            DB.Exercises.Add(vm.NewExercise);
            await DB.SaveChangesAsync();
            return RedirectToAction("Exercises", "Exercises");
        }


        //[Route("UpdateExercise")]
        public IActionResult UpdateExercise(int id)
        {
            var vm = new CreateExerciseVM
            {
                Categories = DB.ExerciseCategories.ToList(),
                NewExercise = DB.Exercises.FirstOrDefault(o => o.Id == id)
            };
            return View(@"Views\Practices\Exercises\UpdateExercise.cshtml", vm);
        }

        [HttpPost]
        //[Route("UpdateExercisePOST"), HttpPost]
        public async Task<IActionResult> UpdateExercisePOST([FromForm] CreateExerciseVM vm)
        {
            vm.NewExercise.ImgPath = await SetAttachmentIfHas(vm.NewExercise.ImgPath, "img");
            vm.NewExercise.HeaderImgPath = await SetAttachmentIfHas(vm.NewExercise.HeaderImgPath, "headerImg");
            vm.NewExercise.IconImgPath = await SetAttachmentIfHas(vm.NewExercise.IconImgPath, "imgPreview");


            if (vm.NewExercise.Subtext == null) vm.NewExercise.Subtext = "";


            DB.Exercises.Update(vm.NewExercise);
            await DB.SaveChangesAsync();
            return RedirectToAction("Exercises", "Exercises");
        }


        [HttpDelete]
        //[Route("DeleteExercise"), HttpDelete]
        public async Task<IActionResult> DeleteExercise(int id)
        {
            var exercise = DB.Exercises.FirstOrDefault(o => o.Id == id);
            exercise.IsDeleted = true;
            DB.Exercises.Update(exercise);
            await DB.SaveChangesAsync();
            return RedirectToAction("Exercises", "Exercises");
        }

        [HttpGet]
        public async Task<IActionResult> SetVisibility(int id, bool hidden)
        {
            var exercise = DB.Exercises.FirstOrDefault(o => o.Id == id);
            exercise.IsHidden = hidden;
            DB.Exercises.Update(exercise);
            await DB.SaveChangesAsync();
            return RedirectToAction("Exercises", "Exercises");
        }

    }
}
