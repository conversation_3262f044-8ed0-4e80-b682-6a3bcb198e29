﻿using MobileAPIWrapper.Methods.Library;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MobileAPIWrapper.MethodGroupings
{
    public class LibraryMethods
    {
        public ExercisesMethods ExercisesMethods { get; set; } = new ExercisesMethods();
        public PracticesMethods PracticesMethods { get; set; } = new PracticesMethods();
        public TopicsMethods TopicsMethods { get; set; } = new TopicsMethods();
        public BreathPracticesMethods BreathPractices { get; set; } = new BreathPracticesMethods();
    }
}
