﻿using DrawnUi.Maui.Controls;
using DrawnUi.Maui.Draw;
using DrawnUi.Maui.Infrastructure.Models;
using Triggero.Models.Practices.Categories;

namespace Triggero.MauiClient.Views
{
    /// <summary>
    ///  For faster rendering we are not using Maui Bindings that act on UI thread
    /// </summary>
    public class FastCellWithBanner : SkiaDynamicDrawnCell
    {


        public override void OnVisibilityChanged(bool newvalue)
        {
            base.OnVisibilityChanged(newvalue);

            if (!newvalue)
            {
                _appeared = false;
            }
        }

        protected override void OnBindingContextChanged()
        {
            base.OnBindingContextChanged();

            if (!_appeared)
            {
                _appeared = true;
                Opacity = 0;
                FadeToAsync(1, 500, Easing.Linear);
            }
        }

        public override void OnScrolled()
        {
            base.OnScrolled();

            if (Drawer != null && !Drawer.IsOpen)
            {
                var thisY = this.DrawingRect.Top;
                if (Math.Abs(thisY - _lastY) > 50) //pixels
                {
                    Drawer.IsOpen = true;
                }
            }
            else
            {
                _lastY = this.DrawingRect.Top;
            }
        }

        public FastCellWithBanner()
        {

        }

        bool once;
        private double _lastY;
        private bool _appeared;
        protected SkiaDrawer Drawer;
        protected SkiaLabel LabelId;
        protected SkiaLabel LabelTitle;
        protected SkiaLabel LabelDesc;
        protected SkiaImage ImageBanner;
        protected SkiaControl ImagePlaceholder;

        public override void OnDisposing()
        {
            if (ImageBanner != null)
            {
                ImageBanner.OnSuccess -= OnImageLoaded;
                ImageBanner.OnCleared -= OnImageCleared;
            }

            base.OnDisposing();

            Drawer = null;
            LabelTitle = null;
            LabelDesc = null;
            ImageBanner = null;
            ImagePlaceholder = null;
            LabelId = null;
        }

        private void OnImageLoaded(object sender, ContentLoadedEventArgs e)
        {
            if (ImagePlaceholder != null)
                ImagePlaceholder.IsVisible = false;
        }

        private void OnImageCleared(object sender, EventArgs e)
        {
            if (ImagePlaceholder != null)
                ImagePlaceholder.IsVisible = true;
        }

        void FindViews()
        {
            if (Drawer == null)
            {
                Drawer = FindView<SkiaDrawer>("Drawer");
            }
            if (LabelId == null)
            {
                LabelId = FindView<SkiaLabel>("LabelId");
            }
            if (LabelTitle == null)
            {
                LabelTitle = FindView<SkiaLabel>("LabelTitle");
            }
            if (LabelDesc == null)
            {
                LabelDesc = FindView<SkiaLabel>("LabelDesc");
            }
            if (ImageBanner == null)
            {
                ImageBanner = FindView<SkiaImage>("ImageBanner");
                if (ImageBanner != null)
                {
                    ImageBanner.OnSuccess += OnImageLoaded;
                    ImageBanner.OnCleared += OnImageCleared;
                }
            }
            if (ImagePlaceholder == null)
            {
                ImagePlaceholder = FindViewByTag("ImagePlaceholder");
            }
        }

        int contextChanged;

        protected virtual bool ApplyContext()
        {
            _lastY = this.DrawingRect.Top;
            if (Drawer != null && !Drawer.IsOpen)
            {
                Drawer.IsOpen = true;
            }

            if (BindingContext is IHasDisplayInfo item)
            {
                contextChanged++;

                //if (LabelId != null)
                //{
                //    LabelId.Text = item.Id.ToString();
                //}
                if (LabelTitle != null)
                {
                    LabelTitle.Text = item.Title;
                }
                if (LabelDesc != null)
                {
                    LabelDesc.Text = item.Description;
                }

                //if (ImagePlaceholder != null)
                //{
                //    ImagePlaceholder.IsVisible = !string.IsNullOrEmpty(item.Banner);
                //}

                if (ImageBanner != null)
                {
                    //mageBanner.StopLoading();
                    //item.BannerPreloadOrdered = true;
                    ImageBanner.Source = item.ImgPath;
                }

                return true;
            }

            return false;
        }

        protected override void SetContent()
        {
            if (!once)
            {
                once = true;
                FindViews();
            }




            var set = ApplyContext();


        }






    }
}
