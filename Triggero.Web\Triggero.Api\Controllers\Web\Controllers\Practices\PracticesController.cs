﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Practices;

namespace Triggero.Web.Controllers.Practices
{
    [Authorize]
    public class PracticesController : AbsController
    {
        public PracticesController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Practices")]
        public IActionResult Practices()
        {
            var practices = DB.Practices.Where(o => !o.IsDeleted).ToList();
            practices.Reverse();
            return View(@"Views\Practices\Practices\Practices.cshtml", practices);
        }


        //[Route("CreatePractice")]
        public IActionResult CreatePractice()
        {
            var vm = new CreatePracticeVM
            {
                Categories = DB.PracticeCategories.ToList(),
            };
            return View(@"Views\Practices\Practices\CreatePractice.cshtml", vm);
        }

        [HttpPost]
        //[Route("CreatePracticePOST"), HttpPost]
        public async Task<IActionResult> CreatePracticePOST([FromForm] CreatePracticeVM vm)
        {
            vm.NewPractice.AudioPath = await SetAttachmentIfHas(vm.NewPractice.AudioPath, "audio");
            vm.NewPractice.ImgPath = await SetAttachmentIfHas(vm.NewPractice.ImgPath, "img");
            vm.NewPractice.IconImgPath = await SetAttachmentIfHas(vm.NewPractice.IconImgPath, "imgPreview");


            if (vm.NewPractice.Description == null) vm.NewPractice.Description = "";

            Triggero.Domain.Jobs.PushNotifications.SendNewPracticeNotification(vm.NewPractice);

            DB.Practices.Add(vm.NewPractice);
            await DB.SaveChangesAsync();
            return RedirectToAction("Practices", "Practices");
        }


        //[Route("UpdatePractice")]
        public IActionResult UpdatePractice(int id)
        {
            var practice = DB.Practices.FirstOrDefault(o => o.Id == id);
            var vm = new CreatePracticeVM
            {
                Categories = DB.PracticeCategories.ToList(),
                NewPractice = practice
            };
            return View(@"Views\Practices\Practices\UpdatePractice.cshtml", vm);
        }

        [HttpPost]
        //[Route("UpdatePracticePOST"), HttpPost]
        public async Task<IActionResult> UpdatePracticePOST([FromForm] CreatePracticeVM vm)
        {
            vm.NewPractice.AudioPath = await SetAttachmentIfHas(vm.NewPractice.AudioPath, "audio");
            vm.NewPractice.ImgPath = await SetAttachmentIfHas(vm.NewPractice.ImgPath, "img");
            vm.NewPractice.IconImgPath = await SetAttachmentIfHas(vm.NewPractice.IconImgPath, "imgPreview");

            if (vm.NewPractice.Description == null) vm.NewPractice.Description = "";

            DB.Practices.Update(vm.NewPractice);
            await DB.SaveChangesAsync();
            return RedirectToAction("Practices", "Practices");
        }

        [HttpDelete]
        //[Route("DeletePractice"), HttpDelete]
        public async Task<IActionResult> DeletePractice(int id)
        {
            var practice = DB.Practices.FirstOrDefault(o => o.Id == id);
            practice.IsDeleted = true;
            DB.Practices.Update(practice);
            await DB.SaveChangesAsync();
            return RedirectToAction("Practices", "Practices");
        }

        [HttpGet]
        public async Task<IActionResult> SetVisibility(int id, bool hidden)
        {
            var practice = DB.Practices.FirstOrDefault(o => o.Id == id);
            practice.IsHidden = hidden;
            DB.Practices.Update(practice);
            await DB.SaveChangesAsync();
            return RedirectToAction("Practices", "Practices");
        }
    }
}
