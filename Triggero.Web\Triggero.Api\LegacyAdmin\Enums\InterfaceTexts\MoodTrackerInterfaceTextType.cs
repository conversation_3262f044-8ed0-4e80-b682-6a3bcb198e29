﻿using System.ComponentModel;

namespace Triggero.Web.Enums.InterfaceTexts
{
    public enum MoodTrackerInterfaceTextType
    {
        [Description("Трекер настроения - уточнение")]
        TrackerFactorsDetailsPageLocalization = 1,
        [Description("Трекер настроения - что повлияло")]
        TrackerFactorsPageLocalization = 2,
        [Description("Трекер настроения - финальная страница")]
        TrackerFinalPageLocalization = 3,
        [Description("Трекер настроения - общее")]
        TrackerGeneralLocalization = 4,
        [Description("Трекер настроения - как ты")]
        TrackerHowAreYouLocalization = 5,
        [Description("Трекер настроения - главная страница")]
        TrackerMainPageLocalization =6,
        [Description("Трекер настроения - начало")]
        TrackerStartLocalization = 7
    }
}
