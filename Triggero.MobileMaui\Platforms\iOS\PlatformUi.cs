using DrawnUi.Models;
using Foundation;
using Triggero.Mobile.Abstractions;
using UIKit;

namespace Triggero.MobileMaui.Platforms.iOS
{
    public class PlatformUi : IPlatformUi
    {
        public PlatformUi()
        {
            Instance = this;
        }

        public void Init(params object[] args)
        {
            try
            {
                Screen.Density = UIScreen.MainScreen.Scale;
                Screen.WidthDip = UIScreen.MainScreen.Bounds.Width;
                Screen.HeightDip = UIScreen.MainScreen.Bounds.Height;

                if (UIDevice.CurrentDevice.CheckSystemVersion(11, 0))
                {
                    var window = new UIWindow(frame: UIScreen.MainScreen.Bounds)
                    { 
                        BackgroundColor = UIColor.Clear // TODO: was Xamarin.Forms.Color.Transparent.ToUIColor()
                    };

                    Screen.TopInset = (int)(window.SafeAreaInsets.Top);
                    Screen.BottomInset = (int)(window.SafeAreaInsets.Bottom);
                    Screen.LeftInset = (int)(window.SafeAreaInsets.Left);
                    Screen.RightInset = (int)(window.SafeAreaInsets.Right);
                }

                // TODO: Xamarin code - Super.StatusBarHeight and Super.NavBarHeight
                // Original code set Super.StatusBarHeight = Screen.TopInset or 20
                // Original code set Super.NavBarHeight = 47
                SetDefaultHeights();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PlatformUi] Init failed: {ex.Message}");
            }
        }

        private void SetDefaultHeights()
        {
            // TODO: Xamarin code - needs adaptation for DrawnUI MAUI
            // Super.StatusBarHeight = Screen.TopInset;
            // if (Super.StatusBarHeight == 0)
            //     Super.StatusBarHeight = 20;
            // Super.NavBarHeight = 47;
        }

        public static PlatformUi? Instance { get; set; }

        public Screen Screen { get; } = new();

        public void ApplyTheme()
        {
            // TODO: Xamarin code was empty for iOS
            // Theme application might need implementation
        }

        public bool OpenUrl(string url)
        {
            try
            {
                var nsUrl = new NSUrl(url); // e.g., "prefs:root=Settings"

                if (!UIApplication.SharedApplication.CanOpenUrl(nsUrl))
                    return false;

                // TODO: Xamarin code used UIApplication.SharedApplication.OpenUrl(nsUrl)
                // MAUI might need different approach
                UIApplication.SharedApplication.OpenUrl(nsUrl);
                return true;
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.WriteLine($"[PlatformUi] OpenUrl failed: {e.Message}");
                return false;
            }
        }

        public void HideStatusBar()
        {
            try
            {
                UIApplication.SharedApplication.StatusBarHidden = true;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PlatformUi] HideStatusBar failed: {ex.Message}");
            }
        }

        public void ShowStatusBar()
        {
            try
            {
                UIApplication.SharedApplication.StatusBarHidden = false;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PlatformUi] ShowStatusBar failed: {ex.Message}");
            }
        }

        public void Command(string command)
        {
            try
            {
                if (command == "ExternalPaymentLink")
                {
                    // TODO: Xamarin code was empty for this command
                    // External payment link handling might need implementation
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PlatformUi] Command failed: {ex.Message}");
            }
        }
    }
}
