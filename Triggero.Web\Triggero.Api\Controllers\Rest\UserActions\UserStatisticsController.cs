﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Models.General;
using Triggero.Models.General.UserStats;

namespace Triggero.Domain.Controllers.Rest.General.Users
{
    [ApiController]
    [Route("[controller]")]
    public class UserStatisticsController : ApiController
    {
        public UserStatisticsController(DatabaseContext db) : base(db)
        {
        }

        #region Статистика по прохождению тестов, практик, упражнений и дыши и др.

        [HttpGet, Route("GetUserStatistics")]
        public async Task<UserStatistics> GetUserStatistics(int userId)
        {
            var user = DB.Users.Include(o => o.UserStatistics).ThenInclude(o => o.TestPassingResults)
                               .Include(o => o.UserStatistics).ThenInclude(o => o.BreathPracticePassingResults)
                               .Include(o => o.UserStatistics).ThenInclude(o => o.PracticePassingResults)
                               .Include(o => o.UserStatistics).ThenInclude(o => o.ExercisePassingResults)
                               .FirstOrDefault(o => o.Id == userId);
            SetCounters(user.UserStatistics);
            return user.UserStatistics;
        }


        [HttpGet, Route("GetPassedTests")]
        public async Task<List<TestPassingResult>> GetPassedTests(int userId)
        {
            var user = DB.Users.Include(o => o.UserStatistics).ThenInclude(o => o.TestPassingResults)
                               .FirstOrDefault(o => o.Id == userId);
            return user.UserStatistics.TestPassingResults;
        }
        [HttpGet, Route("GetPassedExercises")]
        public async Task<List<ExercisePassingResult>> GetPassedExercises(int userId)
        {
            var user = DB.Users.Include(o => o.UserStatistics).ThenInclude(o => o.ExercisePassingResults)
                               .FirstOrDefault(o => o.Id == userId);
            return user.UserStatistics.ExercisePassingResults;
        }






        [HttpPut, Route("AddTestPassingResult")]
        public async Task AddTestPassingResult(int userId, TestPassingResult result)
        {
            var user = DB.Users.Include(o => o.UserStatistics)
                               .FirstOrDefault(o => o.Id == userId);

            user.UserStatistics.TestPassingResults.Add(result);
            DB.Users.Update(user);
            DB.SaveChanges();
        }


        [HttpPut, Route("AddExercisePassingResult")]
        public async Task AddExercisePassingResult(int userId, ExercisePassingResult result)
        {
            var user = DB.Users.Include(o => o.UserStatistics).ThenInclude(o => o.ExercisePassingResults)
                               .FirstOrDefault(o => o.Id == userId);

            var found = user.UserStatistics.ExercisePassingResults.FirstOrDefault(o => o.ExerciseId == result.ExerciseId);
            if (found == null)
            {
                user.UserStatistics.ExercisePassingResults.Add(result);
                DB.Users.Update(user);
                DB.SaveChanges();
            }
            else
            {
                found.EmojiType = result.EmojiType;
                DB.ExercisePassingResult.Update(found);
                DB.SaveChanges();
            }
        }


        [HttpPut, Route("AddPracticePassingResult")]
        public async Task AddPracticePassingResult(int userId, PracticePassingResult result)
        {
            var user = DB.Users.Include(o => o.UserStatistics)
                               .FirstOrDefault(o => o.Id == userId);

            if (!user.UserStatistics.PracticePassingResults.Any(o => o.PracticeId == result.Id))
            {
                user.UserStatistics.PracticePassingResults.Add(result);
                DB.Users.Update(user);
                DB.SaveChanges();
            }


        }


        [HttpPut, Route("AddBreathPracticePassingResult")]
        public async Task AddBreathPracticePassingResult(int userId, BreathPracticePassingResult result)
        {
            var user = DB.Users.Include(o => o.UserStatistics)
                               .FirstOrDefault(o => o.Id == userId);

            if (!user.UserStatistics.BreathPracticePassingResults.Any(o => o.BreathPracticeId == result.Id))
            {
                user.UserStatistics.BreathPracticePassingResults.Add(result);
                DB.Users.Update(user);
                DB.SaveChanges();
            }
        }

        #endregion


        #region Private methods

        private void SetCounters(UserStatistics stats)
        {
            stats.ExercisePassingResultsCount = stats.ExercisePassingResults.Count;
            stats.TestPassingResultsCount = stats.TestPassingResults.Count;
            stats.PracticePassingResultsCount = stats.PracticePassingResults.Count;
            stats.BreathPracticePassingResultsCount = stats.BreathPracticePassingResults.Count;

            stats.ExercisePassingResults.Clear();
            stats.TestPassingResults.Clear();
            stats.PracticePassingResults.Clear();
            stats.BreathPracticePassingResults.Clear();
        }

        #endregion
    }
}
