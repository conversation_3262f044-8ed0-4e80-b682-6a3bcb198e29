﻿<?xml version = "1.0" encoding = "UTF-8" ?>
<Application xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:local="clr-namespace:Triggero.MauiClient"
             x:Class="Triggero.MauiClient.App">
    <Application.Resources>
        <ResourceDictionary>
            <ResourceDictionary.MergedDictionaries>
                <ResourceDictionary Source="Resources/Styles/Colors.xaml" />
                <ResourceDictionary Source="Resources/Styles/Styles.xaml" />
            </ResourceDictionary.MergedDictionaries>
        </ResourceDictionary>


        <!--<converters:TimeSpanToStringConverter x:Key="TimeSpanToStringConverter" />
        <converters:IntegerNotZeroConverter x:Key="IntegerNotZeroConverter" />
        <converters:IntegerIsZeroConverter x:Key="IntegerIsZeroConverter" />

        <toolkit:InvertedBoolConverter x:Key="NotConverter" />
        <toolkit:IsNotNullOrEmptyConverter x:Key="IsNotNullOrEmptyConverter" />

        <appoMobi:CompareIntegersConverter x:Key="CompareIntegersConverter" />-->


    </Application.Resources>
</Application>
