﻿using Triggero.Mobile.Abstractions;
using Triggero.MobileMaui.Services;

namespace Triggero.MobileMaui
{
    public partial class App : Application
    {
        // TODO: Replace DependencyService.Get<IPlatformUi>() calls with this property
        public static IPlatformUi PlatformUi => ServiceHelper.PlatformUi;

        public App()
        {
            InitializeComponent();
        }

        protected override Window CreateWindow(IActivationState? activationState)
        {
            return new Window(new AppShell());
        }
    }
}