﻿@using Triggero.Models
@using Triggero.Models.Plans
@using Triggero.Models.Practices
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "ПРОМОКОДЫ";
}
@model List<Promocode>
<!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                    <th scope="col">ID</th>
                    <th scope="col">Код</th>
                    <th scope="col">Процент</th>
                    <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model)
                    {
                         <tr>
                            <th scope="row">#@item.Id</th>
                            <td>@item.Code</td>
                            <td>@item.Value</td>
                            <td>
                                <div class="interect">
                                    <a asp-action="UpdatePromocode" asp-controller="Promocodes" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    <a onclick="confirmDeleting('deleteItem(@item.Id)')" class="delete"><i class="fa-solid fa-trash"></i></a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

            <div class="mb-3 position-right">
                <a asp-action="CreatePromocode" asp-controller="Promocodes" class="button-classic">Добавить</a>
            </div>

            <script>
                $(document).ready( function () {
                    $('#tablemanager').DataTable();
                } );
            </script>

            <script>
                async function deleteItem(id){
                    await fetch(document.location.origin + '/DeletePromocode?id=' + id,
                    {
                        method: 'DELETE'
                    });
                }
            </script>

        </div>
    </div>
</div>