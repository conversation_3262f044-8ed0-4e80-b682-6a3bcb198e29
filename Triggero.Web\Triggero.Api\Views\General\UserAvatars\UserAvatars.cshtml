﻿@using Triggero.Models
@using Triggero.Models.General
@using Triggero.Models.Practices
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "АВАТАРЫ";
}
@model List<UserAvatar>
<!-- CONTENT -->
<div class="content">
    <div class="row">


        @foreach (var item in Model)
        {
             <div class="col-md-2">
                <a href="" class="lang">
                    <div class="content">
                        <div class="flag" style="background: url('@item.AvatarPath');background-size: 100% 100%;"></div>
                        <div class="name"></div>
                    </div>
                </a>
            </div>
        }

        <div class="col-md-2">
            <a asp-action="CreateUserAvatar" asp-controller="UserAvatars" class="lang">
                <div class="content">
                    <div class="flag"></div>
                    <div class="name">Новый аватар</div>
                </div>
            </a>
        </div>
    </div>
</div>