﻿
using AppoMobi.Specials;
using Triggero.Mobile.Enums;
using ThumbnailSize = Triggero.Domain.Models.Enums.ThumbnailSize;

namespace Triggero.MauiClient
{
    public partial class App : Microsoft.Maui.Controls.Application
    {
        public App()
        {
            InitializeComponent();

            MainPage = new AppShell();
        }

        public static App Instance
        {
            get
            {
                return App.Current as App;
            }
        }

        #region HELPERS


        public static string GetFullImageUrl(string fromServer, ThumbnailSize size, ThumbnailType type)
        {
            var filename = Path.GetFileName(fromServer);

            var url = $"{Constants.UrlContentThumbnails}/{(int)size}/{type.ToString().ToLower()}/{filename}";

            return url;
        }

        public static void UpdateState()
        {
            Tasks.StartDelayed(TimeSpan.FromMilliseconds(10), () =>
            {
                Super.NeedGlobalUpdate();
            });
        }

        public static App This => App.Current as App;



        public bool WasShownEndSubscriptionPopup { get; set; } = false;

        #endregion


        public static void OpenPage(Page page)
        {
            async Task Act()
            {
                try
                {
                    await App.Current.MainPage.Navigation.PushAsync(page);

                    UpdateState();
                }
                catch (Exception e)
                {
                    Super.Log(e);
                }
            }

            if (MainThread.IsMainThread)
            {
                Act().ConfigureAwait(false);
            }
            else
                MainThread.BeginInvokeOnMainThread(async () =>
                {
                    await Act();
                });

        }


    }
}
