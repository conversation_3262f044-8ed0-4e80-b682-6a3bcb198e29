﻿using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Triggero.Database;

namespace Triggero.Domain.Abstractions
{
    public abstract class ApiController : ControllerBase
    {
        protected readonly DatabaseContext DB;
        public ApiController(DatabaseContext db)
        {
            DB = db;
        }

        [NonAction]
        public string MakeNewtonsoftJsonString<T>(T model)
        {
            var testJson = JsonConvert.SerializeObject(model);
            return testJson;
        }
    }
}
