!function(e){"use strict";e.extend(!0,e.trumbowyg,{langs:{en:{lineheight:"Line height",lineheights:{.9:"Small",normal:"Regular",1.5:"Large","2.0":"Extra large"}},sl:{lineheight:"<PERSON><PERSON><PERSON><PERSON> vrstice",lineheights:{.9:"<PERSON><PERSON><PERSON>",normal:"Navadna",1.5:"Veli<PERSON>","2.0":"Ekstra velika"}},by:{lineheight:"Міжрадковы інтэрвал",lineheights:{.9:"Маленькі",normal:"Звычайны",1.5:"Вялікі","2.0":"Вельмі вялікі"}},da:{lineheight:"<PERSON>jehøjde",lineheights:{.9:"Lille",normal:"Normal",1.5:"Stor","2.0":"Eks<PERSON> stor"}},et:{lineheight:"<PERSON><PERSON><PERSON>",lineheights:{.9:"Väike",normal:"Tavaline",1.5:"Suur","2.0":"Väga suur"}},fr:{lineheight:"Hauteur de ligne",lineheights:{.9:"Petite",normal:"Normale",1.5:"Grande","2.0":"Très grande"}},hu:{lineheight:"Line height",lineheights:{.9:"Small",normal:"Regular",1.5:"Large","2.0":"Extra large"}},it:{lineheight:"Altezza linea",lineheights:{.9:"Bassa",normal:"Normale",1.5:"Alta","2.0":"Molto alta"}},ko:{lineheight:"줄 간격",lineheights:{.9:"좁게",normal:"보통",1.5:"넓게","2.0":"아주 넓게"}},nl:{lineheight:"Regelhoogte",lineheights:{.9:"Klein",normal:"Normaal",1.5:"Groot","2.0":"Extra groot"}},pt_br:{lineheight:"Altura de linha",lineheights:{.9:"Pequena",normal:"Regular",1.5:"Grande","2.0":"Extra grande"}},ru:{lineheight:"Межстрочный интервал",lineheights:{.9:"Маленький",normal:"Обычный",1.5:"Большой","2.0":"Очень большой"}},tr:{lineheight:"Satır yüksekliği",lineheights:{.9:"Küçük",normal:"Normal",1.5:"Büyük","2.0":"Çok Büyük"}},zh_tw:{lineheight:"文字間距",lineheights:{.9:"小",normal:"正常",1.5:"大","2.0":"特大"}}}});var n={sizeList:["0.9","normal","1.5","2.0"]};function i(n){var i=[];return e.each(n.o.plugins.lineheight.sizeList,(function(t,l){n.addBtnDef("lineheight_"+l,{text:n.lang.lineheights[l]||l,hasIcon:!1,fn:function(){if(n.saveRange(),""!==n.getRangeText().replace(/\s/g,""))try{var i=function(){var e,n=null;window.getSelection?(e=window.getSelection()).rangeCount&&1!==(n=e.getRangeAt(0).commonAncestorContainer).nodeType&&(n=n.parentNode):(e=document.selection)&&"Control"!==e.type&&(n=e.createRange().parentElement());return n}();e(i).css("lineHeight",l)}catch(e){}}}),i.push("lineheight_"+l)})),i}e.extend(!0,e.trumbowyg,{plugins:{lineheight:{init:function(t){t.o.plugins.lineheight=e.extend({},n,t.o.plugins.lineheight||{}),t.addBtnDef("lineheight",{dropdown:i(t)})}}}})}(jQuery);