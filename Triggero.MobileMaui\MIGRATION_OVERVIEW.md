# Triggero MAUI Migration Overview

## Project Context
This document provides a comprehensive guide for continuing the Triggero Xamarin.Forms to .NET MAUI migration project.

## Migration Strategy
The migration follows a structured 5-phase approach designed to minimize risk and ensure systematic progress.

### Target Platforms
- **Primary Development**: Windows (for fast iteration, hot reload, quick compilation)
- **Production Platforms**: Android, iOS
- **Excluded**: MacCatalyst (explicitly not supported in this port)

### Development Workflow
1. **Windows-First Development**: Develop UI and logic on Windows platform first
2. **Cross-Platform Testing**: Regular builds with `dotnet build -f net9.0-android --configuration Debug`
3. **Mobile Testing**: Test on physical devices after Windows development is stable

## Migration Phases

### Phase 1: Foundation (IN PROGRESS)
**Status**: Platform Services ✅ COMPLETED, DrawnUI Integration PENDING

#### Completed:
- ✅ Platform Services Migration (Xamarin DependencyService → MAUI Dependency Injection)
- ✅ Windows Platform Implementation
- ✅ Android Platform Implementation  
- ✅ iOS Platform Implementation
- ✅ DrawnUI Package Installation (`AppoMobi.Maui.DrawnUi` v1.5.1.4)
- ✅ Cross-platform build verification

#### Pending:
- [ ] Setup DrawnUI MAUI Integration
- [ ] Migrate Core Dependencies

### Phase 2: UI Framework Migration
- Replace CardsView with DrawnUI SkiaShape
- Port custom renderers to MAUI handlers
- Migrate XAML layouts and styles

### Phase 3: Screen Migration (Phone Only)
- Port Phone screens (omit Tablet screens)
- Update navigation patterns
- Migrate view models and business logic

### Phase 4: Platform Features
- Audio integration (Plugin.Maui.Audio)
- Platform-specific implementations
- Hardware feature integration

### Phase 5: Testing & Optimization
- Comprehensive testing on target platforms
- Performance optimization
- Final validation

## Key Technical Decisions

### Package Strategy
- **DrawnUI**: `AppoMobi.Maui.DrawnUi` (MAUI version) replaces `AppoMobi.Xamarin.DrawnUi`
- **Audio**: `Plugin.Maui.Audio` replaces Xamarin audio solutions
- **CardsView**: Replace with DrawnUI SkiaShape components

### Code Migration Patterns
- **Always add TODO comments** when omitting/replacing Xamarin code
- **Prefer copying uncompilable Xamarin code with comments** over silently skipping functionality
- **Use `.ToPlatform()` extension** for converting MAUI colors to native platform colors
- **Port only Phone screens**, omit Tablet-specific implementations

### Platform Service Architecture
- **Pattern**: Xamarin DependencyService → MAUI Dependency Injection
- **Registration**: Conditional compilation with `#if ANDROID`, `#if IOS`, `#if WINDOWS`
- **Access**: ServiceHelper class provides DependencyService-like access pattern
- **Compatibility**: Maintained similar API surface for easier migration

## Project Structure
```
Triggero.MobileMaui/
├── Abstractions/           # Platform service interfaces
├── Platforms/
│   ├── Android/           # Android-specific implementations
│   ├── iOS/               # iOS-specific implementations
│   └── Windows/           # Windows-specific implementations
├── Services/              # Cross-platform service registration
└── *.md                   # Migration documentation
```

## Build Commands
- **Windows**: `dotnet build -f net9.0-windows10.0.19041.0 --configuration Debug`
- **Android**: `dotnet build -f net9.0-android --configuration Debug`
- **iOS**: `dotnet build -f net9.0-ios --configuration Debug`

## Next Steps for Continuation
1. Read all migration .md files in this directory
2. Review current codebase state using codebase-retrieval tool
3. Continue with pending Phase 1 tasks (DrawnUI integration)
4. Follow the structured phase approach
5. Maintain Windows-first development workflow
