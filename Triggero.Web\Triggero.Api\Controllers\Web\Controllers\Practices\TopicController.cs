﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Practices;

namespace Triggero.Web.Controllers.Practices
{
    [Authorize]
    public class TopicController : AbsController
    {
        public TopicController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Topics")]
        public IActionResult Topics()
        {
            var topics = DB.Topics.Where(o => !o.IsDeleted).ToList();
            topics.Reverse();
            return View(@"Views\Practices\Topics\Topics.cshtml", topics);
        }


        //[Route("CreateTopic")]
        public IActionResult CreateTopic()
        {
            var vm = new CreateTopicVM
            {
                Categories = DB.TopicCategories.ToList(),
            };
            return View(@"Views\Practices\Topics\CreateTopic.cshtml", vm);
        }

        [HttpPost]
        //[Route("CreateTopicPOST"), HttpPost]
        public async Task<IActionResult> CreateTopicPOST([FromForm] CreateTopicVM vm)
        {
            vm.NewTopic.ImgPath = await SetAttachmentIfHas(vm.NewTopic.ImgPath, "file");
            vm.NewTopic.IconImgPath = await SetAttachmentIfHas(vm.NewTopic.IconImgPath, "imgPreview");

            Triggero.Domain.Jobs.PushNotifications.SendNewTopicNotification(vm.NewTopic);

            DB.Topics.Add(vm.NewTopic);
            await DB.SaveChangesAsync();
            return RedirectToAction("Topics", "Topic");
        }

        //[Route("UpdateTopic")]
        public IActionResult UpdateTopic(int id)
        {
            var topic = DB.Topics.FirstOrDefault(o => o.Id == id);
            var vm = new CreateTopicVM
            {
                Categories = DB.TopicCategories.ToList(),
                NewTopic = topic
            };
            return View(@"Views\Practices\Topics\UpdateTopic.cshtml", vm);
        }

        [HttpPost]
        //[Route("UpdateTopicPOST"), HttpPost]
        public async Task<IActionResult> UpdateTopicPOST([FromForm] CreateTopicVM vm)
        {
            vm.NewTopic.ImgPath = await SetAttachmentIfHas(vm.NewTopic.ImgPath, "file");
            vm.NewTopic.IconImgPath = await SetAttachmentIfHas(vm.NewTopic.IconImgPath, "imgPreview");
            DB.Topics.Update(vm.NewTopic);
            await DB.SaveChangesAsync();
            return RedirectToAction("Topics", "Topic");
        }

        [HttpDelete]
        //[Route("DeleteTopic"), HttpDelete]
        public async Task<IActionResult> DeleteTopic(int id)
        {
            var topic = DB.Topics.FirstOrDefault(o => o.Id == id);
            topic.IsDeleted = true;
            DB.Topics.Update(topic);
            await DB.SaveChangesAsync();
            return RedirectToAction("Topics", "Topic");
        }

        [HttpGet]
        public async Task<IActionResult> SetVisibility(int id, bool hidden)
        {
            var topic = DB.Topics.FirstOrDefault(o => o.Id == id);
            topic.IsHidden = hidden;
            DB.Topics.Update(topic);
            await DB.SaveChangesAsync();
            return RedirectToAction("Topics", "Topic");
        }
    }
}
