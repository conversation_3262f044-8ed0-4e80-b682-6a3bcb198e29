﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Localization.Practices.Categories;
using Triggero.Models.Localization.Tests;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;
using Triggero.Models.Tests;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class TestCategoriesLocalizationsController : AbsController
    {
        public TestCategoriesLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/TestCategories")]
        public IActionResult TestCategories()
        {
            var cats = DB.TestCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            cats.Reverse();

            var vm = new LocalizationListVM<TestCategory>
            {
                Items = cats,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Practices\Categories\TestCategories\TestCategories.cshtml", vm);
        }







        [Route("Localizations/CreateTestCategoryLocalization")]
        public IActionResult CreateTestCategoryLocalization(int id)
        {
            var vm = new LocalizationVM<TestCategoryLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Practices\Categories\TestCategories\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreateTestCategoryLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateTestCategoryLocalizationPOST([FromForm] LocalizationVM<TestCategoryLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();

            var cat = DB.TestCategories.FirstOrDefault(o => o.Id == vm.Id);
            cat.Localizations.Add(vm.Localization);

            DB.TestCategories.Update(cat);
            await DB.SaveChangesAsync();
            return RedirectToAction("TestCategories", "TestCategoriesLocalizations");
        }





        [Route("Localizations/UpdateTestCategoryLocalization")]
        public IActionResult UpdateTestCategoryLocalization(int id)
        {
            var cat = DB.TestCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<TestCategoryLocalization>
            {
                Id = id,
                Localization = cat.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Practices\Categories\TestCategories\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdateTestCategoryLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateTestCategoryLocalizationPOST([FromForm] LocalizationVM<TestCategoryLocalization> vm)
        {
            DB.TestCategoryLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("TestCategories", "TestCategoriesLocalizations");
        }

    }
}
