/**
 * Trumbowyg v2.26.0 - A lightweight WYSIWYG editor
 * Default stylesheet for Trumbowyg editor plugin
 * ------------------------
 * @link http://alex-d.github.io/Trumbowyg
 * @license MIT
 * <AUTHOR> (Alex-D)
 *         Twitter : @AlexandreDemode
 *         Website : alex-d.fr
 */

.trumbowyg-dropdown-foreColor:not(.trumbowyg-dropdown--color-list),
.trumbowyg-dropdown-backColor:not(.trumbowyg-dropdown--color-list) {
    max-width: 276px;
    padding: 7px 5px;
    overflow: initial;

    button {
        display: block;
        position: relative;
        float: left;
        text-indent: -9999px;
        height: 20px;
        width: 20px;
        border: 1px solid #333;
        padding: 0;
        margin: 2px;

        &:hover,
        &:focus {
            &::after {
                content: " ";
                display: block;
                position: absolute;
                top: -5px;
                left: -5px;
                width: 27px;
                height: 27px;
                background: inherit;
                border: 1px solid #fff;
                box-shadow: #000 0 0 2px;
                z-index: 10;
            }
        }
    }
}

.trumbowyg-dropdown-backColor.trumbowyg-dropdown--color-list {
    button:not(.trumbowyg-backColorRemove-dropdown-button) {
        position: relative;
        color: #fff !important;

        &:hover,
        &:focus {
            &::after {
                content: " ";
                display: block;
                position: absolute;
                top: 13px;
                left: 0;
                width: 0;
                height: 0;
                border: 5px solid transparent;
                border-left-color: #fff;
            }
        }
    }
}
