﻿<Project Sdk="Microsoft.NET.Sdk">

	<PropertyGroup>
		<TargetFrameworks>net9.0-android;net9.0-ios</TargetFrameworks>
		<TargetFrameworks Condition="$([MSBuild]::IsOSPlatform('windows'))">$(TargetFrameworks);net9.0-windows10.0.19041.0</TargetFrameworks>
		<!-- Uncomment to also build the tizen app. You will need to install tizen by following this: https://github.com/Samsung/Tizen.NET -->
		<!-- <TargetFrameworks>$(TargetFrameworks);net9.0-tizen</TargetFrameworks> -->

		<!-- MacCatalyst support explicitly excluded from this port -->

		<OutputType>Exe</OutputType>
		<RootNamespace>Triggero.MobileMaui</RootNamespace>
		<UseMaui>true</UseMaui>
		<SingleProject>true</SingleProject>
		<ImplicitUsings>enable</ImplicitUsings>
		<Nullable>enable</Nullable>

		<!-- Display name -->
		<ApplicationTitle>Triggero.MobileMaui</ApplicationTitle>

		<!-- App Identifier -->
		<ApplicationId>com.companyname.triggero.mobilemaui</ApplicationId>

		<!-- Versions -->
		<ApplicationDisplayVersion>1.0</ApplicationDisplayVersion>
		<ApplicationVersion>1</ApplicationVersion>

		<!-- To develop, package, and publish an app to the Microsoft Store, see: https://aka.ms/MauiTemplateUnpackaged -->
		<WindowsPackageType>None</WindowsPackageType>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'ios'">15.0</SupportedOSPlatformVersion>

		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'android'">21.0</SupportedOSPlatformVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</SupportedOSPlatformVersion>
		<TargetPlatformMinVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'windows'">10.0.17763.0</TargetPlatformMinVersion>
		<SupportedOSPlatformVersion Condition="$([MSBuild]::GetTargetPlatformIdentifier('$(TargetFramework)')) == 'tizen'">6.5</SupportedOSPlatformVersion>
	</PropertyGroup>

	<ItemGroup>
		<!-- App Icon -->
		<MauiIcon Include="Resources\AppIcon\appicon.svg" ForegroundFile="Resources\AppIcon\appiconfg.svg" Color="#512BD4" />

		<!-- Splash Screen -->
		<MauiSplashScreen Include="Resources\Splash\splash.svg" Color="#512BD4" BaseSize="128,128" />

		<!-- Images -->
		<MauiImage Include="Resources\Images\*" />
		<MauiImage Update="Resources\Images\dotnet_bot.png" Resize="True" BaseSize="300,185" />

		<!-- Custom Fonts -->
		<MauiFont Include="Resources\Fonts\*" />

		<!-- Raw Assets (also remove the "Resources\Raw" prefix) -->
		<MauiAsset Include="Resources\Raw\**" LogicalName="%(RecursiveDir)%(Filename)%(Extension)" />
	</ItemGroup>

	<ItemGroup>
	  <MauiFont Remove="Resources\Fonts\OpenSans-Bold.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-BoldItalic.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-ExtraBold.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-ExtraBoldItalic.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-Italic.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-Light.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-LightItalic.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-Medium.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-MediumItalic.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-Regular.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-Semibold.ttf" />
	  <MauiFont Remove="Resources\Fonts\OpenSans-SemiBoldItalic.ttf" />
	</ItemGroup>

	<ItemGroup>
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-Bold.ttf" />
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-BoldItalic.ttf" />
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-ExtraBold.ttf" />
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-ExtraBoldItalic.ttf" />
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-Italic.ttf" />
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-Light.ttf" />
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-LightItalic.ttf" />
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-Medium.ttf" />
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-MediumItalic.ttf" />
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-Regular.ttf" />
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-Semibold.ttf" />
	  <EmbeddedResource Include="Resources\Fonts\OpenSans-SemiBoldItalic.ttf" />
	</ItemGroup>

	<ItemGroup>
		<PackageReference Include="AppoMobi.Maui.DrawnUi" Version="1.5.1.4" />
		<PackageReference Include="Microsoft.Maui.Controls" Version="$(MauiVersion)" />
		<PackageReference Include="Microsoft.Extensions.Logging.Debug" Version="9.0.0" />
	</ItemGroup>

</Project>
