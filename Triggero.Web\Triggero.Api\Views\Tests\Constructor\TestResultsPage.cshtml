﻿@using Triggero.Models.Tests;
@model Test


<div class="steps">
    <div class="step">
        <div class="circle">1</div>
        <div class="text">НАСТРОЙКА СТРАНИЦЫ</div>
        <div class="arrow"><i class="fa-solid fa-arrow-right"></i></div>
    </div>

    <div class="step">
        <div class="circle">2</div>
        <div class="text">ВОПРОСЫ</div>
        <div class="arrow"><i class="fa-solid fa-arrow-right"></i></div>
    </div>

    <div class="step active">
        <div class="circle">3</div>
        <div class="text">РЕЗУЛЬТАТЫ</div>
    </div>
</div>


<form asp-action="SaveTestResultsPageImg" asp-controller="Tests" asp-route-testId="@Model.Id" method="post" id="form" enctype="multipart/form-data" class="mb-3 mt-3">

    <input type="hidden" asp-for="ImgPath" value="@Model.ImgPath" />
    <input type="hidden" id="redirectURLHidden" name="redirectURL" value="" />

    <label for="" class="formlabel">Загрузить изображение</label>
    <div class="content-load" style="">
        <div class="close-file" id="close_file"><i class="fa-solid fa-xmark"></i></div>
        <label class="loadfile1" id="upload-image" for="pct" style="height: 6em;background-image: url('@Model.ResultImgPath');background-size: cover;background-repeat: no-repeat;">
            <div class="text-fileupload">
                @if (string.IsNullOrEmpty(@Model.ResultImgPath))
                {
                    @("Загрузите файл")
                }
            </div>
        </label>
        <input class="pctfile" type="file" name="file" id="pct">
    </div>
</form>

<div class="qcontent">
   
</div>

<div class="mb-3 buttons-que">
    <button class="button-plus" onclick="createResult()"><i class="fa-solid fa-plus"></i></button>
    <div>
        <a onclick="saveChangesAndGoBack()" class="button-classic">Назад</a>
        <a onclick="saveChanges()" class="button-classic">Завершить</a>
    </div>
</div>

 @section js{
    <script src="~/media/js/filesupload.js"></script>
    <script src="~/media/js/main.js"></script>

     <script>



        load();

       

        async function load(){

            mainjsTestId = @Model.Id;
            mainjsTestType = @((int)Model.Type);

            await loadTestScales(@Model.Id);
            await loadResults(@Model.Id);
        }

        async function saveChanges(){
            await saveResults(@Model.Id);
            callExitPopup = false;

            document.getElementById('redirectURLHidden').value = document.location.origin + `/Tests`;
            await cropFinal('pct', 'upload-image');
            document.getElementById('form').submit();
        }
        async function saveChangesAndGoBack() {
            await saveQuestions(@Model.Id);
            callExitPopup = false;

            document.getElementById('redirectURLHidden').value = document.location.origin + `/TestQuestions?id=@Model.Id`;
            await cropFinal('pct', 'upload-image');
            document.getElementById('form').submit();
        }

        let callExitPopup = true;

        window.onbeforeunload = function (e) {
            if (callExitPopup) {
                var message = "Возможно, внесенные изменения не сохранятся.",
                    e = e || window.event;
                // For IE and Firefox
                if (e) {
                    e.returnValue = message;
                }

                // For Safari
                return message;
            }
        };

    </script>



    <script>

        $(document).ready(function () {
            initImageCroppieSized('upload-image', 'pct', 'close_file',238,206);
        });


    </script>


}