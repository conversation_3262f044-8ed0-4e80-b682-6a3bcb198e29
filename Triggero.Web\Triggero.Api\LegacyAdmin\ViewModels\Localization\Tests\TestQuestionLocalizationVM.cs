﻿using Triggero.Models.Abstractions;
using Triggero.Models.General;
using Triggero.Models.Localization.Tests.Questions;

namespace Triggero.Web.ViewModels.Localization.Tests
{
    public class TestQuestionLocalizationVM
    {
        public int Id { get; set; }
        public QuestionLocalization Localization { get; set; } = new QuestionLocalization();
        public Question Question { get; set; } = new Question();

        public Language CurrentLanguage { get; set; } = new Language();
    }
}
