﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.VisualBasic.FileIO;
using Newtonsoft.Json;
using Newtonsoft.Json.Serialization;
using Triggero.Database;
using Triggero.Models.Abstractions;
using Triggero.Models.Enums;
using Triggero.Models.Tests;
using Triggero.Models.Tests.QuestionOptions;
using Triggero.Models.Tests.Questions;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Tests;

namespace Triggero.Web.Controllers
{
    [Authorize]
    public class TestsController : AbsController
    {
        public TestsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Tests")]
        public IActionResult Tests()
        {
            var tests = DB.Tests.Where(o => !o.IsDeleted).ToList();
            tests.Reverse();
            return View(@"Views\Tests\Tests.cshtml", tests);
        }


        [HttpGet]
        public async Task<IActionResult> SetVisibility(int id, bool hidden)
        {
            var topic = DB.Tests.FirstOrDefault(o => o.Id == id);
            topic.IsHidden = hidden;
            DB.Tests.Update(topic);
            await DB.SaveChangesAsync();
            return RedirectToAction("Tests", "Tests");
        }



        //[Route("TestStartPage")]
        public async Task<IActionResult> TestStartPage(int id = 0)
        {
            var test = new Test();
            if (id != 0)
            {
                test = DB.Tests.Include(o => o.Scales)
                               .FirstOrDefault(o => o.Id == id);
            }


            var vm = new TestStartPageVM()
            {
                Test = test,
                Categories = DB.TestCategories.ToList()
            };
            return View(@"Views\Tests\Constructor\TestStartPage.cshtml", vm);
        }

        //[HttpPost, Route("SaveTestStartPage")]
        [HttpPost]
        public async Task<IActionResult> SaveTestStartPage(TestStartPageVM vm)
        {
            vm.Test.ImgPath = await SetAttachmentIfHas(vm.Test.ImgPath, "file");
            vm.Test.IconImgPath = await SetAttachmentIfHas(vm.Test.IconImgPath, "imgPreview");

            if (vm.Test.Id == 0)
            {
                Triggero.Domain.Jobs.PushNotifications.SendNewTestNotification(vm.Test);
            }

            DB.Tests.Update(vm.Test);
            await DB.SaveChangesAsync();

            return RedirectToAction("TestQuestions", "Tests", new { id = vm.Test.Id });
        }



        [HttpPost]
        //[HttpPost, Route("RemoveTestScales")]
        public async Task RemoveTestScales(int testId, [FromBody] List<int> scaleIds)
        {

            try
            {
                var test = DB.Tests.Include(o => o.Scales)
                               .Include(o => o.Results)
                               .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.ScaleScoreInfos)
                               .FirstOrDefault(o => o.Id == testId);

                foreach (var scaleId in scaleIds)
                {

                    var scaleInTest = test.Scales.FirstOrDefault(o => o.Id == scaleId);
                    if (scaleInTest != null)
                    {
                        test.Scales.Remove(scaleInTest);
                    }

                    var questionsOptions = test.Questions.SelectMany(o => o.Options);
                    foreach (var option in questionsOptions)
                    {

                        var scaleInOption = option.ScaleScoreInfos.FirstOrDefault(o => o.ScaleId == scaleId);
                        if (scaleInOption != null)
                        {
                            option.ScaleScoreInfos.Remove(scaleInOption);
                        }
                    }

                    foreach (var result in test.Results)
                    {
                        if (result.ScaleId == scaleId)
                        {
                            result.ScaleId = null;
                        }
                    }
                }

                DB.Tests.Update(test);
                await DB.SaveChangesAsync();
            }
            catch (Exception ex)
            {

            }
        }

        [HttpPost]
        //[HttpPost, Route("UpdateTestScales")]
        public async Task UpdateTestScales(int testId, [FromBody] List<TestScale> scales)
        {

            try
            {
                var test = DB.Tests
                    .Include(o => o.Questions).ThenInclude(o => o.Options)
                    .FirstOrDefault(o => o.Id == testId);

                foreach (var scale in scales)
                {

                    if (scale.Id == 0)
                    {

                        test.Scales.Add(scale);
                        foreach (var option in test.Questions.SelectMany(o => o.Options))
                        {
                            option.ScaleScoreInfos.Add(new QuestionOptionScaleInfo
                            {
                                Scale = scale,
                            });
                        }
                    }
                    else
                    {
                        DB.TestScales.Update(scale);
                    }

                }

                DB.Tests.Update(test);
                await DB.SaveChangesAsync();

            }
            catch (Exception ex)
            {

            }
        }




        //[Route("TestQuestions")]
        public async Task<IActionResult> TestQuestions(int id)
        {
            var test = DB.Tests.Include(o => o.Scales)
                               .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.ScaleScoreInfos)
                               .FirstOrDefault(o => o.Id == id);



            //Добавляем шкалы к вопросам, если пользователь поменял тип теста с бесшкального на шкальный
            if (test.Type == TestType.Scaled || test.Type == TestType.ScaledWithConditions)
            {

                foreach (var option in test.Questions.SelectMany(o => o.Options))
                {
                    foreach (var scale in test.Scales)
                    {
                        if (!option.ScaleScoreInfos.Any(o => o.ScaleId == scale.Id))
                        {
                            option.ScaleScoreInfos.Add(new QuestionOptionScaleInfo
                            {
                                ScaleId = scale.Id,
                            });
                        }
                    }
                }
                DB.Tests.Update(test);
                await DB.SaveChangesAsync();
            }


            return View(@"Views\Tests\Constructor\TestQuestions.cshtml", test);
        }

        [HttpPost]
        //[HttpPost, Route("SaveTestQuestions")]
        public async Task<IActionResult> SaveTestQuestions()
        {
            var req = Request;

            int testId = Convert.ToInt32(Request.Form["testId"]);
            string questionsJson = Request.Form["questions"];

            var questions = JsonConvert.DeserializeObject<List<Question>>(questionsJson);
            var questionIdsToDelete = JsonConvert.DeserializeObject<List<int>>(Request.Form["questionIdsToDelete"]);
            var optionIdsToDelete = JsonConvert.DeserializeObject<List<int>>(Request.Form["optionIdsToDelete"]);
            try
            {
                foreach (var id in questionIdsToDelete)
                {
                    var item = DB.Questions.FirstOrDefault(o => o.Id == id);
                    item.IsDeleted = true;
                    DB.Questions.Update(item);
                }
                foreach (var id in optionIdsToDelete)
                {
                    var item = DB.QuestionOptions.Include(o => o.ScaleScoreInfos).FirstOrDefault(o => o.Id == id);
                    DB.QuestionOptionScaleInfo.RemoveRange(item.ScaleScoreInfos);
                    DB.QuestionOptions.Remove(item);
                }
                await DB.SaveChangesAsync();



                int filesCounter = 0;
                foreach (var question in questions)
                {
                    if (question is PictureQuestion imgQ)
                    {
                        foreach (var option in imgQ.Options)
                        {
                            if (option is PictureQuestionOption picOption && picOption.FilesFromFrontend > 0)
                            {
                                picOption.ImgPath = await SetAttachmentIfHas(picOption.ImgPath, filesCounter++);
                            }
                        }
                    }
                    else if (question is SimplePicturedQuestion withImgQ)
                    {
                        if (withImgQ.FilesFromFrontend > 0)
                        {
                            withImgQ.ImgPath = await SetAttachmentIfHas(withImgQ.ImgPath, filesCounter++);
                        }
                    }
                }



                var test = DB.Tests.FirstOrDefault(o => o.Id == testId);
                test.Questions = questions;



                DB.Tests.Update(test);
                await DB.SaveChangesAsync();
            }
            catch (Exception ex)
            {

            }

            return RedirectToAction("TestResultsPage", "Tests", new { id = testId });
        }

        //[Route("TestResultsPage")]
        public IActionResult TestResultsPage(int id)
        {
            var test = DB.Tests.Include(o => o.Scales)
                               .FirstOrDefault(o => o.Id == id);
            return View(@"Views\Tests\Constructor\TestResultsPage.cshtml", test);
        }

        [HttpPost]
        //[HttpPost, Route("SaveTestResultsPage")]
        public async Task<IActionResult> SaveTestResultsPage()
        {
            int testId = Convert.ToInt32(Request.Form["testId"]);
            string resultsJson = Request.Form["results"];

            var results = JsonConvert.DeserializeObject<List<TestResult>>(resultsJson);
            var resultIdsToDelete = JsonConvert.DeserializeObject<List<int>>(Request.Form["resultIdsToDelete"]);

            try
            {
                foreach (var id in resultIdsToDelete)
                {
                    var item = DB.TestResults.FirstOrDefault(o => o.Id == id);
                    item.IsDeleted = true;
                    DB.TestResults.Update(item);
                }
                await DB.SaveChangesAsync();



                int filesCounter = 0;
                foreach (var result in results)
                {
                    if (result.FilesFromFrontend > 0)
                    {
                        result.ImgPath = await SetAttachmentIfHas(result.ImgPath, filesCounter++);
                    }

                    foreach (var id in result.NeedToHandleItemsIds)
                    {
                        result.NeedToHandleItems.Add(DB.NeedToHandle.FirstOrDefault(o => o.Id == id));
                    }
                }




                var test = DB.Tests.FirstOrDefault(o => o.Id == testId);
                test.Results = results;



                DB.Tests.Update(test);
                await DB.SaveChangesAsync();
            }
            catch (Exception ex)
            {

            }

            return RedirectToAction("Tests", "Tests");
        }

        [HttpPost]
        //[HttpPost, Route("SaveTestResultsPageImg")]
        public async Task<IActionResult> SaveTestResultsPageImg(int testId, string redirectURL)
        {
            var test = DB.Tests.FirstOrDefault(o => o.Id == testId);
            test.ResultImgPath = await SetAttachmentIfHas(test.ResultImgPath);

            DB.Tests.Update(test);
            await DB.SaveChangesAsync();



            return Redirect(redirectURL);
        }


        [HttpDelete]
        //[Route("DeleteTest"), HttpDelete]
        public async Task<IActionResult> DeleteTest(int id)
        {
            var test = DB.Tests.FirstOrDefault(o => o.Id == id);
            test.IsDeleted = true;
            DB.Tests.Update(test);
            await DB.SaveChangesAsync();
            return RedirectToAction("Tests", "Tests");
        }


        #region JSON
        //[Route("GetTestQuestions")]
        public string GetTestQuestions(int id)
        {
            var test = DB.Tests
                .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.ScaleScoreInfos).ThenInclude(o => o.Scale)
                .FirstOrDefault(o => o.Id == id);


            var testJson = JsonConvert.SerializeObject(test.Questions.Where(o => !o.IsDeleted).OrderBy(o => o.Number).ToList(),
                new JsonSerializerSettings
                {
                    ContractResolver = new CamelCasePropertyNamesContractResolver()
                });
            return testJson;
        }


        //[Route("GetTestScales")]
        public IActionResult GetTestScales(int id)
        {
            var test = DB.Tests
                .Include(o => o.Scales)
                .FirstOrDefault(o => o.Id == id);


            return Json(test.Scales);
        }


        //[Route("GetTestResults")]
        public IActionResult GetTestResults(int id)
        {
            var test = DB.Tests
                .Include(o => o.Results).ThenInclude(o => o.NeedToHandleItems)
                .FirstOrDefault(o => o.Id == id);

            foreach (var item in test.Results.SelectMany(o => o.NeedToHandleItems))
            {
                item.TestResultReferences.Clear();
            }

            return Json(test.Results.Where(o => !o.IsDeleted));
        }


        //[Route("GetNeedToHandleItems")]
        public IActionResult GetNeedToHandleItems()
        {
            return Json(DB.NeedToHandle.Where(o => !o.IsDeleted).ToList());
        }

        #endregion


    }
}
