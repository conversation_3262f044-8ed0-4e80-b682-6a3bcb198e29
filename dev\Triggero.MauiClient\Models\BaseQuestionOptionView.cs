﻿using System;
using System.Collections.Generic;
using System.Text;
using Triggero.Controls.Cards.Tests.Questions.Answers.Single;
using Triggero.Models.Abstractions;
using Xamarin.Forms;

namespace Triggero.Mobile.Models
{
    public class BaseQuestionOptionView : ContentView
    {
        public BaseQuestionOptionView(QuestionOption option)
        {
            Option = option;
        }

        private QuestionOption option;
        public QuestionOption Option
        {
            get { return option; }
            set { option = value; OnPropertyChanged(nameof(Option)); }
        }

        public static readonly BindableProperty IsCheckedProperty = BindableProperty.Create(nameof(IsChecked), typeof(bool), typeof(PictureAnswerOption));
        public bool IsChecked
        {
            get { return (bool)GetValue(IsCheckedProperty); }
            set { SetValue(IsCheckedProperty, value); }
        }

    }
}
