﻿using AppoMobi.Specials;
using AppoMobi.Tests.Models;
using FluentAssertions;
using MapsterMapper;
using System;
using System.IO;
using System.Net;
using System.Net.Http;
using System.Net.Http.Headers;
using System.Text;
using System.Threading.Tasks;
using Triggero.Application.Providers.ForaBank;

namespace AppoMobi.Tests;

public class ApiTests : TestsBase
{

    [Test]
    [TestCase("1TEST")]
    public async Task CreateOrder(string clientId)
    {
        var service = TestHost.Services.GetService<ForaBankService>();

        var orderNb = $"{clientId}-{RndExtensions.CreateRandom(1, 99999)}";

        var result = await service.CreateMobileOrderAsync(
            clientId,
            orderNb,
            10000,
            "Tests",
            "https://triggero.ru/lk", null);

        result.Succeeded.Should().BeTrue();

        result.Data.OrderId.Should().NotBeEmpty();

        TestContext.WriteLine($"Check: " +
                              $"Now: {DateTime.UtcNow}\n" +
                              $"{Json(result.Data)}");
    }

    [Test]
    [TestCase("e2cb874c-f45c-7062-add6-c64b00dd8ec4")]
    public async Task CheckOrder(string orderNb)
    {
        var service = TestHost.Services.GetService<ForaBankService>();

        var result = await service.CheckOrderAsync(
            orderNb);

        result.Succeeded.Should().BeTrue();

        result.Data.OrderDescription.Should().Be("Tests");

        TestContext.WriteLine($"Check: " +
                              $"Now: {DateTime.UtcNow}\n" +
                              $"{Json(result.Data)}");
    }

}