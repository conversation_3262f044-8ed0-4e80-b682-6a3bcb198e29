﻿@using MarkupCreator.Helpers.Converters
@using Triggero.Models.Tickets
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "ТИКЕТ";
}
@model TicketPageVM


 <!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="chat">
            <div class="chat-header">
                <div class="profile">
                    <div class="avatar"></div>
                    <div class="info">
                        <div class="username">@Model.Ticket.Title</div>
                       @* <div class="status">
                            <div class="online"></div>
                            <div class="text">Online</div>
                        </div>*@
                    </div>
                </div>
            </div>

            <div class="chat-body">
                <div class="left">
                    <div class="avatar"></div>
                    <div class="message">
                        @Model.Ticket.Description
                    </div>
                </div>

                @foreach (var msg in Model.Ticket.Messages)
                {
                    if (msg.SentBy.Id == Model.Ticket.CreatedBy.Id)
                    {
                        <div class="left">
                            <div class="avatar" style="background: url('@msg.SentBy.Avatar.AvatarPath')"></div>
                            <div class="message">
                                @Model.Ticket.Description
                            </div>
                            @{
                                int counter = 1;
                            }
                            @foreach(var attachment in msg.Attachments)
                            {
                                <a asp-action="GetAttachment" asp-controller="Tickets" asp-route-path="@attachment.Path">Вложение @counter</a>
                                counter++;
                            }
                        </div>
                    }
                    else
                    {
                         <div class="right">
                            <div class="avatar" style="background: url('@msg.SentBy.Avatar.AvatarPath')"></div>
                            <div class="message">
                                @Model.Ticket.Description
                            </div>
                            @{
                                int counter = 1;
                            }
                            @foreach(var attachment in msg.Attachments)
                            {
                                <a asp-action="GetAttachment" asp-controller="Tickets" asp-route-path="@attachment.Path">Вложение @counter</a>
                                counter++;
                            }
                        </div>
                    }
                }

            </div>

            <form asp-action="SendMessage" asp-controller="Tickets" method="post" enctype="multipart/form-data" class="chat-footer">

                <input type="hidden" asp-for="Ticket.Id" value="@Model.Ticket.Id" />

                <input type="text" asp-for="NewMessage.Message" class="form-control form-send" placeholder="Введите сообщение">
                <button class="button-classic button-icons"><i class="fa-solid fa-folder"></i></button>
                <button class="button-classic">Отправить</button>
            </form>
            
            <div class="chat-footer">
                <a asp-action="SetTicketStatus" asp-controller="Tickets" asp-route-ticketId="@Model.Ticket.Id" asp-route-status="3">
                    <button class="button-classic">Закрыть тикет</button>
                </a>
                <a asp-action="SetTicketStatus" asp-controller="Tickets" asp-route-ticketId="@Model.Ticket.Id" asp-route-status="2">
                    <button class="button-classic">Установить статус "В рассмотрении"</button>
                </a>
            </div>
        </div>
    </div>
</div>