﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models.Enums;
using Triggero.Models.Messengers.ChatBot;
using Triggero.Models.Messengers.Support;
using Triggero.Models.Tickets;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels;
using Triggero.Web.ViewModels.Tickets;

namespace Triggero.Web.Controllers.Chats
{
    [Authorize]
    public class SupportChatController : AbsController
    {
        public SupportChatController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Support/Chats")]
        public IActionResult Chats(int page = 1)
        {
            var chats = DB.SupportChats
                .Include(o => o.Messages)
                .Include(o => o.User).ThenInclude(o => o.Avatar)
                .Where(o => o.Messages.Any())
                .OrderByDescending(o => o.CreatedAt);


            var vm = new PaginationVM<SupportChat>()
            {
                CurrentPage = page,
                PagesCount = (int)Math.Floor((double)chats.Count() / (double)25),
                Items = chats.Skip(25*(page-1)).Take(25).ToList()
            };
            return View(@"Views\Chats\Support\Chats.cshtml", vm);
        }

        [Route("Support/ChatPage")]
        public IActionResult ChatPage(int id)
        {
            var chat = DB.SupportChats
                .Include(o => o.User).ThenInclude(o => o.Avatar)
                .Include(o => o.Messages)
                .FirstOrDefault(o => o.Id == id);
            return View(@"Views\Chats\Support\ChatPage.cshtml", chat);
        }

        [Route("Support/SendMessage"), HttpPost]
        public async Task<IActionResult> SendMessage(int chatId,SupportChatMessage msg)
        {
            var chat = DB.SupportChats.FirstOrDefault(o => o.Id == chatId);
            msg.ChatSide = ChatSide.Admin;
            chat.Messages.Add(msg);

            DB.SupportChats.Update(chat);
            await DB.SaveChangesAsync();
            return RedirectToAction("Chats", "SupportChat");
        }
    }
}
