﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
             xmlns:views="clr-namespace:Triggero.MobileMaui.Views"
             x:Class="Triggero.MobileMaui.MainPage"
             x:Name="ThisPage"
             BackgroundColor="White">

    <!-- Root layout grid with switchable views -->
    <Grid RowDefinitions="*,Auto" RowSpacing="0">

        <!-- HOME VIEW -->
        <Grid x:Name="MainViewGrid"
              Grid.Row="0"
              Padding="0"
              IsVisible="True">
            <!-- HomeView will be added here dynamically -->
        </Grid>

        <!-- LIBRARY VIEW -->
        <Grid x:Name="LibraryGrid"
              Grid.Row="0"
              Padding="0"
              IsVisible="False">
            <!-- LibraryView will be added here dynamically -->
        </Grid>

        <!-- TESTS VIEW -->
        <Grid x:Name="TestsGrid"
              Grid.Row="0"
              Padding="0"
              IsVisible="False">
            <!-- TestsView will be added here dynamically -->
        </Grid>

        <!-- CHATBOT VIEW -->
        <Grid x:Name="ChatBotGrid"
              Grid.Row="0"
              Padding="0"
              IsVisible="False">
            <!-- ChatBotView will be added here dynamically -->
        </Grid>

        <!-- FOOTER NAVIGATION -->
        <views:FooterNavigation x:Name="Footer"
                               Grid.Row="1"
                               MainPage="{Binding Source={x:Reference ThisPage}}" />

    </Grid>

</ContentPage>
