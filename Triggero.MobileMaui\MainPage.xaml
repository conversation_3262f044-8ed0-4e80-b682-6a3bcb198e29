﻿<?xml version="1.0" encoding="utf-8" ?>
<ContentPage xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
             x:Class="Triggero.MobileMaui.MainPage">

    <ScrollView>
        <VerticalStackLayout
            Padding="30,0"
            Spacing="25">
            <Image
                Source="dotnet_bot.png"
                HeightRequest="185"
                Aspect="AspectFit"
                SemanticProperties.Description="dot net bot in a hovercraft number nine" />

            <Label
                Text="Hello, World!"
                Style="{StaticResource Headline}"
                SemanticProperties.HeadingLevel="Level1" />

            <Label
                Text="Welcome to &#10;.NET Multi-platform App UI"
                Style="{StaticResource SubHeadline}"
                SemanticProperties.HeadingLevel="Level2"
                SemanticProperties.Description="Welcome to dot net Multi platform App U I" />

            <Button
                x:Name="CounterBtn"
                Text="Click me"
                SemanticProperties.Hint="Counts the number of times you click"
                Clicked="OnCounterClicked"
                HorizontalOptions="Fill" />

            <!-- DrawnUI Test Section -->
            <Label Text="DrawnUI Integration Test:" FontSize="18" FontAttributes="Bold" />

            <draw:Canvas HorizontalOptions="Fill" HeightRequest="200" BackgroundColor="LightBlue">
                <draw:SkiaLayout Type="Column" Padding="16" Spacing="12">
                    <draw:SkiaLabel
                        Text="✅ DrawnUI is working!"
                        FontSize="16"
                        TextColor="DarkBlue"
                        HorizontalOptions="Center" />

                    <draw:SkiaButton
                        x:Name="DrawnUiTestBtn"
                        Text="Test Platform Services"
                        BackgroundColor="Orange"
                        TextColor="White"
                        Tapped="OnDrawnUiTestClicked"
                        HorizontalOptions="Center" />

                    <draw:SkiaLabel
                        x:Name="PlatformInfoLabel"
                        Text="Platform info will appear here..."
                        FontSize="12"
                        TextColor="DarkGreen"
                        HorizontalOptions="Center" />
                </draw:SkiaLayout>
            </draw:Canvas>

        </VerticalStackLayout>
    </ScrollView>

</ContentPage>
