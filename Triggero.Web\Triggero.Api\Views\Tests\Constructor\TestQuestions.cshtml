﻿@using Triggero.Models.Tests
@model Test


@section css{
    <link rel="stylesheet" href="~/media/css/emojionearea.min.css">

    <script type="text/javascript" src="~/media/js/emojionearea.min.js"></script>
}


    <div class="steps">
        <div class="step">
            <div class="circle">1</div>
            <div class="text">НАСТРОЙКА СТРАНИЦЫ</div>
            <div class="arrow"><i class="fa-solid fa-arrow-right"></i></div>
        </div>

        <div class="step active">
            <div class="circle">2</div>
            <div class="text">ВОПРОСЫ</div>
            <div class="arrow"><i class="fa-solid fa-arrow-right"></i></div>
        </div>

        <div class="step">
            <div class="circle">3</div>
            <div class="text">РЕЗУЛЬТАТЫ</div>
        </div>
    </div>

    <div class="createq">
       
    </div>

    <div class="mb-3 buttons-que">
        <button onclick="createQ()" class="button-plus"><i class="fa-solid fa-plus"></i></button>
        <div>
            <a onclick="saveChangesAndGoBack()" class="button-classic">Назад</a>
            <a onclick="saveChanges()" class="button-classic">Далее</a>
        </div>
 
    </div>

@section js{
    <script src="~/media/js/filesupload.js"></script>
    <script src="~/media/js/main.js"></script>

    <script>

       
        load();

        async function load(){
             mainjsTestId = @Model.Id;
             mainjsTestType = @((int)Model.Type);

             loadTestScales(@Model.Id);
             loadQuestions(@Model.Id);
        }

        async function saveChanges(){
            await saveQuestions(@Model.Id);
            callExitPopup = false;
            document.location = document.location.origin + `/TestResultsPage?id=@Model.Id`;
        }
        async function saveChangesAndGoBack() {
            await saveQuestions(@Model.Id);
            callExitPopup = false;
            document.location = document.location.origin + `/TestStartPage?id=@Model.Id`;
        }

        let callExitPopup = true;

        window.onbeforeunload = function (e) {
            if (callExitPopup){
                var message = "Возможно, внесенные изменения не сохранятся.",
                    e = e || window.event;
                // For IE and Firefox
                if (e) {
                    e.returnValue = message;
                }

                // For Safari
                return message;
            }              
        };

    </script>
}