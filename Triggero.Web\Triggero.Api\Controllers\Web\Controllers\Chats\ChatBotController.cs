﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models.Enums;
using Triggero.Models.Messengers.ChatBot;
using Triggero.Models.Tickets;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels;
using Triggero.Web.ViewModels.Tickets;

namespace Triggero.Web.Controllers.Chats
{
    [Authorize]
    public class ChatBotController : AbsController
    {
        public ChatBotController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("ChatBot/Chats")]
        public IActionResult Chats(int page = 1)
        {
            var chats = DB.ChatBotChats
                .Include(o => o.Messages)
                .Include(o => o.User).ThenInclude(o => o.Avatar)
                .Where(o => o.Severity == ChatBotChatSeverity.NeedHelp && o.Messages.Any())
                .OrderByDescending(o => o.CreatedAt);


            var vm = new PaginationVM<ChatBotChat>()
            {
                CurrentPage = page,
                PagesCount = (int)Math.Floor((double)chats.Count() / (double)25),
                Items = chats.Skip(25 * (page - 1)).Take(25).ToList()
            };
            return View(@"Views\Chats\ChatBot\Chats.cshtml", vm);
        }

        [Route("ChatBot/ChatPage")]
        public IActionResult ChatPage(int id)
        {
            var chat = DB.ChatBotChats
                .Include(o => o.User).ThenInclude(o => o.Avatar)
                .Include(o => o.Messages).ThenInclude(o => o.Attachments)
                .FirstOrDefault(o => o.Id == id);
            return View(@"Views\Chats\ChatBot\ChatPage.cshtml", chat);
        }

        [Route("ChatBot/SendMessage"), HttpPost]
        public async Task<IActionResult> SendMessage(int chatId, ChatBotMessage msg)
        {
            var chat = DB.ChatBotChats.FirstOrDefault(o => o.Id == chatId);
            msg.ChatSide = ChatSide.Admin;
            chat.Messages.Add(msg);

            DB.ChatBotChats.Update(chat);
            await DB.SaveChangesAsync();
            return RedirectToAction("Chats", "ChatBot");
        }
        [Route("ChatBot/SetStatus")]
        public async Task<IActionResult> SetStatus(int chatId, ChatBotChatSeverity status)
        {
            var chat = DB.ChatBotChats.FirstOrDefault(o => o.Id == chatId);
            chat.Severity = status;

            DB.ChatBotChats.Update(chat);
            await DB.SaveChangesAsync();

            return RedirectToAction("Chats", "ChatBot");
        }
    }
}
