﻿@using CRMWeb.Helpers.Html
@using Triggero.Models.Practices
@using Triggero.Models.Practices.Categories
@using Triggero.Web.ViewModels.Practices
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "СОЗДАНИЕ УПРАЖНЕНИЯ";
}
@model CreateExerciseVM

 <!-- CONTENT -->
<form asp-action="CreateExercisePOST" asp-controller="Exercises" onsubmit="onSubmit(event)" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">

            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="NewExercise.Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Подтекст</label>
                <input type="text" asp-for="NewExercise.Subtext" class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Текст</label>
                <textarea id="editor-input" required asp-for="NewExercise.Description" rows="3"></textarea>
                <script>
                    $('#editor-input')
                        .trumbowyg({
                            btnsDef: {
                                // Create a new dropdown
                                image: {
                                    dropdown: ['insertImage', 'noembed'],
                                    ico: 'insertImage'
                                }
                            },
                            // Redefine the button pane
                            btns: [
                                ['viewHTML'],
                                ['formatting'],
                                ['strong', 'em', 'del'],
                                ['superscript', 'subscript'],
                                ['link'],
                                ['image'], // Our fresh created dropdown
                                ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
                                ['unorderedList', 'orderedList'],
                                ['horizontalRule'],
                                ['removeformat'],
                                ['fullscreen']
                            ]
                        });
                </script>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Текст 2</label>
                <textarea id="editor-input2" required asp-for="NewExercise.Description2" rows="3"></textarea>
                <script>
                    $('#editor-input2')
                        .trumbowyg({
                            btnsDef: {
                                // Create a new dropdown
                                image: {
                                    dropdown: ['insertImage', 'noembed'],
                                    ico: 'insertImage'
                                }
                            },
                            // Redefine the button pane
                            btns: [
                                ['viewHTML'],
                                ['formatting'],
                                ['strong', 'em', 'del'],
                                ['superscript', 'subscript'],
                                ['link'],
                                ['image'], // Our fresh created dropdown
                                ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
                                ['unorderedList', 'orderedList'],
                                ['horizontalRule'],
                                ['removeformat'],
                                ['fullscreen']
                            ]
                        });
                </script>
            </div>
            
            <div class="mb-3">
                <label for="" class="formlabel">Ссылка на источник</label>
                <input type="url" asp-for="NewExercise.ReferenceLink" class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Может понадобиться</label>
                <textarea class="form-control" required asp-for="NewExercise.MayBeNeeded" rows="3" placeholder=""></textarea>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Время выполнения в минутах</label>
                <input type="number" asp-for="NewExercise.PassingTimeInMinutes" required class="form-control" placeholder="">
            </div>


            <div class="mb-3">
                <label for="" class="formlabel">Превью</label>
                <div class="content-load">
                    <div class="close-file" id="close_file3"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image3" for="pct3" style="height: 6em;"><div class="text-fileupload" id="uploadText3">Загрузите файл</div></label>
                    <input class="pctfile" type="file" name="imgPreview" id="pct3">
                </div>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Изображение в хедере</label>
                <div class="content-load">
                    <div class="close-file" id="close_file2"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image2" for="pct2" style="height: 6em;"><div class="text-fileupload" id="uploadText2">Загрузите файл</div></label>
                    <input class="pctfile" type="file" name="headerImg" id="pct2">
                </div>
            </div>


            <div class="mb-3">
                <label for="" class="formlabel">Изображение</label>
                <div class="content-load">
                    <div class="close-file" id="close_file"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image" for="pct" style="height: 6em;"><div class="text-fileupload" id="uploadText">Загрузите файл</div></label>
                    <input class="pctfile" type="file" name="img" id="pct">
                </div>
            </div>

            <div class="mb-3">
                 <label for="" class="formlabel">Категория</label>
                <select class="form-select" asp-for="NewExercise.CategoryId" required>
                    @Html.Raw(@ClassesToHtmlHelper<ExerciseCategory>.Convert(Model.Categories))
                </select>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Основные теги</label>
                <input type="text" asp-for="NewExercise.MainTags" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Вторичные теги</label>
                <input type="text" asp-for="NewExercise.SecondaryTags" required class="form-control" placeholder="">
            </div>


            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>

<script>

    async function onSubmit(event) {
        event.preventDefault();

        await cropFinal('pct', 'upload-image');
        await cropFinal('pct2', 'upload-image2');
        await cropFinal('pct3', 'upload-image3');
        event.target.submit();
    }

    $(document).ready(function () {
        initImageCroppieSized('upload-image', 'pct', 'close_file', 402, 278);
        initImageCroppieSized('upload-image2', 'pct2', 'close_file2', 562, 270);
        initImageCroppieSized('upload-image3', 'pct3', 'close_file3', 300, 300);
    });


</script>