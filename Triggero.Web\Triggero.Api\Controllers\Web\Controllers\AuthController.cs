﻿using CRMWeb.ViewModels.Home;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Mvc;
using System.Security.Claims;
using Triggero.Api;
using Triggero.Database;
using Triggero.Web.Abstractions;

namespace CRMWeb.Controllers
{
    public class AuthController : AbsController
    {

        public AuthController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        public IActionResult Index()
        {
            return new JsonResult("Auth");
        }

        [HttpPost]
        public async Task<IActionResult> Auth([FromForm] LoginVM vm)
        {
            if (vm.Login == Globals.LoginUser && vm.Password == Globals.LoginPass)
            {
                await MakeAuth();
                return RedirectToAction("Index", "Home");
            }
            return RedirectToAction("Login", "Home");
        }


        public async Task<IActionResult> Logout()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
            return RedirectToAction("Login", "Home");
        }

        [HttpGet]
        public bool HasUser(string login, string password)
        {
            return login == Globals.LoginUser && password == Globals.LoginPass;
        }

        private async Task<ClaimsPrincipal> MakeAuth()
        {
            await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);


            // создаем один claim
            var claims = new List<Claim>
            {
                 new Claim(ClaimsIdentity.DefaultNameClaimType, 2.ToString()),
                 new Claim(ClaimsIdentity.DefaultRoleClaimType, "Admin")
            };
            // создаем объект ClaimsIdentity
            ClaimsIdentity id = new ClaimsIdentity(claims, "ApplicationCookie", ClaimsIdentity.DefaultNameClaimType, ClaimsIdentity.DefaultRoleClaimType);
            var principal = new ClaimsPrincipal(id);
            // установка аутентификационных куки

            await HttpContext.SignInAsync(CookieAuthenticationDefaults.AuthenticationScheme, principal);

            return principal;
        }
    }
}
