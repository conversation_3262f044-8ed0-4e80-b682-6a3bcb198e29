﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;

namespace Triggero.Domain.Controllers.Rest.Library
{
    [ApiController]
    [Route("[controller]")]
    public class ExercisesController : ApiController
    {
        public ExercisesController(DatabaseContext db) : base(db)
        {
        }

        [ResponseCache(Duration = 120)]
        [HttpGet, Route("GetExerciseCategories")]
        public async Task<List<ExerciseCategory>> GetExerciseCategories()
        {
            return DB.ExerciseCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .ToList();
        }

        [ResponseCache(Duration = 120)]
        [HttpGet, Route("GetExercises")]
        public async Task<List<Exercise>> GetExercises()
        {
            return DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .ToList();
        }

        [ResponseCache(VaryByQueryKeys = new[] { "count", "offset" }, Duration = 120)]
        [HttpGet, Route("GetExercisesChunk")]
        public async Task<List<Exercise>> GetExercisesChunk(int count, int offset)
        {
            return DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Skip(offset).Take(count)
                .ToList();
        }

        [ResponseCache(VaryByQueryKeys = new[] { "categoryId" }, Duration = 120)]
        [HttpGet, Route("GetExercisesByCategory")]
        public async Task<List<Exercise>> GetExercisesByCategory(int categoryId)
        {
            return DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden && o.CategoryId == categoryId)
                .ToList();
        }

        [ResponseCache(VaryByQueryKeys = new[] { "categoryId", "count", "offset" }, Duration = 120)]
        [HttpGet, Route("GetExercisesByCategoryChunk")]
        public async Task<List<Exercise>> GetExercisesByCategoryChunk(int categoryId, int count, int offset)
        {
            return DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden && o.CategoryId == categoryId)
                .Skip(offset).Take(count)
                .ToList();
        }


        [ResponseCache(VaryByQueryKeys = new[] { "tag" }, Duration = 120)]
        [HttpGet, Route("GetExercisesByTag")]
        public async Task<List<Exercise>> GetExercisesByTag(string tag)
        {
            return DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.MainTags.Equals(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Equals(tag, StringComparison.OrdinalIgnoreCase))
                .ToList();
        }


        [ResponseCache(VaryByQueryKeys = new[] { "id" }, Duration = 120)]
        [HttpGet, Route("GetExercise")]
        public async Task<Exercise> GetExercise(int id)
        {
            return DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .FirstOrDefault(o => o.Id == id);
        }

        [HttpPut, Route("AddWatch")]
        public async Task AddWatch(int exerciseId)
        {
            var exercise = DB.Exercises.FirstOrDefault(o => o.Id == exerciseId);
            exercise.Watches++;

            DB.Exercises.Update(exercise);
            await DB.SaveChangesAsync();
        }

    }
}
