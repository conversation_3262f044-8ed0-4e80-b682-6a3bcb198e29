﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Models.Practices;

namespace Triggero.Domain.Controllers.Rest.Library
{
    [ApiController]
    [Route("[controller]")]
    public class BreathPracticesController : ApiController
    {
        public BreathPracticesController(DatabaseContext db) : base(db)
        {
        }


        [ResponseCache(Duration = 120)]
        [HttpGet, Route("GetBreathPractices")]
        public async Task<List<BreathPractice>> GetBreathPractices()
        {
            return DB.BreathPractices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
        }

        [ResponseCache(VaryByQueryKeys = new[] { "id" }, Duration = 120)]
        [HttpGet, Route("GetBreathPractice")]
        public async Task<BreathPractice> GetBreathPractice(int id)
        {
            return await DB.BreathPractices
                    .Include(o => o.Localizations).ThenInclude(o => o.Language)
                    .FirstOrDefaultAsync(o => o.Id == id);
        }



    }
}
