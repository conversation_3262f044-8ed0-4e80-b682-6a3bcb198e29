﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Practices;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class ExerciseLocalizationsController : AbsController
    {
        public ExerciseLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/Exercises")]
        public IActionResult Exercises()
        {
            var exercises = DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            exercises.Reverse();

            var vm = new LocalizationListVM<Exercise>
            {
                Items = exercises,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Practices\Exercises\Exercises.cshtml", vm);
        }







        [Route("Localizations/CreateExerciseLocalization")]
        public IActionResult CreateExerciseLocalization(int id)
        {
            var vm = new LocalizationVM<ExerciseLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Practices\Exercises\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreateExerciseLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateExerciseLocalizationPOST([FromForm] LocalizationVM<ExerciseLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();

            var exercise = DB.Exercises.FirstOrDefault(o => o.Id == vm.Id);
            exercise.Localizations.Add(vm.Localization);

            DB.Exercises.Update(exercise);
            await DB.SaveChangesAsync();
            return RedirectToAction("Exercises", "ExerciseLocalizations");
        }





        [Route("Localizations/UpdateExerciseLocalization")]
        public IActionResult UpdateExerciseLocalization(int id)
        {
            var exercise = DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<ExerciseLocalization>
            {
                Id = id,
                Localization = exercise.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Practices\Exercises\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdateExerciseLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateExerciseLocalizationPOST([FromForm] LocalizationVM<ExerciseLocalization> vm)
        {
            DB.ExerciseLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("Exercises", "ExerciseLocalizations");
        }

    }
}
