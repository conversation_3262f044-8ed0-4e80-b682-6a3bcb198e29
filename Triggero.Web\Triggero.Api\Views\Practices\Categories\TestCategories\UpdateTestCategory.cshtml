﻿@using CRMWeb.Helpers.Html
@using Triggero.Models.Practices
@using Triggero.Models.Practices.Categories
@using Triggero.Models.Tests
@using Triggero.Web.ViewModels.Practices
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "РЕДАКТИРОВАНИЕ КАТЕГОРИИ ТЕСТОВ";
}
@model TestCategory

 <!-- CONTENT -->
<form asp-action="UpdateTestCategoryPOST" asp-controller="TestCategories" onsubmit="onSubmit(event)" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">

            <input type="hidden" asp-for="Id" value="@Model.Id" />
            <input type="hidden" asp-for="ImgPath" value="@Model.ImgPath" />

            <div class="mb-3">
                <label for="" class="formlabel">Загрузить изображение</label>
                <div class="content-load" style="">
                    <div class="close-file" id="close_file"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image" for="pct" style="height: 6em;background-image: url('@Model.ImgPath');background-size: cover;background-repeat: no-repeat;">
                        <div class="text-fileupload">
                            @if (string.IsNullOrEmpty(Model.ImgPath))
                            {
                                @("Загрузите файл")
                            }
                        </div>
                    </label>
                    <input class="pctfile" type="file" name="file" id="pct">
                </div>
            </div>


            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Title" required class="form-control" placeholder="">
            </div>   

            <div class="mb-3">
                <label for="" class="formlabel">Описание</label>
                <textarea asp-for="Description" required class="form-control" placeholder="">@Model.Description</textarea>
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>

<script>

    async function onSubmit(event) {
        event.preventDefault();

        if (document.getElementById('pct').files.length > 0) {
            await cropFinal('pct', 'upload-image');
        }

        event.target.submit();
    }

    $(document).ready(function () {
        initImageCroppieSized('upload-image', 'pct', 'close_file', 300, 300);
    });


</script>