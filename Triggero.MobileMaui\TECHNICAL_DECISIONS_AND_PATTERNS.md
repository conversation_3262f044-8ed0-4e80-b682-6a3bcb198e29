# Technical Decisions and Patterns

## Overview
This document captures all technical decisions, coding patterns, and architectural choices made during the Triggero MAUI migration.

## Platform Strategy

### Target Platforms
- ✅ **Windows**: Primary development platform
- ✅ **Android**: Production target
- ✅ **iOS**: Production target  
- ❌ **MacCatalyst**: Explicitly excluded from this port

### Development Workflow
**Windows-First Approach**:
- Develop UI and logic on Windows for fast iteration
- Hot reload and quick compilation (50% faster development time)
- Cross-platform testing with regular Android/iOS builds
- Mobile device testing only after Windows stability

## Code Migration Patterns

### TODO Comment Strategy
**Always add TODO comments** when omitting or replacing Xamarin code:
```csharp
// TODO: MAUI Migration - Replace Xamarin dependency
// Original: DependencyService.Get<IService>()
var service = ServiceHelper.ServiceName;
```

### Preservation Over Omission
**Prefer copying uncompilable Xamarin code with comments** over silently skipping:
```csharp
/*
// Original Xamarin implementation
[assembly: Dependency(typeof(XamarinService))]
public class XamarinService : IService
{
    // Xamarin-specific implementation
}
*/

// TODO: Convert to MAUI dependency injection
public class MauiService : IService
{
    // MAUI implementation
}
```

### Screen Migration Focus
- **Include**: All Phone screens and layouts
- **Exclude**: All Tablet-specific implementations
- **Document**: Add TODO comments when omitting tablet code

## MAUI-Specific Patterns

### Color Conversion
**Use `.ToPlatform()` extension** for MAUI color conversion:
```csharp
// ✅ MAUI Pattern
Activity.Window.SetNavigationBarColor(mauiColor.ToPlatform());

// ❌ Avoid Xamarin patterns
Activity.Window.SetNavigationBarColor(xamarinColor.ToAndroid());
```

### Namespace Resolution
**Use global:: prefix** to avoid namespace conflicts:
```csharp
// ✅ Explicit namespace
var context = global::Android.App.Application.Context;

// ❌ Ambiguous reference
var context = Application.Context; // Could be Android.App or Microsoft.Maui.Controls
```

### Service Access Patterns
**Maintain compatibility** while modernizing:
```csharp
// Modern DI approach
public class ViewModel
{
    private readonly IPlatformUi _platformUi;
    
    public ViewModel(IPlatformUi platformUi)
    {
        _platformUi = platformUi;
    }
}

// Compatibility approach for existing code
public class LegacyCode
{
    public void DoSomething()
    {
        var platformUi = ServiceHelper.PlatformUi; // DependencyService-like access
    }
}
```

## Package Management Strategy

### Always Use Package Managers
**Never manually edit package files**. Use appropriate package managers:
```bash
# ✅ Correct approach
dotnet add package AppoMobi.Maui.DrawnUi

# ❌ Avoid manual editing
# Editing Triggero.MobileMaui.csproj directly
```

### Package Replacement Strategy
| Xamarin Package | MAUI Replacement | Status |
|----------------|------------------|---------|
| `AppoMobi.Xamarin.DrawnUi` | `AppoMobi.Maui.DrawnUi` | ✅ Installed |
| Xamarin Audio | `Plugin.Maui.Audio` | 📋 Planned |
| CardsView | DrawnUI SkiaShape | 📋 Planned |

## Build and Testing Patterns

### Build Commands
**Platform-specific builds** for testing:
```bash
# Windows development
dotnet build -f net9.0-windows10.0.19041.0 --configuration Debug

# Android verification  
dotnet build -f net9.0-android --configuration Debug

# iOS validation
dotnet build -f net9.0-ios --configuration Debug
```

### Testing Strategy
1. **Windows Development**: Primary development and testing
2. **Cross-Platform Builds**: Regular compilation verification
3. **Mobile Testing**: Physical device testing after Windows stability
4. **Automated Testing**: Unit tests for business logic

## Error Handling Patterns

### Platform-Specific Error Handling
```csharp
public void PlatformOperation()
{
    try
    {
        // Platform-specific code
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"[ServiceName] Operation failed: {ex.Message}");
        // Graceful degradation
    }
}
```

### Service Initialization
```csharp
public void Init(params object[] args)
{
    try
    {
        // Initialization logic
        System.Diagnostics.Debug.WriteLine("[ServiceName] Platform initialized");
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"[ServiceName] Init failed: {ex.Message}");
    }
}
```

## Conditional Compilation Patterns

### Platform-Specific Registration
```csharp
public static MauiAppBuilder AddPlatformServices(this MauiAppBuilder builder)
{
#if ANDROID
    builder.Services.AddSingleton<IPlatformUi, Platforms.Android.PlatformUi>();
#elif IOS
    builder.Services.AddSingleton<IPlatformUi, Platforms.iOS.PlatformUi>();
#elif WINDOWS
    builder.Services.AddSingleton<IPlatformUi, Platforms.Windows.PlatformUi>();
#endif
    return builder;
}
```

### Platform-Specific Implementations
```csharp
public void PlatformSpecificMethod()
{
#if ANDROID
    // Android-specific implementation
#elif IOS
    // iOS-specific implementation  
#elif WINDOWS
    // Windows-specific implementation
#else
    // Fallback implementation
#endif
}
```

## Memory Management

### Service Lifecycle
- **Singleton Services**: Platform services registered as singletons
- **Dispose Pattern**: Implement IDisposable for services with resources
- **Weak References**: Use for event subscriptions to prevent memory leaks

### Platform Resources
```csharp
public class PlatformService : IPlatformService, IDisposable
{
    private bool _disposed = false;
    
    public void Dispose()
    {
        if (!_disposed)
        {
            // Clean up platform resources
            _disposed = true;
        }
    }
}
```

## Performance Considerations

### Windows Development Optimization
- **Hot Reload**: Leverage MAUI hot reload for rapid UI iteration
- **Fast Compilation**: Windows builds are significantly faster
- **Debug Performance**: Windows debugging is more responsive

### Cross-Platform Performance
- **Lazy Loading**: Defer expensive operations until needed
- **Platform Optimization**: Use platform-specific optimizations where beneficial
- **Memory Efficiency**: Monitor memory usage across platforms

## Security Considerations

### Platform Permissions
- **Android**: Handle runtime permissions appropriately
- **iOS**: Configure Info.plist for required permissions
- **Windows**: Consider Windows security model

### Data Protection
- **Secure Storage**: Use MAUI secure storage for sensitive data
- **Network Security**: Implement certificate pinning where required
- **Local Data**: Encrypt sensitive local data

## Future Migration Guidelines

### Code Review Checklist
- [ ] TODO comments added for omitted functionality
- [ ] Platform-specific code properly conditionally compiled
- [ ] Services registered in dependency injection
- [ ] Cross-platform builds successful
- [ ] Windows development workflow maintained

### Documentation Requirements
- **API Changes**: Document any breaking changes
- **Migration Steps**: Step-by-step migration procedures
- **Testing Results**: Cross-platform testing outcomes
- **Performance Impact**: Before/after performance comparisons

## Common Pitfalls and Solutions

### Namespace Conflicts
**Problem**: Ambiguous references between platform and MAUI types
**Solution**: Use global:: prefix or explicit using aliases

### Service Registration
**Problem**: Services not available at runtime
**Solution**: Verify registration in MauiProgram.cs and conditional compilation

### Platform Differences
**Problem**: Code works on one platform but not others
**Solution**: Regular cross-platform builds and platform-specific testing

### Performance Issues
**Problem**: Slow performance on mobile platforms
**Solution**: Profile on target platforms, optimize hot paths
