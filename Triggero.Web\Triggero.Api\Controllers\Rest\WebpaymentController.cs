﻿using FirebaseAdmin.Messaging;
using MapsterMapper;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Dto;
using Triggero.Domain.Models.Enums;
using Newtonsoft.Json;
using Polly;
using Triggero.Application.Providers.ForaBank;
using Triggero.Application.Providers.ForaBank.Models;
using Triggero.Application.Services;
using Triggero.Database;
using Triggero.Models.Enums;
using Triggero.Models.General;
using Triggero.Models.General.UserData;
using Yandex.Checkout.V3;

namespace Triggero.Domain.Controllers.Rest;


/// <summary>
/// Для оплаты в веб-клиенте
/// </summary>
[ApiController]
[Route("[controller]/[action]")]
public class WebpaymentController : BaseApiController
{

    private readonly PaymentsService _payments;

    /// <summary>
    /// TODO mode to attribute etc
    /// </summary>
    /// <param name="apiKey"></param>
    /// <returns></returns>
    static bool ValidateApiKey(string apiKey)
    {
        return apiKey == Secrets.WebClientApiKey;
    }


    public WebpaymentController(
        IMapper mapper,
        PaymentsService payments,
        DatabaseContext db) : base(db, mapper)
    {
        _payments = payments;
    }


    /// <summary>
    /// Создание заказа для пользователя, для использования его данных при дальнейшей оплате через платежный виджет.
    /// </summary>
    /// <returns></returns>
    [HttpPost]
    [Consumes("application/json")]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(WebpaymentOrderDto))]
    public async Task<ActionResult> PayForSubscription([FromBody] RequestPayForSubscriptionDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }

        if (dto.Amount < 1)
        {
            return BadRequest("Check your amount");
        }

        if (!ValidateApiKey(dto.ApiKey))
        {
            return Unauthorized("Need ApiKey");
        }

        var user = DB.Users.Include(o => o.UserSubscription)
    .FirstOrDefault(o => o.GlobalId == dto.UserId && !o.IsDeleted);


        if (user == null)
        {
            return NotFound("User not found");
        }

        var result = await _payments.CreateWebPayment(user, dto);

        if (result.Succeeded)
        {
            return Ok(result.Data);
        }

        return BadRequest(result.Errors.FirstOrDefault());
    }

    /// <summary>
    /// Метод вызывается после того, как веб-клиент закончил оплату, для проведения обновления данных пользователя.
    /// </summary>
    /// <param name="dto"></param>
    /// <returns></returns>
    [HttpPost]
    [ProducesResponseType(StatusCodes.Status200OK, Type = typeof(ReportOrderPaymentStatusDto))]
    [Consumes("application/json")]
    public async Task<ActionResult> CheckPayment([FromBody] RequestCheckPaymentOrderDto dto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest(ModelState);
        }
        if (!ValidateApiKey(dto.ApiKey))
        {
            return Unauthorized("Need ApiKey");
        }

        var check = await _payments.UpdatePaymentStatusAsync(dto.OrderId);

        if (check.Succeeded)
        {
            return Ok();
        }

        return BadRequest($"{check.Errors.FirstOrDefault()}");
    }


}