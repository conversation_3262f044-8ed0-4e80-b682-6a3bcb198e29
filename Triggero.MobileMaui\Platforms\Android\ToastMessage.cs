using Android.App;
using Android.Views;
using Android.Widget;
using Triggero.Mobile.Abstractions;

namespace Triggero.MobileMaui.Platforms.Android
{
    public class ToastMessage : IToastMessage
    {
        public void ShortAlert(string message)
        {
            try
            {
                var toast = Toast.MakeText(global::Android.App.Application.Context, message, ToastLength.Short);
                toast?.SetGravity(GravityFlags.Center | GravityFlags.CenterHorizontal, 0, 0);
                toast?.Show();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ToastMessage] ShortAlert failed: {ex.Message}");
            }
        }

        public void LongAlert(string message, ToastPosition position)
        {
            try
            {
                var gravity = GravityFlags.Bottom;
                switch (position)
                {
                    case ToastPosition.Top:
                        gravity = GravityFlags.Top;
                        break;
                    case ToastPosition.Center:
                        gravity = GravityFlags.CenterVertical;
                        break;
                    case ToastPosition.Bottom:
                        gravity = GravityFlags.Bottom;
                        break;
                }

                var toast = Toast.MakeText(global::Android.App.Application.Context, message, ToastLength.Long);
                toast?.SetGravity(gravity | GravityFlags.CenterHorizontal, 0, 0);
                toast?.SetMargin(0, 0.1f);
                toast?.Show();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[ToastMessage] LongAlert failed: {ex.Message}");
            }
        }
    }
}
