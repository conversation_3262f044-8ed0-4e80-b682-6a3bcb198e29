﻿<?xml version="1.0" encoding="UTF-8" ?>
<?xaml-comp compile="true" ?>
<ResourceDictionary 
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw">

    <!--<Style TargetType="ActivityIndicator">
        <Setter Property="Color" Value="{AppThemeBinding Light={StaticResource Primary}, Dark={StaticResource White}}" />
    </Style>-->

      <Style
        ApplyToDerivedTypes="True"
        TargetType="Label">
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="TextColor" Value="{x:StaticResource ColorText}" />
    </Style>

    <Style
        x:Key="StyleBtnText"
        ApplyToDerivedTypes="True"
        TargetType="Label">
        <Setter Property="FontFamily" Value="FontTextBold" />
        <Setter Property="FontSize" Value="14" />
    </Style>

    <Style
        x:Key="StyleHeaderNavigation"
        ApplyToDerivedTypes="True"
        TargetType="Label">
        <Setter Property="FontFamily" Value="FontTextBold" />
        <Setter Property="FontSize" Value="16" />
    </Style>

    <Style
        x:Key="StyleHeaderText"
        ApplyToDerivedTypes="True"
        TargetType="Label">
        <Setter Property="FontFamily" Value="FontTextBold" />
        <Setter Property="FontSize" Value="24" />
    </Style>

    <Style
        x:Key="StyleArticleTitle"
        ApplyToDerivedTypes="True"
        TargetType="Label">
        <Setter Property="FontFamily" Value="FontTextSemiBold" />
        <Setter Property="FontSize" Value="20" />
        <Setter Property="TextColor" Value="Black" />
    </Style>

    <Style
        x:Key="StyleArticleSubTitle"
        ApplyToDerivedTypes="True"
        TargetType="Label">
        <Setter Property="FontSize" Value="12" />
        <Setter Property="TextColor" Value="#7F000000" />
    </Style>

    <Style
        x:Key="StyleArticleText"
        ApplyToDerivedTypes="True"
        TargetType="Label">
        <Setter Property="FontSize" Value="15" />
    </Style>

    <Style
        x:Key="StyleHeaderTextDrawn"
        ApplyToDerivedTypes="True"
        TargetType="draw:SkiaLabel">
        <Setter Property="ParagraphSpacing" Value="0.1" />
        <Setter Property="FontFamily" Value="FontTextBold" />
        <Setter Property="FontSize" Value="24" />
    </Style>

    <Style
        ApplyToDerivedTypes="True"
        TargetType="draw:SkiaLabel">
        <Setter Property="FontFamily" Value="FontText" />
        <Setter Property="FontSize" Value="12" />
        <Setter Property="LineSpacing" Value="1.0" />
        <Setter Property="TextColor" Value="{x:StaticResource ColorText}" />
    </Style>

    <Style
        x:Key="StyleBtnTextDrawn"
        ApplyToDerivedTypes="True"
        TargetType="draw:SkiaLabel">
        <Setter Property="FontFamily" Value="FontTextBold" />
        <Setter Property="FontSize" Value="14" />
        <Setter Property="LineSpacing" Value="0.9" />
        <Setter Property="ParagraphSpacing" Value="-0.15" />
    </Style>

    <Style
        ApplyToDerivedTypes="True"
        TargetType="ContentPage">
        <!--<Setter Property="Background" Value="White" />-->
        <Setter Property="BackgroundColor" Value="White" />
    </Style>

    <Style
        ApplyToDerivedTypes="True"
        TargetType="NavigationPage">
        <!--<Setter Property="Background" Value="White" />-->
        <Setter Property="BackgroundColor" Value="White" />
    </Style>

    <Style
        ApplyToDerivedTypes="True"
        TargetType="ActivityIndicator">
        <Setter Property="Color" Value="{x:StaticResource ColorPrimary}" />
    </Style>

</ResourceDictionary>
