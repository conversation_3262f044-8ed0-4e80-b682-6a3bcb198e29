﻿using System.ComponentModel;

namespace Triggero.Web.Enums.InterfaceTexts
{
    public enum SubscriptionInterfaceTextType
    {
        [Description("Подписки - выбор")]
        SelectSubscriptionLocalization = 1,
        [Description("Подписки - закончилась подписка")]
        SubscriptionEndedLocalization = 2,
        [Description("Подписки - основное")]
        SubscriptionMainLocalization = 3,
        [Description("Подписки - пробный период")]
        TrialPageLocalization = 4,
        [Description("Подписки - пробный период начался")]
        TrialStartedLocalization = 5
    }
}
