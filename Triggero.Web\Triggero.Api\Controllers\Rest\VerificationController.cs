﻿using Microsoft.AspNetCore.Mvc;
using Triggero.Application.Abstractions;
using Triggero.Application.Services;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Domain.Models.Dto;
using Triggero.Models.Enums;
using Triggero.Models.General;

namespace Triggero.Domain.Controllers.Rest.General
{
    [Obsolete]
    [ApiController]
    [Route("[controller]")]
    public class VerificationController : ApiController
    {
        private readonly IAccountManagerService _auth;
        private readonly IMailService _mailer;

        public VerificationController(DatabaseContext db,
            IMailService mails,
            IAccountManagerService auth) : base(db)
        {
            _auth = auth;
            _mailer = mails;
        }

        [HttpGet, Route("IsCodeRight")]
        public async Task<bool> IsCodeRight(string address, string code)
        {
            var login = await _auth.LoginAsync(new LoginWithPhoneNumberDto()
            {
                Code = code,
                PhoneNumber = address
            });

            return login.Succeeded;
        }

        [HttpPut, Route("SendSMS")]
        public async Task SendSMS(string phone)
        {
            await _auth.RequestSmsCode(phone);
        }

        [HttpPut, Route("SendEmailCode")]
        public async Task SendEmailCode(string email)
        {
            var oldCode = DB.Codes.FirstOrDefault(o => o.Address == email);
            if (oldCode != null)
            {
                DB.Codes.Remove(oldCode);
            }

            var codeModel = new CodeModel
            {
                Address = email,
                Code = Random.Shared.Next(100000, 999999).ToString(),
                Type = VerificationType.Email
            };

            var subject = "Восстановление доступа к аккаунту";
            var body = "<h2>Ваш код для восстановления доступа</h2>" +
                    $"<h3>{codeModel.Code}</h3>";

            await _mailer.SendEmailAsync(email, subject, body);

            DB.Codes.Add(codeModel);
            await DB.SaveChangesAsync();
        }
    }
}
