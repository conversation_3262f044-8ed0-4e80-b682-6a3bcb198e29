﻿using AppoMobi.Specials;

namespace Triggero.MauiClient.Views
{
    public partial class DrawnListCategories
    {
        private readonly BaseCategoriesViewModel _vm;

        public DrawnListCategories(BaseCategoriesViewModel vm, int msDelayInitialize = 1)
        {
            HorizontalOptions = LayoutOptions.Fill;
            VerticalOptions = LayoutOptions.Fill;
            BackgroundColor = Colors.White;

            _vm = vm;

            InitializeComponent();

            BindingContext = _vm;

            Tasks.StartDelayed(TimeSpan.FromMilliseconds(50), async () =>
            {
                await _vm.InitializeAsyc();
            });
        }
    }
}