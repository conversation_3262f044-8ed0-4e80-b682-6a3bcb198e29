/* ===========================================================
 * trumbowyg.mathMl.js v1.0
 * MathML plugin for Trumbowyg
 * http://alex-d.github.com/Trumbowyg
 * ===========================================================
 * Author : loclamor
 */
!function(e){"use strict";e.extend(!0,e.trumbowyg,{langs:{en:{mathml:"Insert Formulas",formulas:"Formulas",inline:"Inline"},sl:{mathml:"Vstavi matematični izraz",formulas:"Formula",inline:"V vrstici"},by:{mathml:"Уставіць формулу",formulas:"Формула",inline:"Inline-элемент"},da:{mathml:"Indsæt formler",formulas:"Formler",inline:"Inline"},et:{mathml:"Sisesta valem",formulas:"Valemid",inline:"Teksti sees"},fr:{mathml:"Inserer une formule",formulas:"Formule",inline:"En ligne"},hu:{mathml:"Formulák beszúrás",formulas:"Formulák",inline:"Inline"},ko:{mathml:"수식 넣기",formulas:"수식",inline:"글 안에 넣기"},pt_br:{mathml:"Inserir fórmulas",formulas:"Fórmulas",inline:"Em linha"},ru:{mathml:"Вставить формулу",formulas:"Формула",inline:"Строчный элемент"},tr:{mathml:"Formül Ekle",formulas:"Formüller",inline:"Satır içi"},zh_tw:{mathml:"插入方程式",formulas:"方程式",inline:"內嵌"}},plugins:{mathml:{init:function(n){var l={formulas:{label:n.lang.formulas,required:!0,value:""},inline:{label:n.lang.inline,attributes:{checked:!0},type:"checkbox",required:!1}},a=function(l){var a=l.inline?"$":"$$";if(n.currentMathNode)e(n.currentMathNode).html(a+" "+l.formulas+" "+a).attr("formulas",l.formulas).attr("inline",l.inline?"true":"false");else{var i='<span contenteditable="false" formulas="'+l.formulas+'" inline="'+(l.inline?"true":"false")+'" >'+a+" "+l.formulas+" "+a+"</span>",r=e(i)[0];r.onclick=t,n.range.deleteContents(),n.range.insertNode(r)}return n.currentMathNode=!1,MathJax.Hub.Queue(["Typeset",MathJax.Hub]),!0},t=function(){n.currentMathNode=this,l.formulas.value=e(this).attr("formulas"),"true"===e(this).attr("inline")?l.inline.attributes.checked=!0:delete l.inline.attributes.checked,n.openModalInsert(n.lang.mathml,l,a)},i={fn:function(){n.saveRange(),l.formulas.value=n.getRangeText(),l.inline.attributes.checked=!0,n.openModalInsert(n.lang.mathml,l,a)}};n.$ta.on("tbwinit",(function(){n.$ed.find("[formulas]").each((function(e,n){n.onclick=t}))})),n.addBtnDef("mathml",i)}}}})}(jQuery);