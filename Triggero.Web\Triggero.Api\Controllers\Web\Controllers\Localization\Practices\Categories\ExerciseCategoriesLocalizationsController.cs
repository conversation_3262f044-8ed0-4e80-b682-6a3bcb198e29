﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Localization.Practices.Categories;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class ExerciseCategoriesLocalizationsController : AbsController
    {
        public ExerciseCategoriesLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/ExercisesCategories")]
        public IActionResult ExercisesCategories()
        {
            var cats = DB.ExerciseCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            cats.Reverse();

            var vm = new LocalizationListVM<ExerciseCategory>
            {
                Items = cats,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Practices\Categories\ExerciseCategories\ExerciseCategories.cshtml", vm);
        }







        [Route("Localizations/CreateExerciseCategoryLocalization")]
        public IActionResult CreateExerciseCategoryLocalization(int id)
        {
            var vm = new LocalizationVM<ExerciseCategoryLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Practices\Categories\ExerciseCategories\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreateExerciseCategoryLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateExerciseCategoryLocalizationPOST([FromForm] LocalizationVM<ExerciseCategoryLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();

            var cat = DB.ExerciseCategories.FirstOrDefault(o => o.Id == vm.Id);
            cat.Localizations.Add(vm.Localization);

            DB.ExerciseCategories.Update(cat);
            await DB.SaveChangesAsync();
            return RedirectToAction("ExercisesCategories", "ExerciseCategoriesLocalizations");
        }



        [Route("Localizations/UpdateExerciseCategoryLocalization")]
        public IActionResult UpdateExerciseCategoryLocalization(int id)
        {
            var cat = DB.ExerciseCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<ExerciseCategoryLocalization>
            {
                Id = id,
                Localization = cat.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id) as ExerciseCategoryLocalization
            };
            return View(@"Views\Localization\Practices\Categories\ExerciseCategories\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdateExerciseCategoryLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateExerciseCategoryLocalizationPOST([FromForm] LocalizationVM<ExerciseCategoryLocalization> vm)
        {
            DB.ExerciseCategoryLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("ExercisesCategories", "ExerciseCategoriesLocalizations");
        }

    }
}
