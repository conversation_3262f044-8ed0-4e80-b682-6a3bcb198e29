/* ===========================================================
 * zh_cn.js
 * Simplified Chinese translation for Trumbowyg
 * http://alex-d.github.com/Trumbowyg
 * ===========================================================
 * Author : Liu <PERSON> (akai)
 *          Twitter : @akai404
 *          Github : https://github.com/akai
 */

// jshint camelcase:false
jQuery.trumbowyg.langs.zh_cn = {
    viewHTML: '源代码',

    formatting: '格式',
    p: '段落',
    blockquote: '引用',
    code: '代码',
    header: '标题',

    bold: '加粗',
    italic: '斜体',
    strikethrough: '删除线',
    underline: '下划线',

    strong: '加粗',
    em: '斜体',
    del: '删除线',

    unorderedList: '无序列表',
    orderedList: '有序列表',

    insertImage: '插入图片',
    insertVideo: '插入视频',
    link: '超链接',
    createLink: '插入链接',
    unlink: '取消链接',

    justifyLeft: '居左对齐',
    justifyCenter: '居中对齐',
    justifyRight: '居右对齐',
    justifyFull: '两端对齐',

    horizontalRule: '插入分隔线',

    fullscreen: '全屏',

    close: '关闭',

    submit: '确定',
    reset: '取消',

    required: '必需的',
    description: '描述',
    title: '标题',
    text: '文字'
};