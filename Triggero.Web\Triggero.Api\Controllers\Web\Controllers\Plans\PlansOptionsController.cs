﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.General;
using Triggero.Models.Plans;
using Triggero.Web.Abstractions;

namespace Triggero.Web.Controllers
{
    [Authorize]
    public class PlansOptionsController : AbsController
    {
        public PlansOptionsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("PlansOptions")]
        public IActionResult PlansOptions()
        {
            var posts = DB.PlanOptions.ToList();
            posts.Reverse();
            return View(@"Views\Plans\PlanOptions\PlanOptions.cshtml", posts);
        }


        //[Route("UpdatePlanOption")]
        public IActionResult UpdatePlanOption(int id)
        {
            var planOption = DB.PlanOptions.FirstOrDefault(o => o.Id == id);
            return View(@"Views\Plans\PlanOptions\UpdatePlanOption.cshtml", planOption);
        }

        [HttpPost]
        //[Route("UpdatePlanOptionPOST"), HttpPost]
        public async Task<IActionResult> UpdatePlanOptionPOST([FromForm] PlanOption planOption)
        {
            DB.PlanOptions.Update(planOption);
            await DB.SaveChangesAsync();
            return RedirectToAction("PlansOptions", "PlansOptions");
        }


    }
}
