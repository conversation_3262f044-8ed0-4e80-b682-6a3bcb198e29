﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Practices;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Practices;

namespace Triggero.Web.Controllers.Practices
{
    [Authorize]
    public class BreathPracticesController : AbsController
    {
        public BreathPracticesController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("BreathPractices")]
        public IActionResult BreathPractices()
        {
            var practices = DB.BreathPractices.Where(o => !o.IsDeleted).ToList();
            practices.Reverse();
            return View(@"Views\Practices\BreathPractices\BreathPractices.cshtml", practices);
        }


        //[Route("CreateBreathPractice")]
        public IActionResult CreateBreathPractice()
        {
            return View(@"Views\Practices\BreathPractices\CreateBreathPractice.cshtml");
        }

        [HttpPost]
        //[Route("CreateBreathPracticePOST"), HttpPost]
        public async Task<IActionResult> CreateBreathPracticePOST([FromForm] BreathPractice practice)
        {
            practice.ImgPath = await SetAttachmentIfHas(practice.ImgPath, 0);
            practice.AudioPath = await SetAttachmentIfHas(practice.AudioPath, 1);
            DB.BreathPractices.Add(practice);
            await DB.SaveChangesAsync();
            return RedirectToAction("BreathPractices", "BreathPractices");
        }




        //[Route("UpdateBreathPractice")]
        public IActionResult UpdateBreathPractice(int id)
        {
            var practice = DB.BreathPractices.FirstOrDefault(o => o.Id == id);
            return View(@"Views\Practices\BreathPractices\UpdateBreathPractice.cshtml", practice);
        }

        [HttpPost]
        //[Route("UpdateBreathPracticePOST"), HttpPost]
        public async Task<IActionResult> UpdateBreathPracticePOST([FromForm] BreathPractice practice)
        {
            practice.ImgPath = await SetAttachmentIfHas(practice.ImgPath, 0);
            practice.AudioPath = await SetAttachmentIfHas(practice.AudioPath, 1);
            DB.BreathPractices.Update(practice);
            await DB.SaveChangesAsync();
            return RedirectToAction("BreathPractices", "BreathPractices");
        }

        [HttpDelete]
        //[Route("DeleteBreathPractice"), HttpDelete]
        public async Task<IActionResult> DeleteBreathPractice(int id)
        {
            var practice = DB.BreathPractices.FirstOrDefault(o => o.Id == id);
            practice.IsDeleted = true;
            DB.BreathPractices.Update(practice);
            await DB.SaveChangesAsync();
            return RedirectToAction("BreathPractices", "BreathPractices");
        }
    }
}
