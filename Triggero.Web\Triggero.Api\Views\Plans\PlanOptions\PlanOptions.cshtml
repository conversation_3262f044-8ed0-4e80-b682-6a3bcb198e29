﻿@using Triggero.Models
@using Triggero.Models.Plans
@using Triggero.Models.Practices
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "ТАРИФНЫЕ ОПЦИИ";
}
@model List<PlanOption>
<!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                        <th scope="col">ID</th>
                        <th scope="col">Название</th>
                        <th scope="col">Описание</th>
                        <th scope="col">Цена</th>
                        <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model)
                    {
                         <tr>
                            <th scope="row">#@item.Id</th>
                            <td>@item.Title</td>
                            <td>@item.Description</td>
                            <td>@item.Price</td>
                            <td>
                                <div class="interect">
                                    <a asp-action="UpdatePlanOption" asp-controller="PlansOptions" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>                
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

         @*   <div class="mb-3 position-right">
                <a asp-action="CreatePromocode" asp-controller="Promocodes" class="button-classic">Добавить</a>
            </div>*@

            <script>
                $(document).ready( function () {
                    $('#tablemanager').DataTable();
                } );
            </script>
        </div>
    </div>
</div>