﻿using System.ComponentModel;

namespace Triggero.Web.Enums.InterfaceTexts
{
    public enum AuthInterfaceTextType
    {
        [Description("Аккаунт зарегистрирован")]
        AccountRegisteredLocalization = 1,
        [Description("Создание аккаунта")]
        CreateAccountLocalization = 2,
        [Description("Восстановление пароля - код")]
        EmailForgotPasswordCodePageLocalization = 3,
        [Description("Забыл пароль")]
        EmailForgotPasswordLocalization = 4,
        [Description("Код из смс")]
        EnterSMSCodeLocalization = 5,
        [Description("Вход по email")]
        LoginByEmailLocalization = 6,
        [Description("Вход по телефону")]
        LoginByPhoneLocalization = 7,
        [Description("Вход - главная страница")]
        LoginMainPageLocalization = 8
    }
}
