﻿using MobileAPIWrapper;
using System.Collections.Generic;
using System.Linq;
using System.Threading.Tasks;
using Triggero.MauiClient.Helpers;
using Triggero.Mobile.Enums;
using Triggero.Models;

namespace Triggero.MauiClient.Helpers.Modules
{
    public class TodayData
    {
        public static string FilePath = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + @"/todayData.json";

        public DateTime LastUpdateTime { get; set; }


        public List<RecommendationModel> Recommendations { get; set; } = new List<RecommendationModel>();
        public List<TaskForToday> TasksForToday { get; set; } = new List<TaskForToday>();



        private int _counter = 0;

        public async Task StartJob()
        {
            while (true)
            {
                if (LastUpdateTime != DateTime.Now.Date)
                {
                    LastUpdateTime = DateTime.Now.Date;
                    await GenerateRecomendationsForToday();
                    await GenerateTasksForToday();

                    GlobalEvents.OnTodayDataChanged();
                    ApplicationState.SaveToFile(this, FilePath);
                }

                await Task.Delay(TimeSpan.FromMinutes(10));
            }
        }

        #region Рекомендации
        public async Task GenerateRecomendationsForToday()
        {
            if (ConnectionHelper.HasInternet())
            {
                await GenerateRecomendationsListWithConnection();
            }
            else
            {
                await GenerateRecomendationsListWithNoConnection();
            }
        }
        public async Task GenerateRecomendationsListWithNoConnection()
        {
            var tests = ApplicationState.Data.Tests.OrderByDescending(o => o.CreatedAt).Take(3).ToList();
            var exercises = ApplicationState.Data.Exercises.OrderByDescending(o => o.CreatedAt).Take(3).ToList();
            var practices = ApplicationState.Data.Practices.OrderByDescending(o => o.CreatedAt).Take(3).ToList();
            var topics = ApplicationState.Data.Topics.OrderByDescending(o => o.CreatedAt).Take(3).ToList();


            _counter = 0;
            Recommendations.Clear();

            var recommendations = new List<RecommendationModel>();

            var rnd = new Random();

            while (Recommendations.Count < 4)
            {
                if (_counter++ > 30) return;

                var type = (RecomendationType)rnd.Next(1, 4);
                switch (type)
                {
                    case RecomendationType.Test:
                    if (tests.Any())
                    {
                        var item = tests[rnd.Next(0, tests.Count)];

                        if (recommendations.Any(o => o.Test?.Id == item.Id)) continue;
                        recommendations.Add(new RecommendationModel(item));
                    }
                    break;
                    case RecomendationType.Practice:
                    if (practices.Any())
                    {
                        var item = practices[rnd.Next(0, practices.Count)];

                        if (recommendations.Any(o => o.Practice?.Id == item.Id)) continue;
                        recommendations.Add(new RecommendationModel(item));
                    }
                    break;
                    case RecomendationType.Topic:
                    if (topics.Any())
                    {
                        var item = topics[rnd.Next(0, topics.Count)];

                        if (recommendations.Any(o => o.Topic?.Id == item.Id)) continue;
                        recommendations.Add(new RecommendationModel(item));
                    }
                    break;
                    case RecomendationType.Exercise:
                    if (exercises.Any())
                    {
                        var item = exercises[rnd.Next(0, exercises.Count)];

                        if (recommendations.Any(o => o.Exercise?.Id == item.Id)) continue;
                        recommendations.Add(new RecommendationModel(item));
                    }
                    break;
                }


            }

            foreach (var recommendation in recommendations)
            {
                var model = recommendation.GetModel();

            }

            Recommendations.AddRange(recommendations);

        }
        public async Task GenerateRecomendationsListWithConnection()
        {

            var items = await TriggeroMobileAPI.Common.GetRandomRecomendations();

            _counter = 0;
            Recommendations.Clear();


            foreach (var item in items)
            {
                Recommendations.Add(new RecommendationModel(item.GetItem()));
            }

        }
        #endregion

        #region Задачи на сегодня
        public async Task GenerateTasksForToday()
        {
            if (ConnectionHelper.HasInternet())
            {
                await GenerateTasksForTodayWithConnection();
            }
            else
            {
                await GenerateTasksForTodayWithNoConnection();
            }
        }
        private async Task GenerateTasksForTodayWithConnection()
        {
            TasksForToday.Clear();

            var randomToDoList = await TriggeroMobileAPI.Common.GetToDoListRandom();
            foreach (var item in randomToDoList)
            {
                TasksForToday.Add(new TaskForToday(item));

                //TasksForToday.LastOrDefault().Title = item.GetLocalizedTitle(LanguageHelper.LangCode);
                //TasksForToday.LastOrDefault().ImgPath = item.ImgPath;
                //TasksForToday.LastOrDefault().PassingTimeInMinutes = item.MinutesCount;
            }
        }
        private async Task GenerateTasksForTodayWithNoConnection()
        {
            _counter = 0;
            TasksForToday.Clear();

            var tests = ApplicationState.Data.Tests;
            var exercises = ApplicationState.Data.Exercises;
            var practices = ApplicationState.Data.Practices;
            var topics = ApplicationState.Data.Topics;

            var rnd = new Random();

            while (TasksForToday.Count < 3)
            {
                if (_counter++ > 30) return;

                var type = (RecomendationType)rnd.Next(1, 4);
                switch (type)
                {
                    case RecomendationType.Test:
                    if (tests.Any())
                    {
                        var item = tests[rnd.Next(0, tests.Count)];

                        if (Recommendations.Any(o => o.Test?.Id == item.Id)) continue;
                        TasksForToday.Add(new TaskForToday(item));
                    }
                    break;
                    case RecomendationType.Practice:
                    if (practices.Any())
                    {
                        var item = practices[rnd.Next(0, practices.Count)];

                        if (Recommendations.Any(o => o.Practice?.Id == item.Id)) continue;
                        TasksForToday.Add(new TaskForToday(item));
                    }
                    break;
                    case RecomendationType.Topic:
                    if (topics.Any())
                    {
                        var item = topics[rnd.Next(0, topics.Count)];

                        if (Recommendations.Any(o => o.Topic?.Id == item.Id)) continue;
                        TasksForToday.Add(new TaskForToday(item));
                    }
                    break;
                    case RecomendationType.Exercise:
                    if (exercises.Any())
                    {
                        var item = exercises[rnd.Next(0, exercises.Count)];

                        if (Recommendations.Any(o => o.Exercise?.Id == item.Id)) continue;
                        TasksForToday.Add(new TaskForToday(item));
                    }
                    break;
                }
            }
        }

        #endregion

    }
}
