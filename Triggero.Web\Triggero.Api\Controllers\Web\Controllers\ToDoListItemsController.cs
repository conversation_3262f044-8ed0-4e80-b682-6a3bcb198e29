﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Swashbuckle.AspNetCore.Annotations;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.General;
using Triggero.Models.General.Influence;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels;

namespace Triggero.Web.Controllers
{

    [Authorize]
    public class ToDoListItemsController : AbsController
    {
        public ToDoListItemsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("ToDoListItems")]
        public IActionResult ToDoListItems()
        {
            var posts = DB.ToDoListItems.Where(o => !o.IsDeleted).ToList();
            posts.Reverse();
            return View(@"Views\ToDoList\ToDoList.cshtml", posts);
        }


        //[Route("CreateToDoListItem")]
        public IActionResult CreateToDoListItem()
        {
            var vm = new CreateToDoListItemVM
            {
                Exercises = DB.Exercises.ToList(),
                Practices = DB.Practices.ToList(),
                Tests = DB.Tests.ToList(),
                Topics = DB.Topics.ToList(),
            };
            return View(@"Views\ToDoList\CreateToDoList.cshtml", vm);
        }

        //[Route("CreateToDoListItemPOST"), HttpPost]
        public async Task<IActionResult> CreateToDoListItemPOST([FromForm] CreateToDoListItemVM item)
        {
            item.ToDoListItem.ImgPath = await SetAttachmentIfHas(item.ToDoListItem.ImgPath);

            DB.ToDoListItems.Add(item.ToDoListItem);
            await DB.SaveChangesAsync();
            return RedirectToAction("ToDoListItems", "ToDoListItems");
        }



        //[Route("UpdateToDoListItem")]
        public IActionResult UpdateToDoListItem(int id)
        {
            var item = DB.ToDoListItems
                .Include(o => o.Topic)
                .Include(o => o.Test)
                .Include(o => o.Practice)
                .Include(o => o.Exercise)
                .FirstOrDefault(o => o.Id == id);

            var vm = new CreateToDoListItemVM
            {
                ToDoListItem = item,
                Exercises = DB.Exercises.ToList(),
                Practices = DB.Practices.ToList(),
                Tests = DB.Tests.ToList(),
                Topics = DB.Topics.ToList(),
            };
            return View(@"Views\ToDoList\UpdateToDoList.cshtml", vm);
        }

        [HttpPost]
        //[Route("UpdateToDoListItemPOST"), HttpPost]
        public async Task<IActionResult> UpdateToDoListItemPOST([FromForm] CreateToDoListItemVM item)
        {
            item.ToDoListItem.ImgPath = await SetAttachmentIfHas(item.ToDoListItem.ImgPath);

            DB.ToDoListItems.Update(item.ToDoListItem);
            await DB.SaveChangesAsync();
            return RedirectToAction("ToDoListItems", "ToDoListItems");
        }

        [HttpDelete]
        //[Route("DeleteToDoListItem"), HttpDelete]
        public async Task<IActionResult> DeleteToDoListItem(int id)
        {
            var item = DB.ToDoListItems.FirstOrDefault(o => o.Id == id);
            item.IsDeleted = true;
            DB.ToDoListItems.Update(item);
            await DB.SaveChangesAsync();
            return RedirectToAction("ToDoListItems", "ToDoListItems");
        }
    }
}
