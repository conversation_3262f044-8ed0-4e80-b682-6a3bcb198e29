﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Plans
@{
    ViewData["Title"] = "РЕДАКТИРОВАНИЕ ТАРИФНОЙ ОПЦИИ";
}
@model PlanOption

 <!-- CONTENT -->
<form asp-action="UpdatePlanOptionPOST" asp-controller="PlansOptions" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">

            <input type="hidden" asp-for="Id" value="@Model.Id" />

           <div class="mb-3">
               <label for="" class="formlabel">Заголовок</label>
               <input type="text" asp-for="Title" value="@Model.Title"  required class="form-control" placeholder="">
           </div>

            <div class="mb-3">
               <label for="" class="formlabel">Описание</label>
               <input type="text" asp-for="Description" value="@Model.Description"  required class="form-control" placeholder="">
           </div>

           <div class="mb-3">
                <label for="" class="formlabel">Цена</label>
                <input type="number" asp-for="Price" value="@Model.Price" required class="form-control" placeholder="">
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>