﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>
	<!-- To customize the asp.net core module uncomment and edit the following section. 
  For more info see https://go.microsoft.com/fwlink/?linkid=838655 -->
	<system.webServer>
		<handlers>
			<remove name="aspNetCore" />
			<remove name="WebDAV" />
			<add name="aspNetCore" path="*" verb="*" modules="AspNetCoreModuleV2" resourceType="Unspecified" />
		</handlers>
		<modules runAllManagedModulesForAllRequests="true">
			<remove name="WebDAVModule" />
			<!-- add this -->
		</modules>
		<aspNetCore processPath="%LAUNCHER_PATH%" arguments="%LAUNCHER_ARGS%" stdoutLogEnabled="false" stdoutLogFile=".\logs\stdout" hostingModel="InProcess" />
	</system.webServer>
</configuration>