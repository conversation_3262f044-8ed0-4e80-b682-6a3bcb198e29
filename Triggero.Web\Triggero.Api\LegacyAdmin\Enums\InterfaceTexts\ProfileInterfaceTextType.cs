﻿using System.ComponentModel;

namespace Triggero.Web.Enums.InterfaceTexts
{
    public enum ProfileInterfaceTextType
    {
        [Description("Профиль - скачать данные")]
        ProfileDownloadDataLocalization = 1,
        [Description("Профиль - введите email")]
        ProfileEnterEmailLocalization = 2,
        [Description("Профиль - главная страница")]
        ProfileMainLocalization = 3,
        [Description("Профиль - уведомления")]
        ProfileNotificationsLocalization = 4,
        [Description("Профиль - выбор периода")]
        ProfileSelectPeriodLocalization = 5,
        [Description("Профиль - выбор времени")]
        ProfileSelectTimeLocalization = 6
    }
}
