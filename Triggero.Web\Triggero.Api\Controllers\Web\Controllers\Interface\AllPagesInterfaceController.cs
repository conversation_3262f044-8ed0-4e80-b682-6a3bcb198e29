﻿using GamblingFactory.Admin.ViewModels.Localization;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models.Localization.Interface;
using Triggero.Models.Localization.Interface.Auth;
using Triggero.Models.Localization.Interface.Library;
using Triggero.Models.Localization.Interface.MoodTracker;
using Triggero.Models.Localization.Interface.Profile;
using Triggero.Models.Localization.Interface.Start;
using Triggero.Models.Localization.Interface.Subscriptions;
using Triggero.Models.Localization.Interface.Tutorial;
using Triggero.Models.Localization.Interface.TutorialNew;
using Triggero.Web.Abstractions;
using Triggero.Web.Enums.InterfaceTexts;
using Triggero.Web.Models.Localization;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class AllPagesInterfaceController : AbsController
    {
        public AllPagesInterfaceController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Interface/AllPages")]
        public IActionResult AllPages()
        {
            var vm = new LocalizationInterfaceAllPagesVM();
            FillLocalizationInterfaceAllPagesVM(vm);

            return View(@"Views\Interface\MainPage.cshtml", vm);
        }

        [Route("Interface/AllPagesByLang")]
        public IActionResult AllPagesByLang()
        {
            var vm = new LocalizationInterfaceAllPagesVM();

            vm.LanguageId = GetCurrentLanguage()?.Id;
            FillLocalizationInterfaceAllPagesVM(vm);

            return View(@"Views\Interface\MainPage.cshtml", vm);
        }


        [Route("Interface/AuthInterfacePage")]
        public IActionResult AuthInterfacePage(AuthInterfaceTextType type, int? langId = null)
        {
            object model = null;

            switch (type)
            {
            case AuthInterfaceTextType.AccountRegisteredLocalization:
            model = DB.AccountRegisteredLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new AccountRegisteredLocalization() { LanguageId = langId };
            break;
            case AuthInterfaceTextType.CreateAccountLocalization:
            model = DB.CreateAccountLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new CreateAccountLocalization() { LanguageId = langId };
            break;
            case AuthInterfaceTextType.EmailForgotPasswordCodePageLocalization:
            model = DB.EmailForgotPasswordCodePageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new EmailForgotPasswordCodePageLocalization() { LanguageId = langId };
            break;
            case AuthInterfaceTextType.EmailForgotPasswordLocalization:
            model = DB.EmailForgotPasswordLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new EmailForgotPasswordLocalization() { LanguageId = langId };
            break;
            case AuthInterfaceTextType.EnterSMSCodeLocalization:
            model = DB.EnterSMSCodeLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new EnterSMSCodeLocalization() { LanguageId = langId };
            break;
            case AuthInterfaceTextType.LoginByEmailLocalization:
            model = DB.LoginByEmailLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new LoginByEmailLocalization() { LanguageId = langId };
            break;
            case AuthInterfaceTextType.LoginByPhoneLocalization:
            model = DB.LoginByPhoneLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new LoginByPhoneLocalization() { LanguageId = langId };
            break;
            case AuthInterfaceTextType.LoginMainPageLocalization:
            model = DB.LoginMainPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new LoginMainPageLocalization() { LanguageId = langId };
            break;
            }

            var vm = new LocalizationPageTranslationVM
            {
                EnumInterfaceTextType = type,
                Model = model,
                AspSaveAction = "AuthPOST",
                LanguageId = langId
            };
            return View(@"Views\Interface\GeneratedPage.cshtml", vm);
        }


        [Route("Interface/LibraryInterfacePage")]
        public IActionResult LibraryInterfacePage(LibraryInterfaceTextType type, int? langId = null)
        {
            object model = null;

            switch (type)
            {
            case LibraryInterfaceTextType.BreathPracticeInterfaceLocalization:
            model = DB.BreathPracticeInterfaceLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new BreathPracticeInterfaceLocalization() { LanguageId = langId };
            break;
            case LibraryInterfaceTextType.EmotionsLocalization:
            model = DB.EmotionsLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new EmotionsLocalization() { LanguageId = langId };
            break;
            case LibraryInterfaceTextType.LibraryLocalization:
            model = DB.LibraryLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new LibraryLocalization() { LanguageId = langId };
            break;
            }

            var vm = new LocalizationPageTranslationVM
            {
                EnumInterfaceTextType = type,
                Model = model,
                AspSaveAction = "LibraryPOST",
                LanguageId = langId
            };
            return View(@"Views\Interface\GeneratedPage.cshtml", vm);
        }



        [Route("Interface/MoodTrackerInterfacePage")]
        public IActionResult MoodTrackerInterfacePage(MoodTrackerInterfaceTextType type, int? langId = null)
        {
            object model = null;

            switch (type)
            {
            case MoodTrackerInterfaceTextType.TrackerFactorsDetailsPageLocalization:
            model = DB.TrackerFactorsDetailsPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TrackerFactorsDetailsPageLocalization() { LanguageId = langId };
            break;
            case MoodTrackerInterfaceTextType.TrackerFactorsPageLocalization:
            model = DB.TrackerFactorsPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TrackerFactorsPageLocalization() { LanguageId = langId };
            break;
            case MoodTrackerInterfaceTextType.TrackerFinalPageLocalization:
            model = DB.TrackerFinalPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TrackerFinalPageLocalization() { LanguageId = langId };
            break;
            case MoodTrackerInterfaceTextType.TrackerGeneralLocalization:
            model = DB.TrackerGeneralLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TrackerGeneralLocalization() { LanguageId = langId };
            break;
            case MoodTrackerInterfaceTextType.TrackerHowAreYouLocalization:
            model = DB.TrackerHowAreYouLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TrackerHowAreYouLocalization() { LanguageId = langId };
            break;
            case MoodTrackerInterfaceTextType.TrackerMainPageLocalization:
            model = DB.TrackerMainPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TrackerMainPageLocalization() { LanguageId = langId };
            break;
            case MoodTrackerInterfaceTextType.TrackerStartLocalization:
            model = DB.TrackerStartLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TrackerStartLocalization() { LanguageId = langId };
            break;
            }

            var vm = new LocalizationPageTranslationVM
            {
                EnumInterfaceTextType = type,
                Model = model,
                AspSaveAction = "MoodtrackerPOST",
                LanguageId = langId
            };
            return View(@"Views\Interface\GeneratedPage.cshtml", vm);
        }


        [Route("Interface/ProfileInterfacePage")]
        public IActionResult ProfileInterfacePage(ProfileInterfaceTextType type, int? langId = null)
        {
            object model = null;

            switch (type)
            {
            case ProfileInterfaceTextType.ProfileDownloadDataLocalization:
            model = DB.ProfileDownloadDataLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new ProfileDownloadDataLocalization() { LanguageId = langId };
            break;
            case ProfileInterfaceTextType.ProfileEnterEmailLocalization:
            model = DB.ProfileEnterEmailLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new ProfileEnterEmailLocalization() { LanguageId = langId };
            break;
            case ProfileInterfaceTextType.ProfileMainLocalization:
            model = DB.ProfileMainLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new ProfileMainLocalization() { LanguageId = langId };
            break;
            case ProfileInterfaceTextType.ProfileNotificationsLocalization:
            model = DB.ProfileNotificationsLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new ProfileNotificationsLocalization() { LanguageId = langId };
            break;
            case ProfileInterfaceTextType.ProfileSelectPeriodLocalization:
            model = DB.ProfileSelectPeriodLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new ProfileSelectPeriodLocalization() { LanguageId = langId };
            break;
            case ProfileInterfaceTextType.ProfileSelectTimeLocalization:
            model = DB.ProfileSelectTimeLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new ProfileSelectTimeLocalization() { LanguageId = langId };
            break;
            }

            var vm = new LocalizationPageTranslationVM
            {
                EnumInterfaceTextType = type,
                Model = model,
                AspSaveAction = "ProfilePOST",
                LanguageId = langId
            };
            return View(@"Views\Interface\GeneratedPage.cshtml", vm);
        }


        [Route("Interface/StartInterfacePage")]
        public IActionResult StartInterfacePage(StartInterfaceTextType type, int? langId = null)
        {
            object model = null;

            switch (type)
            {
            case StartInterfaceTextType.HelloPageLocalization:
            model = DB.HelloPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new HelloPageLocalization() { LanguageId = langId };
            break;
            case StartInterfaceTextType.HelloSliderPageLocalization:
            model = DB.HelloSliderPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new HelloSliderPageLocalization() { LanguageId = langId };
            break;
            case StartInterfaceTextType.LetsStartPageLocalization:
            model = DB.LetsStartPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new LetsStartPageLocalization() { LanguageId = langId };
            break;
            }

            var vm = new LocalizationPageTranslationVM
            {
                EnumInterfaceTextType = type,
                Model = model,
                AspSaveAction = "StartPOST",
                LanguageId = langId
            };
            return View(@"Views\Interface\GeneratedPage.cshtml", vm);
        }


        [Route("Interface/SubscriptionInterfacePage")]
        public IActionResult SubscriptionInterfacePage(SubscriptionInterfaceTextType type, int? langId = null)
        {
            object model = null;

            switch (type)
            {
            case SubscriptionInterfaceTextType.SelectSubscriptionLocalization:
            model = DB.SelectSubscriptionLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new SelectSubscriptionLocalization() { LanguageId = langId };
            break;
            case SubscriptionInterfaceTextType.SubscriptionEndedLocalization:
            model = DB.SubscriptionEndedLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new SubscriptionEndedLocalization() { LanguageId = langId };
            break;
            case SubscriptionInterfaceTextType.SubscriptionMainLocalization:
            model = DB.SubscriptionMainLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new SubscriptionMainLocalization() { LanguageId = langId };
            break;
            case SubscriptionInterfaceTextType.TrialPageLocalization:
            model = DB.TrialPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TrialPageLocalization() { LanguageId = langId };
            break;
            case SubscriptionInterfaceTextType.TrialStartedLocalization:
            model = DB.TrialStartedLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TrialStartedLocalization() { LanguageId = langId };
            break;
            }

            var vm = new LocalizationPageTranslationVM
            {
                EnumInterfaceTextType = type,
                Model = model,
                AspSaveAction = "SubscriptionPOST",
                LanguageId = langId
            };
            return View(@"Views\Interface\GeneratedPage.cshtml", vm);
        }


        [Route("Interface/TutorialInterfacePage")]
        public IActionResult TutorialInterfacePage(TutorialInterfaceTextType type, int? langId = null)
        {
            object model = null;

            switch (type)
            {
            case TutorialInterfaceTextType.StartTutorialPageLocalization:
            model = DB.StartTutorialPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new StartTutorialPageLocalization() { LanguageId = langId };
            break;
            case TutorialInterfaceTextType.TutorialCompletedPageLocalization:
            model = DB.TutorialCompletedPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialCompletedPageLocalization() { LanguageId = langId };
            break;
            }

            var vm = new LocalizationPageTranslationVM
            {
                EnumInterfaceTextType = type,
                Model = model,
                AspSaveAction = "TutorialPOST",
                LanguageId = langId
            };
            return View(@"Views\Interface\GeneratedPage.cshtml", vm);
        }


        [Route("Interface/TutorialNewInterfacePage")]
        public IActionResult TutorialNewInterfacePage(TutorialNewInterfaceTextType type, int? langId = null)
        {
            object model = null;

            switch (type)
            {
            case TutorialNewInterfaceTextType.TutorialNewPage1Localization:
            model = DB.TutorialNewPage1Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage1Localization() { LanguageId = langId };
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage2Localization:
            model = DB.TutorialNewPage2Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage2Localization() { LanguageId = langId };
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage3Localization:
            model = DB.TutorialNewPage3Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage3Localization() { LanguageId = langId };
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage4Localization:
            model = DB.TutorialNewPage4Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage4Localization() { LanguageId = langId };
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage5Localization:
            model = DB.TutorialNewPage5Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage5Localization() { LanguageId = langId };
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage6Localization:
            model = DB.TutorialNewPage6Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage6Localization() { LanguageId = langId };
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage7Localization:
            model = DB.TutorialNewPage7Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage7Localization() { LanguageId = langId };
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage8Localization:
            model = DB.TutorialNewPage8Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage8Localization() { LanguageId = langId };
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage9Localization:
            model = DB.TutorialNewPage9Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage9Localization() { LanguageId = langId };
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage10Localization:
            model = DB.TutorialNewPage10Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage10Localization() { LanguageId = langId };
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage11Localization:
            model = DB.TutorialNewPage11Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage11Localization() { LanguageId = langId };
            break;
            case TutorialNewInterfaceTextType.TutorialNewPage12Localization:
            model = DB.TutorialNewPage12Localizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TutorialNewPage12Localization() { LanguageId = langId };
            break;
            }

            var vm = new LocalizationPageTranslationVM
            {
                EnumInterfaceTextType = type,
                Model = model,
                AspSaveAction = "TutorialNewPOST",
                LanguageId = langId
            };
            return View(@"Views\Interface\GeneratedPage.cshtml", vm);
        }


        [Route("Interface/UngroupedInterfacePage")]
        public IActionResult UngroupedInterfacePage(UngroupedInterfaceTextType type, int? langId = null)
        {
            object model = null;

            switch (type)
            {
            case UngroupedInterfaceTextType.ChatBotLocalization:
            model = DB.ChatBotLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new ChatBotLocalization() { LanguageId = langId };
            break;
            case UngroupedInterfaceTextType.LegalLocalization:
            model = DB.LegalLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new LegalLocalization() { LanguageId = langId };
            break;
            case UngroupedInterfaceTextType.MainPageLocalization:
            model = DB.MainPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new MainPageLocalization() { LanguageId = langId };
            break;
            case UngroupedInterfaceTextType.SearchPageLocalization:
            model = DB.SearchPageLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new SearchPageLocalization() { LanguageId = langId };
            break;
            case UngroupedInterfaceTextType.SupportLocalization:
            model = DB.SupportLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new SupportLocalization() { LanguageId = langId };
            break;
            case UngroupedInterfaceTextType.TestsLocalization:
            model = DB.TestsLocalizations.FirstOrDefault(o => o.LanguageId == langId);
            if (model is null) model = new TestsLocalization() { LanguageId = langId };
            break;
            }

            var vm = new LocalizationPageTranslationVM
            {
                EnumInterfaceTextType = type,
                Model = model,
                AspSaveAction = "UngroupedPOST",
                LanguageId = langId
            };
            return View(@"Views\Interface\GeneratedPage.cshtml", vm);
        }




        private void FillLocalizationInterfaceAllPagesVM(LocalizationInterfaceAllPagesVM vm)
        {
            vm.Groups.Add(new LocalizationPageGroupItem("Авторизация", "AuthInterfacePage")
            {
                Pages = new List<LocalizationPageItem>()
                {
                    new LocalizationPageItem(AuthInterfaceTextType.AccountRegisteredLocalization),
                    new LocalizationPageItem(AuthInterfaceTextType.CreateAccountLocalization),
                    new LocalizationPageItem(AuthInterfaceTextType.EmailForgotPasswordCodePageLocalization),
                    new LocalizationPageItem(AuthInterfaceTextType.EmailForgotPasswordLocalization),
                    new LocalizationPageItem(AuthInterfaceTextType.EnterSMSCodeLocalization),
                    new LocalizationPageItem(AuthInterfaceTextType.LoginByEmailLocalization),
                    new LocalizationPageItem(AuthInterfaceTextType.LoginByPhoneLocalization),
                    new LocalizationPageItem(AuthInterfaceTextType.LoginMainPageLocalization),
                }
            });
            vm.Groups.Add(new LocalizationPageGroupItem("Библиотека", "LibraryInterfacePage")
            {
                Pages = new List<LocalizationPageItem>()
                {
                    new LocalizationPageItem(LibraryInterfaceTextType.BreathPracticeInterfaceLocalization),
                    new LocalizationPageItem(LibraryInterfaceTextType.EmotionsLocalization),
                    new LocalizationPageItem(LibraryInterfaceTextType.LibraryLocalization),
                }
            });
            vm.Groups.Add(new LocalizationPageGroupItem("Трекер настроения", "MoodTrackerInterfacePage")
            {
                Pages = new List<LocalizationPageItem>()
                {
                    new LocalizationPageItem(MoodTrackerInterfaceTextType.TrackerFactorsDetailsPageLocalization),
                    new LocalizationPageItem(MoodTrackerInterfaceTextType.TrackerFactorsPageLocalization),
                    new LocalizationPageItem(MoodTrackerInterfaceTextType.TrackerFinalPageLocalization),
                    new LocalizationPageItem(MoodTrackerInterfaceTextType.TrackerGeneralLocalization),
                    new LocalizationPageItem(MoodTrackerInterfaceTextType.TrackerHowAreYouLocalization),
                    new LocalizationPageItem(MoodTrackerInterfaceTextType.TrackerMainPageLocalization),
                    new LocalizationPageItem(MoodTrackerInterfaceTextType.TrackerStartLocalization),
                }
            });
            vm.Groups.Add(new LocalizationPageGroupItem("Профиль", "ProfileInterfacePage")
            {
                Pages = new List<LocalizationPageItem>()
                {
                    new LocalizationPageItem(ProfileInterfaceTextType.ProfileDownloadDataLocalization),
                    new LocalizationPageItem(ProfileInterfaceTextType.ProfileEnterEmailLocalization),
                    new LocalizationPageItem(ProfileInterfaceTextType.ProfileMainLocalization),
                    new LocalizationPageItem(ProfileInterfaceTextType.ProfileNotificationsLocalization),
                    new LocalizationPageItem(ProfileInterfaceTextType.ProfileSelectPeriodLocalization),
                    new LocalizationPageItem(ProfileInterfaceTextType.ProfileSelectTimeLocalization),
                }
            });
            vm.Groups.Add(new LocalizationPageGroupItem("Стартовые страницы", "StartInterfacePage")
            {
                Pages = new List<LocalizationPageItem>()
                {
                    new LocalizationPageItem(StartInterfaceTextType.HelloPageLocalization),
                    new LocalizationPageItem(StartInterfaceTextType.HelloSliderPageLocalization),
                    new LocalizationPageItem(StartInterfaceTextType.LetsStartPageLocalization),
                }
            });
            vm.Groups.Add(new LocalizationPageGroupItem("Подписки", "SubscriptionInterfacePage")
            {
                Pages = new List<LocalizationPageItem>()
                {
                    new LocalizationPageItem(SubscriptionInterfaceTextType.SelectSubscriptionLocalization),
                    new LocalizationPageItem(SubscriptionInterfaceTextType.SubscriptionEndedLocalization),
                    new LocalizationPageItem(SubscriptionInterfaceTextType.SubscriptionMainLocalization),
                    new LocalizationPageItem(SubscriptionInterfaceTextType.TrialPageLocalization),
                    new LocalizationPageItem(SubscriptionInterfaceTextType.TrialStartedLocalization),
                }
            });
            vm.Groups.Add(new LocalizationPageGroupItem("Обучение", "TutorialInterfacePage")
            {
                Pages = new List<LocalizationPageItem>()
                {
                    new LocalizationPageItem(TutorialInterfaceTextType.StartTutorialPageLocalization),
                    new LocalizationPageItem(TutorialInterfaceTextType.TutorialCompletedPageLocalization),
                }
            });
            vm.Groups.Add(new LocalizationPageGroupItem("Полное обучение", "TutorialNewInterfacePage")
            {
                Pages = new List<LocalizationPageItem>()
                {
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage1Localization),
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage2Localization),
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage3Localization),
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage4Localization),
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage5Localization),
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage6Localization),
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage7Localization),
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage8Localization),
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage9Localization),
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage10Localization),
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage11Localization),
                     new LocalizationPageItem(TutorialNewInterfaceTextType.TutorialNewPage12Localization),

                }
            });
            vm.Groups.Add(new LocalizationPageGroupItem("Прочее", "UngroupedInterfacePage")
            {
                Pages = new List<LocalizationPageItem>()
                {
                    new LocalizationPageItem(UngroupedInterfaceTextType.ChatBotLocalization),
                    new LocalizationPageItem(UngroupedInterfaceTextType.LegalLocalization),
                    new LocalizationPageItem(UngroupedInterfaceTextType.MainPageLocalization),
                    new LocalizationPageItem(UngroupedInterfaceTextType.SearchPageLocalization),
                    new LocalizationPageItem(UngroupedInterfaceTextType.SupportLocalization),
                    new LocalizationPageItem(UngroupedInterfaceTextType.TestsLocalization),
                }
            });
        }

    }
}
