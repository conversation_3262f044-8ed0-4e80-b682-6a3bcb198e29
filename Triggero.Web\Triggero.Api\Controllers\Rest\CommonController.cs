﻿using ImageMagick;
using MapsterMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Diagnostics.HealthChecks;
using System.Drawing;
using System.Net;
using Triggero.Database;
using Triggero.Domain.Enums;
using Triggero.Domain.Models;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Enums;
using Triggero.Domain.Models.Models;
using Triggero.Models;
using Triggero.Models.Enums;
using Triggero.Models.General;
using Triggero.Models.General.Influence;
using Triggero.Models.General.UserData;
using Triggero.Models.Localization;
using Triggero.Models.MoodTracker;

namespace Triggero.Domain.Controllers.Rest
{

    /// <summary>
    /// Разное
    /// </summary>
    [ApiController]
    [Route("[controller]")]
    public class CommonController : BaseApiController
    {
        private readonly IConfiguration _configuration;
        private readonly IWebHostEnvironment _hosting;
        private readonly IMemoryCache _cache;

        public CommonController(IMapper mapper,
            IWebHostEnvironment hosting,
            IConfiguration configuration,
            IMemoryCache cache,
            DatabaseContext db) : base(db, mapper)
        {
            _cache = cache;
            _hosting = hosting;
            _configuration = configuration;
        }


        /// <summary>
        /// Показывать ли заглушку для эпла
        /// </summary>
        /// <returns></returns>
        [Obsolete]
        [HttpGet, Route("CheckHui")]
        public bool CheckHui()
        {
            return true;
        }

        [HttpGet, Route("HealthCheck")]
        public async Task<HealthCheckResult> CheckHealthAsync(HealthCheckContext context, CancellationToken cancellationToken = default)
        {
            try
            {
                // Check if the database is accessible
                await DB.Database.ExecuteSqlRawAsync("SELECT 1", cancellationToken);
                return HealthCheckResult.Healthy("Database is reachable.");
            }
            catch (Exception ex)
            {
                return HealthCheckResult.Unhealthy("Database is not reachable.", ex);
            }
        }

        [HttpGet, Route("GetAvatars")]
        [ResponseCache(Duration = 120)]
        public async Task<List<UserAvatar>> GetAvatars()
        {
            return await DB.UserAvatars.Where(o => !o.IsDeleted).ToListAsync();
        }

        [ResponseCache(Duration = 120)]
        [HttpGet, Route("GetVideoBGs")]
        public async Task<List<VideoBg>> GetVideoBGs()
        {
            return await DB.VideoBgs.Where(o => !o.IsDeleted).ToListAsync();
        }


        [HttpGet, Route("GetFactors")]
        [ResponseCache(Duration = 120)]
        public async Task<List<Factor>> GetFactors()
        {
            return await DB.Factors.Where(o => !o.IsDeleted).ToListAsync();
        }

        [HttpGet, Route("GetFactorDetails")]
        [ResponseCache(Duration = 120)]
        public async Task<List<FactorDetail>> GetFactorDetails()
        {
            return await DB.FactorDetails.Where(o => !o.IsDeleted).ToListAsync();
        }


        [ResponseCache(VaryByQueryKeys = new[] { "userId" }, Duration = 120)]
        [HttpGet, Route("GetWhatNeedToHandle")]
        public async Task<List<NeedToHandle>> GetWhatNeedToHandle(int userId)
        {
            var items = new List<NeedToHandle>();

            var user = await DB.Users.Include(o => o.UserStatistics)
                .ThenInclude(o => o.TestPassingResults).ThenInclude(o => o.Test).ThenInclude(o => o.Results)
                .ThenInclude(o => o.NeedToHandleItems)
                .ThenInclude(o => o.Localizations)
                .ThenInclude(o => o.Language)
                .FirstOrDefaultAsync(o => o.Id == userId);

            if (user != null)
            {
                foreach (var test in user.UserStatistics.TestPassingResults.Select(o => o.Test))
                {
                    test.Results = test.Results.Where(o => !o.IsDeleted).ToList();
                }


                foreach (var passingResult in user.UserStatistics.TestPassingResults.TakeLast(7))
                {

                    var result = passingResult.Test.GetTestResult(passingResult.Score);

                    if (result != null && result.IsNegativeResult)
                    {
                        var tags = passingResult.Test.MainTags.Split(',', StringSplitOptions.RemoveEmptyEntries).ToList();
                        tags.ForEach(o => o = o.Trim());

                        foreach (var tag in tags)
                        {
                            //if (items.Count >= 5) break;

                            if (!items.Any(o => o.Title == tag))
                            {
                                items.Add(new NeedToHandle() { Title = tag });
                            }
                        }

                    }
                }

                if (DateTime.Now < user.NeedToHandleTagsBeforeTime || items.Count < 5)
                {
                    if (!user.NeedToHandleTagsHandled)
                    {
                        var userTags = user.NeedToHandleTags.Split(',', StringSplitOptions.RemoveEmptyEntries);
                        for (int i = 0; i < userTags.Length; i++)
                        {
                            userTags[i] = userTags[i].Trim();
                            if (!items.Any(o => o.Title == userTags[i]))
                            {
                                items.Add(new NeedToHandle() { Title = userTags[i] });
                            }
                        }
                    }
                }
                else if (DateTime.Now > user.NeedToHandleTagsBeforeTime && items.Count >= 5)
                {
                    user.NeedToHandleTagsHandled = true;
                    DB.Users.Update(user);
                    await DB.SaveChangesAsync();
                }

                items.ForEach(o => o.TestResultReferences.Clear());
                return items;
            }

            throw new ApiException(404);
        }


        [ResponseCache(VaryByQueryKeys = new[] { "userId", "needToHandleTags" }, Duration = 120)]
        [HttpPut, Route("SetWhatNeedToHandle")]
        public async Task SetWhatNeedToHandle(int userId, string needToHandleTags)
        {
            var user = DB.Users.FirstOrDefault(o => o.Id == userId);
            user.NeedToHandleTags = needToHandleTags;
            user.NeedToHandleTagsBeforeTime = DateTime.Now.AddDays(7);
        }


        [ResponseCache(Duration = 10)]
        [HttpGet, Route("GetToDoListRandom")]
        public string GetToDoListRandom()
        {
            var items = new List<ToDoListItem>();


            var fromDB = DB.ToDoListItems
                .Include(o => o.Test).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Test).ThenInclude(o => o.Questions).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Test).ThenInclude(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Test).ThenInclude(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Test).ThenInclude(o => o.Results)
                .Include(o => o.Test).ThenInclude(o => o.Results)
                .Include(o => o.Exercise).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Practice).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Topic).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();


            if (fromDB.Any())
            {
                var rnd = new Random();

                for (int i = 0; i < 3; i++)
                {
                    var item = fromDB[rnd.Next(0, fromDB.Count - 1)];
                    while (items.Any(o => o.Id == item.Id))
                    {
                        item = fromDB[rnd.Next(0, fromDB.Count - 1)];
                    }
                    items.Add(item);
                }
            }

            return MakeNewtonsoftJsonString(items);
        }


        [ResponseCache(Duration = 10)]
        [HttpGet, Route("GetRandomRecomendations")]
        public string GetRandomRecomendations()
        {



            var tests = DB.Tests
                  .Include(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Questions).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Results)
                  .Include(o => o.Results)
                  .Where(o => !o.IsDeleted && !o.IsHidden && o.TestPurpose == TestPurpose.Public);
            var practices = DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden);
            var exercises = DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Where(o => !o.IsDeleted && !o.IsHidden);
            var topics = DB.Topics
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Include(o => o.TopicRates)
                .Where(o => !o.IsDeleted && !o.IsHidden);
            SetRates(topics);


            var rnd = new Random();
            var items = new List<TagSearchItem>();

            while (items.Count < 4)
            {
                int randType = rnd.Next(1, 4);

                switch (randType)
                {
                case 1:
                var test = tests.Skip(rnd.Next(0, tests.Count() - 1)).FirstOrDefault();
                items.Add(new TagSearchItem() { Test = test });
                break;
                case 2:
                var exercise = exercises.Skip(rnd.Next(0, exercises.Count() - 1)).FirstOrDefault();
                items.Add(new TagSearchItem() { Exercise = exercise });
                break;
                case 3:
                var practice = practices.Skip(rnd.Next(0, practices.Count() - 1)).FirstOrDefault();
                items.Add(new TagSearchItem() { Practice = practice });
                break;
                case 4:
                var topic = topics.Skip(rnd.Next(0, topics.Count() - 1)).FirstOrDefault();
                items.Add(new TagSearchItem() { Topic = topic });
                break;
                }
            }


            return MakeNewtonsoftJsonString(items);
        }


        [ResponseCache(VaryByQueryKeys = new[] { "tag" }, Duration = 120)]
        [HttpGet, Route("GetTagSearchItems")]
        public string GetTagSearchItems(string tag)
        {
            var items = new List<TagSearchItem>();

            if (string.IsNullOrEmpty(tag))
            {
                return tag;
            }

            var tests = DB.Tests
                  .Include(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Questions).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Results)
                  .Include(o => o.Results)
                  .ToList()
                  .Where(o => !o.IsDeleted && !o.IsHidden && o.TestPurpose == TestPurpose.Public)
                  .Where(o => o.MainTags.Contains(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Contains(tag, StringComparison.OrdinalIgnoreCase))
                  .ToList();
            var practices = DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .ToList()
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.MainTags.Contains(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Contains(tag, StringComparison.OrdinalIgnoreCase))
                .ToList();
            var exercises = DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .ToList()
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.MainTags.Contains(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Contains(tag, StringComparison.OrdinalIgnoreCase))
                .ToList();
            var topics = DB.Topics
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Include(o => o.TopicRates)
                .ToList()
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.MainTags.Contains(tag, StringComparison.OrdinalIgnoreCase) || o.SecondaryTags.Contains(tag, StringComparison.OrdinalIgnoreCase))
                .ToList();
            SetRates(topics);


            tests.ForEach(o =>
            {
                items.Add(new TagSearchItem { Test = o });
            });
            practices.ForEach(o =>
            {
                items.Add(new TagSearchItem { Practice = o });
            });
            exercises.ForEach(o =>
            {
                items.Add(new TagSearchItem { Exercise = o });
            });
            topics.ForEach(o =>
            {
                items.Add(new TagSearchItem { Topic = o });
            });

            return MakeNewtonsoftJsonString(items);
        }

        [ResponseCache(Duration = 120)]
        [HttpGet, Route("GetAllSearchItems")]
        public string GetAllSearchItems()
        {
            var items = new List<TagSearchItem>();


            var tests = DB.Tests
                  .Include(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Questions).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Results)
                  .Include(o => o.Results)
                  .ToList()
                  .Where(o => !o.IsDeleted && !o.IsHidden && o.TestPurpose == TestPurpose.Public)
                  .ToList();
            var practices = DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .ToList()
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .ToList();
            var exercises = DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .ToList()
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .ToList();
            var topics = DB.Topics
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Include(o => o.TopicRates)
                .ToList()
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .ToList();
            SetRates(topics);


            tests.ForEach(o =>
            {
                items.Add(new TagSearchItem { Test = o });
            });
            practices.ForEach(o =>
            {
                items.Add(new TagSearchItem { Practice = o });
            });
            exercises.ForEach(o =>
            {
                items.Add(new TagSearchItem { Exercise = o });
            });
            topics.ForEach(o =>
            {
                items.Add(new TagSearchItem { Topic = o });
            });

            return MakeNewtonsoftJsonString(items);
        }


        //[ResponseCache(VaryByQueryKeys = new[] { "query" }, Duration = 120)]
        [HttpGet, Route("GetSearchItems")]
        public string GetSearchItems(string query)
        {
            var items = new List<TagSearchItem>();


            var tests = DB.Tests
                  .Include(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Category).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Questions).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Questions).ThenInclude(o => o.Options).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Results).ThenInclude(o => o.Localizations).ThenInclude(o => o.Language)
                  .Include(o => o.Results)
                  .Include(o => o.Results)
                  .ToList()
                  .Where(o => !o.IsDeleted && !o.IsHidden && o.TestPurpose == TestPurpose.Public)
                  .Where(o => o.Title.Contains(query, StringComparison.OrdinalIgnoreCase))
                  .ToList();
            var practices = DB.Practices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .ToList()
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.Title.Contains(query, StringComparison.OrdinalIgnoreCase))
                .ToList();
            var exercises = DB.Exercises
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .ToList()
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.Title.Contains(query, StringComparison.OrdinalIgnoreCase))
                .ToList();
            var topics = DB.Topics
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Include(o => o.Category)
                .Include(o => o.TopicRates)
                .ToList()
                .Where(o => !o.IsDeleted && !o.IsHidden)
                .Where(o => o.Title.Contains(query, StringComparison.OrdinalIgnoreCase))
                .ToList();
            SetRates(topics);


            tests.ForEach(o =>
            {
                items.Add(new TagSearchItem { Test = o });
            });
            practices.ForEach(o =>
            {
                items.Add(new TagSearchItem { Practice = o });
            });
            exercises.ForEach(o =>
            {
                items.Add(new TagSearchItem { Exercise = o });
            });
            topics.ForEach(o =>
            {
                items.Add(new TagSearchItem { Topic = o });
            });

            return MakeNewtonsoftJsonString(items);
        }

        [ResponseCache(Duration = 120)]
        [HttpGet, Route("GetInterfaceLocalization")]
        public InterfaceLocalization GetInterfaceLocalization(string langCode)
        {
            var incliding = GetLocalizationIncludes();

            var found = incliding.Where(o => o.Language != null)
                                 .FirstOrDefault(o => o.Language.Code == langCode);
            if (found is null)
            {
                //Русский язык значит возвращаем
                found = incliding.FirstOrDefault(o => o.Language == null);
            }
            return found;
        }


        [HttpGet, Route("AddUserDeviceIfNew")]
        public async Task<string> AddUserDeviceIfNew(int userId, string firebaseToken)
        {
            try
            {
                var user = DB.Users.Include(o => o.Devices)
                              .FirstOrDefault(o => o.Id == userId);
                if (!user.Devices.Any(o => o.FirebaseToken == firebaseToken))
                {
                    user.Devices.Add(new UserDevice
                    {
                        FirebaseToken = firebaseToken,
                    });
                }

                DB.Users.Update(user);
                await DB.SaveChangesAsync();

                return "Ок";
            }
            catch (Exception ex)
            {
                return ex.ToString();
            }
        }



        #region Сжатие изображения

        [HttpGet, Route("GetImage/{thumbnailSize}/{ext}/{path}")]
        public async Task<IActionResult> GetImage(string path,
            ThumbnailSize thumbnailSize,
            string ext = ".png")
        {
            var cacheKey = $"{path}_{thumbnailSize}_{ext}";

            if (!_cache.TryGetValue(cacheKey, out GetImgIfHasModel cachedImage))
            {
                var builtin = "/built_in/images/";
                var foldersArray = new string[]
                {
                    "/uploads/",
                    builtin
                };
                foreach (var folder in foldersArray)
                {
                    try
                    {
                        var imgBytes = GetImgIfHas(path, folder, thumbnailSize, ext);
                        if (imgBytes.ImgBytes != null && imgBytes.ImgBytes.Length > 0)
                        {

                            //add to cache if built-in and/or small
                            if (thumbnailSize == ThumbnailSize.Small ||
                                folder == builtin && thumbnailSize != ThumbnailSize.Original)
                            {
                                var cacheEntryOptions = new MemoryCacheEntryOptions()
                                    .SetSlidingExpiration(TimeSpan.FromMinutes(10)) // Cache for 10 minutes
                                    .SetSize(CalculateSize(imgBytes)); // Set the size of the cache entry

                                _cache.Set(cacheKey, imgBytes, cacheEntryOptions);
                            }

                            return new FileContentResult(imgBytes.ImgBytes, imgBytes.MimeType);
                        }
                    }
                    catch (Exception ex)
                    {
                        Console.WriteLine(ex);
                    }
                }
                return new NotFoundResult();
            }

            //return cached
            return new FileContentResult(cachedImage.ImgBytes, cachedImage.MimeType);
        }

        private long CalculateSize(GetImgIfHasModel imgBytes)
        {
            // Approximate size calculation: byte array length + size of the MIME type string
            return imgBytes.ImgBytes.Length + imgBytes.MimeType.Length * sizeof(char);
        }

        protected GetImgIfHasModel GetImgIfHas(string imgName, string folderPath, ThumbnailSize thumbnailSize, string ext = ".png")
        {
            string mimeType;
            if (ext.EndsWith("png"))
            {
                mimeType = "image/png";
            }
            else
            {
                mimeType = "image/jpeg";
            }

            //if (!imgName.Contains(folderPath))
            //{
            //    imgName = folderPath + imgName;
            //}

            //string root = "C:\\Users\\<USER>\\Desktop\\adminpanel\\wwwroot";
            //string root = "C:\\Users\\<USER>\\Desktop\\wwwroot";

            //string fullPath = root + imgName.Replace("/", "\\");
            if (string.IsNullOrWhiteSpace(_hosting.WebRootPath))
            {
                _hosting.WebRootPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
            }

            var root = _configuration["Files:ImagesRoot"];

            // Use ContentRootPath instead of wwwrootpath because of issue on Linux Hosting Environment
            //var path = Path.Combine(_hosting.ContentRootPath, "wwwroot", root);

            var fullPath = Path.Combine(_hosting.WebRootPath, folderPath.TrimStart('/'), imgName.TrimStart('/'));

            var imageBytes = System.IO.File.ReadAllBytes(fullPath);
            if (thumbnailSize == ThumbnailSize.Original)
            {
                return new GetImgIfHasModel
                {
                    ImgBytes = imageBytes,
                    MimeType = mimeType
                };
            }

            int height = 0;
            int width = 0;
            var newImageBytes = new byte[0];

            string resizedFilePath = root;
            switch (thumbnailSize)
            {
            case ThumbnailSize.Large:
            height = 900;
            width = 900;
            resizedFilePath += imgName.Replace(folderPath, $"{folderPath}thumbLarge_").Replace("/", "\\");
            break;
            case ThumbnailSize.Middle:
            height = 500;
            width = 500;
            resizedFilePath += imgName.Replace(folderPath, $"{folderPath}thumbMiddle_").Replace("/", "\\");
            break;
            case ThumbnailSize.Small:
            height = 250;
            width = 250;
            resizedFilePath += imgName.Replace(folderPath, $"{folderPath}thumbSmall_").Replace("/", "\\");
            break;
            }

            resizedFilePath = Path.ChangeExtension(resizedFilePath, ext);




            if (System.IO.File.Exists(resizedFilePath))
            {
                newImageBytes = System.IO.File.ReadAllBytes(resizedFilePath);
                return new GetImgIfHasModel
                {
                    ImgBytes = newImageBytes,
                    MimeType = mimeType
                };
            }

            if (ext.EndsWith("png"))
            {
                newImageBytes = CreatePngThumbnailUnsafe(imageBytes, height, width);
            }
            else
            {
                newImageBytes = CreateJpgThumbnailUnsafe(imageBytes, "#FFFFFF", height, width);
            }

            System.IO.File.WriteAllBytes(resizedFilePath, newImageBytes);
            return new GetImgIfHasModel
            {
                ImgBytes = newImageBytes,
                MimeType = mimeType
            };
        }


        public static Size ResizeImageToFit(int imageWidth, int imageHeight, int maxWidth, int maxHeight)
        {
            var widthRatio = maxWidth / (double)imageWidth;
            var heightRatio = maxHeight / (double)imageHeight;
            var minAspectRatio = Math.Min(widthRatio, heightRatio);
            if (minAspectRatio > 1)
                return new Size(imageWidth, imageHeight);
            return new Size((int)(imageWidth * minAspectRatio), (int)(imageHeight * minAspectRatio));
        }

        public static byte[] CreateJpgThumbnailUnsafe(byte[] imageBytes, string hexBackgroundColor, int maxWidth, int maxHeight, bool sharpen = false, int quality = 90)
        {
            using MagickImage image = new MagickImage(imageBytes);
            var scale = ResizeImageToFit(image.Width, image.Height, maxWidth, maxHeight);
            int width = scale.Width;
            int height = scale.Height;

            // convert -strip -interlace Plane -gaussian-blur 0.05 -quality 85% source.jpg result.jpg
            image.Settings.SetDefine(MagickFormat.Jpg, "compression", "dxt1");
            image.Resize(width, height);
            image.Format = MagickFormat.Pjpeg;

            image.BackgroundColor = new MagickColor(hexBackgroundColor);
            image.Alpha(AlphaOption.Remove); //flatten transparent image

            if (sharpen)
                image.AdaptiveSharpen(2, 1.2);//(2, 1.5); //todo add options

            image.Quality = quality;

            return image.ToByteArray();
        }
        public static byte[] CreatePngThumbnailUnsafe(byte[] imageBytes, int maxWidth, int maxHeight, bool sharpen = false, int quality = 90)
        {
            using MagickImage image = new MagickImage(imageBytes);
            var scale = ResizeImageToFit(image.Width, image.Height, maxWidth, maxHeight);
            int width = scale.Width;
            int height = scale.Height;

            // convert -strip -interlace Plane -gaussian-blur 0.05 -quality 85% source.jpg result.jpg
            image.Settings.SetDefine(MagickFormat.Png, "compression", "dxt1");
            image.Resize(width, height);
            image.Format = MagickFormat.Png;

            if (sharpen)
                image.AdaptiveSharpen(2, 1.2);//(2, 1.5); //todo add options

            image.Quality = quality;

            return image.ToByteArray();
        }



        #endregion



        #region Private methods

        private IEnumerable<InterfaceLocalization> GetLocalizationIncludes()
        {
            var items = DB.InterfaceLocalizations.Include(o => o.Auth).ThenInclude(o => o.AccountRegistered)
                                                                       .Include(o => o.Auth).ThenInclude(o => o.CreateAccount)
                                                                       .Include(o => o.Auth).ThenInclude(o => o.EmailForgotPasswordCode)
                                                                       .Include(o => o.Auth).ThenInclude(o => o.EmailForgotPassword)
                                                                       .Include(o => o.Auth).ThenInclude(o => o.EnterSMSCode)
                                                                       .Include(o => o.Auth).ThenInclude(o => o.LoginByEmail)
                                                                       .Include(o => o.Auth).ThenInclude(o => o.LoginByPhone)
                                                                       .Include(o => o.Auth).ThenInclude(o => o.LoginMainPage)
                                                                       .Include(o => o.Library).ThenInclude(o => o.BreathPractice)
                                                                       .Include(o => o.Library).ThenInclude(o => o.Emotions)
                                                                       .Include(o => o.Library).ThenInclude(o => o.Library)
                                                                       .Include(o => o.MoodTracker).ThenInclude(o => o.TrackerFactorsDetails)
                                                                       .Include(o => o.MoodTracker).ThenInclude(o => o.TrackerFactors)
                                                                       .Include(o => o.MoodTracker).ThenInclude(o => o.TrackerFinal)
                                                                       .Include(o => o.MoodTracker).ThenInclude(o => o.TrackerGeneral)
                                                                       .Include(o => o.MoodTracker).ThenInclude(o => o.TrackerHowAreYou)
                                                                       .Include(o => o.MoodTracker).ThenInclude(o => o.TrackerMainPage)
                                                                       .Include(o => o.MoodTracker).ThenInclude(o => o.TrackerStart)
                                                                       .Include(o => o.Profile).ThenInclude(o => o.ProfileDownloadData)
                                                                       .Include(o => o.Profile).ThenInclude(o => o.ProfileEnterEmail)
                                                                       .Include(o => o.Profile).ThenInclude(o => o.ProfileMain)
                                                                       .Include(o => o.Profile).ThenInclude(o => o.ProfileNotifications)
                                                                       .Include(o => o.Profile).ThenInclude(o => o.ProfileSelectPeriod)
                                                                       .Include(o => o.Profile).ThenInclude(o => o.ProfileSelectTime)

                                                                       .Include(o => o.Start).ThenInclude(o => o.HelloPage)
                                                                       .Include(o => o.Start).ThenInclude(o => o.HelloSliderPage)
                                                                       .Include(o => o.Start).ThenInclude(o => o.LetsStartPage)
                                                                       .Include(o => o.Subscriptions).ThenInclude(o => o.SelectSubscription)
                                                                       .Include(o => o.Subscriptions).ThenInclude(o => o.SubscriptionEnded)
                                                                       .Include(o => o.Subscriptions).ThenInclude(o => o.SubscriptionMain)
                                                                       .Include(o => o.Subscriptions).ThenInclude(o => o.TrialPage)
                                                                       .Include(o => o.Subscriptions).ThenInclude(o => o.TrialStarted)
                                                                       .Include(o => o.Tutorial).ThenInclude(o => o.StartTutorialPage)
                                                                       .Include(o => o.Tutorial).ThenInclude(o => o.TutorialCompletedPage)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage1)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage2)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage3)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage4)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage5)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage6)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage7)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage8)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage9)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage10)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage11)
                                                                       .Include(o => o.TutorialNew).ThenInclude(o => o.TutorialNewPage12)
                                                                       .Include(o => o.Language)
                                                                       .AsSplitQuery()
                                                                       .ToList();

            //Костыль, а то возникает ошибка Too many tables; MySQL can only use 61 tables in a join

            var include2 = DB.InterfaceLocalizations.Include(o => o.ChatBot)
                                                    .Include(o => o.Legal)
                                                    .Include(o => o.MainPage)
                                                    .Include(o => o.SearchPage)
                                                    .Include(o => o.Support)
                                                    .Include(o => o.Tests);

            foreach (var item in include2)
            {
                var actualItem = items.FirstOrDefault(o => o.Id == item.Id);
                actualItem.ChatBot = item.ChatBot;
                actualItem.Legal = item.Legal;
                actualItem.MainPage = item.MainPage;
                actualItem.SearchPage = item.SearchPage;
                actualItem.Support = item.Support;
                actualItem.Tests = item.Tests;
            }

            return items;
        }
        private void SetRates(IEnumerable<Topic> topics)
        {
            foreach (var topic in topics)
            {
                topic.Likes = topic.TopicRates.Count(o => o.RateType == RateType.Like);
                topic.Dislikes = topic.TopicRates.Count(o => o.RateType == RateType.Dislike);
                topic.TopicRates.Clear();
            }
        }


        #endregion
    }
}
