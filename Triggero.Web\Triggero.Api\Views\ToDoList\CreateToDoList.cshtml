﻿@using CRMWeb.Helpers.Html
@using CRMWeb.Helpers.Html.Specific;
@using CRMWeb.Helpers;
@using Triggero.Models.Enums;
@using Triggero.Models.General.Influence;
@using Triggero.Web.ViewModels;
@{
    ViewData["Title"] = "ДОБАВЛЕНИЕ ЗАДАЧИ TODO ЛИСТА";
}
@model CreateToDoListItemVM

 <!-- CONTENT -->
<form asp-action="CreateToDoListItemPOST" asp-controller="ToDoListItems" onsubmit="onSubmit(event)" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">

            <input type="hidden" asp-for="ToDoListItem.Id" />
            <input type="hidden" asp-for="ToDoListItem.IsHidden" />

            <div class="mb-3">
                <label for="" class="formlabel">Изображение</label>
                <div class="content-load" style="background-size: 100% 100%;">
                    <div class="close-file" id="close_file"><i class="fa-solid fa-xmark"></i></div>
                    <label class="loadfile1" id="upload-image" for="pct" style="height: 10em;"><div class="text-fileupload">Загрузите файл</div></label>
                    <input class="pctfile" type="file" name="file" id="pct">
                </div>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="ToDoListItem.Title" id="title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Кол-во минут</label>
                <input type="number" asp-for="ToDoListItem.MinutesCount" id="minutes" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Тип</label>
                <select class="form-select" id="type" asp-for="ToDoListItem.Type">
                    @Html.Raw(EnumToHtmlHelper<ToDoItemType>.Convert(Model.ToDoListItem.Type))
                </select>
            </div>

            <div class="mb-3">
                <select class="form-select" id="itemSelect">
                    @Html.Raw(ToDoItemsToHtmlOptions.Convert(Model.Tests,Model.Exercises,Model.Topics,Model.Practices,Model.ToDoListItem))
                </select>
            </div>

            <input id="practiceId" type="hidden" asp-for="ToDoListItem.PracticeId" />
            <input id="exerciseId" type="hidden" asp-for="ToDoListItem.ExerciseId" />
            <input id="testId" type="hidden" asp-for="ToDoListItem.TestId" />
            <input id="topicId" type="hidden" asp-for="ToDoListItem.TopicId" />

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>

<script>

    async function onSubmit(event) {

        event.preventDefault();

        await cropFinal('pct', 'upload-image');

        let itemSelect = document.getElementById('itemSelect');
        let selectedOption = itemSelect.options[itemSelect.selectedIndex];

        if (selectedOption != undefined) {

            if (selectedOption.hasAttribute('data-test-id')) {
                document.getElementById('testId').value = new Number(selectedOption.getAttribute('data-test-id'));
            }
            else if (selectedOption.hasAttribute('data-exercise-id')) {
                document.getElementById('exerciseId').value = new Number(selectedOption.getAttribute('data-exercise-id'));
            }
            else if (selectedOption.hasAttribute('data-topic-id')) {
                document.getElementById('topicId').value = new Number(selectedOption.getAttribute('data-topic-id'));
            }
            else if (selectedOption.hasAttribute('data-practice-id')) {
                document.getElementById('practiceId').value = new Number(selectedOption.getAttribute('data-practice-id'));
            }
            else {
                document.getElementById('practiceId').value = '';
                document.getElementById('topicId').value = '';
                document.getElementById('exerciseId').value = '';
                document.getElementById('testId').value = '';
            }
        }

        event.target.submit();
    }

    $(document).ready(function () {
        initImageCroppieSized('upload-image', 'pct', 'close_file', 300, 300);
    });

</script>