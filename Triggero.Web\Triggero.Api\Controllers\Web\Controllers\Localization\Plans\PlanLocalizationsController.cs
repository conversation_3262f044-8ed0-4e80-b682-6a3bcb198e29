﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Plans;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Plans;
using Triggero.Models.Practices;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class PlanLocalizationsController : AbsController
    {
        public PlanLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/Plans")]
        public IActionResult Plans()
        {
            var posts = DB.Plans
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .ToList();
            posts.Reverse();

            var vm = new LocalizationListVM<Plan>
            {
                Items = posts,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Plans\Plans\Plans.cshtml", vm);
        }






        [Route("Localizations/CreatePlansLocalization")]
        public IActionResult CreatePlansLocalization(int id)
        {
            var vm = new LocalizationVM<PlanLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Plans\Plans\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreatePlanLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreatePlanLocalizationPOST([FromForm] LocalizationVM<PlanLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();

            var option = DB.Plans
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => o.Id == vm.Id);
            option.Localizations.Add(vm.Localization);

            DB.Plans.Update(option);
            await DB.SaveChangesAsync();
            return RedirectToAction("Plans", "PlanLocalizations");
        }





        [Route("Localizations/UpdatePlansLocalization")]
        public IActionResult UpdatePlansLocalization(int id)
        {
            var option = DB.Plans
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<PlanLocalization>
            {
                Id = id,
                Localization = option.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Plans\Plans\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdatePlanLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdatePlanLocalizationPOST([FromForm] LocalizationVM<PlanLocalization> vm)
        {
            DB.PlanLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("Plans", "PlanLocalizations");
        }

    }
}
