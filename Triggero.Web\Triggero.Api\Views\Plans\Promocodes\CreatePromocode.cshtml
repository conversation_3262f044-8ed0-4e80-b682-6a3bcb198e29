﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Plans
@{
    ViewData["Title"] = "СОЗДАНИЕ ПРОМОКОДА";
}
@model Promocode

 <!-- CONTENT -->
<form asp-action="CreatePromocodePOST" asp-controller="Promocodes" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">


           <div class="mb-3">
               <label for="" class="formlabel">Код</label>
               <input type="text" asp-for="Code" required class="form-control" placeholder="">
           </div>

           <div class="mb-3">
                <label for="" class="formlabel">Процент</label>
                <input type="number" asp-for="Value" required class="form-control" placeholder="">
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>