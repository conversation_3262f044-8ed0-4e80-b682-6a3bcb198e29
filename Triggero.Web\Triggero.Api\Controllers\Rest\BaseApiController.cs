﻿using MapsterMapper;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using System.Security.Claims;
using Triggero.Database;
using Triggero.Models.General;

namespace Triggero.Domain.Controllers.Rest;

public class BaseApiController : ControllerBase
{
    protected readonly DatabaseContext DB;

    protected readonly IMapper Mapper;

    public BaseApiController(DatabaseContext db, IMapper mapper)
    {
        DB = db;
        this.Mapper = mapper;
    }

    protected async Task<User> GetAuthorizedUser()
    {
        var globalIdClaim = User?.FindFirst(ClaimTypes.NameIdentifier)?.Value;

        if (string.IsNullOrEmpty(globalIdClaim))
            return null;

        return await DB.Users.FirstOrDefaultAsync(o => o.GlobalId == globalIdClaim);
    }

    [NonAction]
    public string MakeNewtonsoftJsonString<T>(T model)
    {
        var testJson = JsonConvert.SerializeObject(model);
        return testJson;
    }

}