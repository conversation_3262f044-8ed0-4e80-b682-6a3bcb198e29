﻿@using Triggero.Models
@using Triggero.Models.General.Influence;
@{
    ViewData["Title"] = "TODO ЛИСТ";
}
@model List<ToDoListItem>
<!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                    <th scope="col">ID</th>
                    <th scope="col">Название</th>
                    <th scope="col">Дата добавления</th>
                    <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model)
                    {
                         <tr>
                            <th scope="row">#@item.Id</th>
                            <td>@item.Title</td>
                            <td>@item.CreatedAt.ToString("dd.MM.yyyy")</td>
                            <td>
                                <div class="interect">
                                    <a asp-action="UpdateToDoListItem" asp-controller="ToDoListItems" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    <a onclick="confirmDeleting('deleteItem(@item.Id)')" class="delete"><i class="fa-solid fa-trash"></i></a>
                                </div>
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

            <div class="mb-3 position-right">
                <a asp-action="CreateToDoListItem" asp-controller="ToDoListItems" class="button-classic">Добавить</a>
            </div>

            <script>
                $(document).ready( function () {
                    $('#tablemanager').DataTable();
                } );
            </script>

            <script>
                async function deleteItem(id) {
                    await fetch(document.location.origin + '/DeleteToDoListItem?id=' + id,
                        {
                            method: 'DELETE'
                        });
                }
            </script>
        </div>
    </div>
</div>