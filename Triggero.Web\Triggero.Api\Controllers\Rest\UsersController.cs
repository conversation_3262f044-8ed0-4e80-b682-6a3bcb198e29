﻿using AppoMobi.Specials;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Common.Helpers;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Domain.Enums;
using Triggero.Domain.Models;
using Triggero.Models.Enums;
using Triggero.Models.General;
using Triggero.Models.General.Settings;
using Triggero.Models.General.UserData;
using Triggero.Models.Messengers.ChatBot;
using Triggero.Models.Messengers.Support;
using Triggero.Models.Tickets;

namespace Triggero.Domain.Controllers.Rest.General.Users
{
    /// <summary>
    /// Старинный контролер управления пользователями
    /// </summary>
    [Obsolete]
    [ApiController]
    [Route("[controller]")]
    public class UsersController : ApiController
    {
        public UsersController(DatabaseContext db) : base(db)
        {
        }

        #region Получения пользователя

        [HttpGet, Route("GetUserById")]
        public async Task<User> GetUserById(int id, OSType OS = OSType.iOS)
        {
            var user = IncludeUserFields().FirstOrDefault(o => o.Id == id && !o.IsDeleted);
            if (user != null)
            {
                SetStatisticsCounters(user);

                //if (OS == OSType.iOS)
                //{
                //    user.UserSubscription.SubscriptionDuration = BuiltInPlanType.Year;
                //    user.UserSubscription.SubscriptionBefore = DateTime.MaxValue;
                //    user.UserSubscription.TrialActivated = true;
                //    user.UserSubscription.SubscriptionType = SubscriptionType.Full;
                //}

                user.Password = "";
            }
            return user;
        }

        [HttpGet, Route("GetUserByPhone")]
        public async Task<User> GetUserByPhone(string phone, OSType OS = OSType.iOS)
        {
            var user = IncludeUserFields().FirstOrDefault(o => o.Phone.Trim().Replace("+", "") == phone.Trim().Replace("+", "") && !o.IsDeleted);
            if (user != null)
            {
                SetStatisticsCounters(user);

                user.Password = "";
            }
            return user;
        }

        [HttpGet, Route("GetUserByEmail")]
        public async Task<User> GetUserByEmail(string email, OSType OS = OSType.iOS)
        {
            var user = IncludeUserFields().FirstOrDefault(o => o.Email.Trim().ToLower() == email.Trim().ToLower() && !o.IsDeleted);
            if (user != null)
            {
                SetStatisticsCounters(user);
                //if (OS == OSType.iOS)
                //{
                //    user.UserSubscription.SubscriptionDuration = BuiltInPlanType.Year;
                //    user.UserSubscription.SubscriptionBefore = DateTime.MaxValue;
                //    user.UserSubscription.TrialActivated = true;
                //    user.UserSubscription.SubscriptionType = SubscriptionType.Full;
                //}

                user.Password = "";
            }


            return user;
        }

        [HttpPost, Route("GetUserByEmailAndPassword")]
        public async Task<User> GetUserByEmailAndPassword([FromBody] AuthUserModel auth, OSType OS = OSType.iOS)
        {
            var user = IncludeUserFields().FirstOrDefault(o => o.Email.Trim().ToLower() == auth.Email.Trim().ToLower() && o.Password == auth.Password && !o.IsDeleted);
            if (user != null)
            {
                SetStatisticsCounters(user);

                //if (OS == OSType.iOS)
                //{
                //    user.UserSubscription.SubscriptionDuration = BuiltInPlanType.Year;
                //    user.UserSubscription.TrialActivated = true;
                //    user.UserSubscription.SubscriptionType = SubscriptionType.Full;
                //    user.UserSubscription.SubscriptionBefore = DateTime.MaxValue;
                //}

                user.Password = "";
            }


            return user;
        }

        private IQueryable<User> IncludeUserFields()
        {
            return DB.Users.Include(o => o.Avatar)
                     .Include(o => o.NotificationSettings)
                     .Include(o => o.UserSubscription).ThenInclude(o => o.UserSubsctiptionOptions).ThenInclude(o => o.PlanOption).ThenInclude(o => o.Localizations)
                     .Include(o => o.UserStatistics).ThenInclude(o => o.ExercisePassingResults)
                     .Include(o => o.UserStatistics).ThenInclude(o => o.TestPassingResults)
                     .Include(o => o.UserStatistics).ThenInclude(o => o.PracticePassingResults)
                     .Include(o => o.UserStatistics).ThenInclude(o => o.BreathPracticePassingResults);
        }

        private void SetStatisticsCounters(User user)
        {
            if (user.UserStatistics != null)
            {
                user.UserStatistics.ExercisePassingResultsCount = user.UserStatistics.ExercisePassingResults.Count;
                user.UserStatistics.TestPassingResultsCount = user.UserStatistics.TestPassingResults.Count;
                user.UserStatistics.PracticePassingResultsCount = user.UserStatistics.PracticePassingResults.Count;
                user.UserStatistics.BreathPracticePassingResultsCount = user.UserStatistics.BreathPracticePassingResults.Count;

                user.UserStatistics.ExercisePassingResults.Clear();
                user.UserStatistics.TestPassingResults.Clear();
                user.UserStatistics.PracticePassingResults.Clear();
                user.UserStatistics.BreathPracticePassingResults.Clear();
            }
        }


        #endregion

        [HttpPost, Route("RegisterUser")]
        public async Task<IResult> RegisterUser([FromBody] RegisterUserDto model)
        {
            try
            {
                var phoneNumber = PhoneConverter.ImportPhoneNumber(model.Phone);
                if (phoneNumber == null)
                {
                    throw new ApplicationException("Некорректный номер телефона");
                }
                model.Phone = phoneNumber;

                if (await GetUserByPhone(model.Phone) != null) return Results.BadRequest("Данный номер телефона не доступен для регистрации");
                if (await GetUserByEmail(model.Email) != null) return Results.BadRequest("Данный адрес электронной почты не доступен для регистрации");

                var user = new User
                {
                    Email = model.Email.ToLower(),
                    Phone = model.Phone,
                    Name = model.Name,
                    Password = model.Password,
                    Avatar = DB.UserAvatars.FirstOrDefault(),
                    Login = model.Email.ToLower().Split('@')[0],
                    Role = UserRole.User,
                    RegisteredAt = DateTime.UtcNow,
                    Patronymic = "",
                    Surname = "",
                    NeedToHandleTags = "",
                    EmailToSendReports = model.Email.ToLower(),
                    SupportChat = new SupportChat(),
                    ChatBotChat = new ChatBotChat(),
                    UserFavorites = new UserFavorites(),
                    NotificationSettings = new NotificationSettings(),
                    UserStatistics = new UserStatistics(),
                    UserSubscription = new UserSubscription(),
                    Username = model.Email.ToLower().Split('@')[0]
                };

                DB.Users.Add(user);

                await DB.SaveChangesAsync();

                //user.SupportChat = new SupportChat() { UserId = user.Id };
                //user.ChatBotChat = new ChatBotChat() { UserId = user.Id };

                //DB.Users.Add(user);
                //await DB.SaveChangesAsync();

                return Results.Ok(true);
            }
            catch (Exception ex)
            {
                return Results.Problem(ex.ToString());
            }
        }


        [HttpPut, Route("ChangePassword")]
        public async Task<User> ChangePassword(string email, string password, OSType OS = OSType.iOS)
        {
            var user = await GetUserByEmail(email);
            if (user != null)
            {
                user.Password = password;

                DB.Users.Update(user);

                await DB.SaveChangesAsync();

                //if (OS == OSType.iOS)
                //{
                //    user.UserSubscription.SubscriptionDuration = BuiltInPlanType.Year;
                //    user.UserSubscription.SubscriptionBefore = DateTime.MaxValue;
                //    user.UserSubscription.TrialActivated = true;
                //    user.UserSubscription.SubscriptionType = SubscriptionType.Full;
                //}

                user.Password = "";
            }


            return user;
        }



        [HttpGet, Route("GetUserTickets")]
        public async Task<List<Ticket>> GetUserTickets(int id)
        {
            return DB.Users
                .Include(o => o.Tickets).ThenInclude(o => o.Messages).ThenInclude(o => o.Attachments)
                .FirstOrDefault(o => o.Id == id && !o.IsDeleted)
                .Tickets;
        }

        [HttpGet, Route("DeleteUserAccount")]
        public async Task DeleteUserAccount(int id)
        {
            var user = DB.Users.FirstOrDefault(o => o.Id == id);
            user.IsDeleted = true;

            DB.Users.Update(user);
            await DB.SaveChangesAsync();
        }




        #region Редактирование основных данных пользователя

        [HttpPut, Route("SetUserAvatar")]
        public async Task SetUserAvatar(int userId, int avatarId)
        {
            var user = DB.Users.FirstOrDefault(o => o.Id == userId);
            user.AvatarId = avatarId;

            DB.Users.Update(user);
            await DB.SaveChangesAsync();
        }

        [HttpPut, Route("SetName")]
        public async Task SetName(int userId, string name)
        {
            var user = DB.Users.FirstOrDefault(o => o.Id == userId);
            user.Name = name;

            DB.Users.Update(user);
            await DB.SaveChangesAsync();
        }

        [HttpPut, Route("SetEmailToSendReports")]
        public async Task SetEmailToSendReports(int userId, string email)
        {
            var user = DB.Users.FirstOrDefault(o => o.Id == userId);
            user.EmailToSendReports = email;

            DB.Users.Update(user);
            await DB.SaveChangesAsync();
        }
        #endregion


        [HttpPut, Route("UpdateNotificationSettings")]
        public async Task<NotificationSettings> UpdateNotificationSettings([FromBody] NotificationSettings settings)
        {
            var existing = DB.NotificationSettings.First(x => x.Id == settings.Id);

            Reflection.MapProperties(settings, existing);

            DB.Entry(existing).State = EntityState.Modified;

            await DB.SaveChangesAsync();

            return existing;
        }



    }
}
