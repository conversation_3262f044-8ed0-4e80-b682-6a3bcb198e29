﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Practices
@using Triggero.Models.Practices.Categories
@using Triggero.Web.ViewModels.Practices
@{
    ViewData["Title"] = "СОЗДАНИЕ ДЫХАТЕЛЬНОЙ ПРАКТИКИ";
}
@model BreathPractice

 <!-- CONTENT -->
<form asp-action="CreateBreathPracticePOST" asp-controller="BreathPractices" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">

            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Текст</label>
                <textarea class="form-control" required asp-for="Description" rows="3" placeholder=""></textarea>
            </div>

                
            <div class="mb-3">
                <label for="" class="formlabel">Изображение</label>
                <input type="file" name="file2" required class="form-control" placeholder="">
            </div>


            <div class="mb-3">
                <label for="" class="formlabel">Аудиозапись</label>
                <input type="file" name="file" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Время в минутах</label>
                <input type="number" asp-for="TimeMinutes" required class="form-control" placeholder="">
            </div>

             <div class="mb-3">
                <label for="" class="formlabel">Кол-во секунд на вдох</label>
                <input type="number" asp-for="InhaleSeconds" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Кол-во секунд на выдох</label>
                <input type="number" asp-for="ExhaleSeconds" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Кол-во секунд задержки</label>
                <input type="number" asp-for="DelaySeconds" required class="form-control" placeholder="">
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>