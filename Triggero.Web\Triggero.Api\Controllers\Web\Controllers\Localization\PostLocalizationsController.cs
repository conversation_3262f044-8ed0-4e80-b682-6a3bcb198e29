﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class PostLocalizationsController : AbsController
    {
        public PostLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Localizations/Posts")]
        public IActionResult Posts()
        {
            var posts = DB.Posts
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            posts.Reverse();

            var vm = new LocalizationListVM<Post>
            {
                Items = posts,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Posts\Posts.cshtml", vm);
        }


        //[Route("Localizations/CreatePostLocalization")]
        public IActionResult CreatePostLocalization(int id)
        {
            var vm = new LocalizationVM<PostLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Posts\CreateLocalization.cshtml", vm);
        }

        [HttpPost]
        //[Route("Localizations/CreatePostLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreatePostLocalizationPOST([FromForm] LocalizationVM<PostLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();
            var post = DB.Posts.FirstOrDefault(o => o.Id == vm.Id);
            post.Localizations.Add(vm.Localization);

            DB.Posts.Update(post);
            await DB.SaveChangesAsync();
            return RedirectToAction("Posts", "PostLocalizations");
        }



        //[Route("Localizations/UpdatePostLocalization")]
        public IActionResult UpdatePostLocalization(int id)
        {
            var post = DB.Posts
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<PostLocalization>
            {
                Id = id,
                Localization = post.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Posts\UpdateLocalization.cshtml", vm);
        }

        [HttpPost]
        //[Route("Localizations/UpdatePostLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdatePostLocalizationPOST([FromForm] LocalizationVM<PostLocalization> vm)
        {
            DB.PostLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("Posts", "PostLocalizations");
        }

    }
}
