﻿using System;
using System.Collections.Generic;
using System.Text;
using Triggero.Models.Abstractions;
using Triggero.Models.Practices;
using Xamarin.Forms;

namespace Triggero.Mobile.Models
{
    public class BaseQuestionView : ContentView
    {
        public BaseQuestionView(Question question)
        {
            Question = question;
        }

        private Question question;
        public Question Question
        {
            get { return question; }
            set { question = value; OnPropertyChanged(nameof(Question)); }
        }

        public virtual List<QuestionOption> GetSelectedOptions() { throw new NotImplementedException(); }
    }
}
