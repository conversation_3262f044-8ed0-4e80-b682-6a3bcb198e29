﻿using Newtonsoft.Json;
using Odintcovo.API.Helpers;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Text;
using System.Threading.Tasks;
using Triggero.Domain.Models;
using Triggero.Models.General;
using Triggero.Models.General.Influence;
using Triggero.Models.Localization;
using Triggero.Models.MoodTracker;
using Triggero.Models.Tests;

namespace MobileAPIWrapper.Methods
{
    public class PaymentMethods
    {
        private string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("Payments/");

        public async Task<CreatedPaymentModel> PaySubscription(int userID, SubscriptionPaymentSettings settings)
        {
            string url = BASE_HOST + $"PaySubscription?userID={userID}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Post, settings);

            var obj = JsonConvert.DeserializeObject<CreatedPaymentModel>(response.Content);
            return obj;
        }



        public async Task<object> SetPaidViaAppstore(string paymentId)
        {
            string url = BASE_HOST + $"SetPaidViaAppstore?userPaymentId={paymentId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<object>(response.Content);
            return obj;
        }



        public async Task<bool> CheckPayment(string paymentId)
        {
            string url = BASE_HOST + $"CheckPayment?paymentId={paymentId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }



        public async Task UnbindCard(int userId)
        {
            string url = BASE_HOST + $"UnbindCard?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);
        }
        public async Task<List<FactorDetail>> GetFactorDetails()
        {
            string url = BASE_HOST + "GetFactorDetails";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<FactorDetail>>(response.Content);
            return obj;
        }











        public async Task<List<ToDoListItem>> GetToDoListRandom()
        {
            string url = BASE_HOST + $"GetToDoListRandom";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<ToDoListItem>>(str);
            return obj;
        }
        public async Task<List<NeedToHandle>> GetWhatNeedToHandle(int userId)
        {
            string url = BASE_HOST + $"GetWhatNeedToHandle?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);


            var obj = JsonConvert.DeserializeObject<List<NeedToHandle>>(response.Content);
            return obj;
        }








        public async Task<List<TagSearchItem>> GetAllSearchItems()
        {
            string url = BASE_HOST + $"GetAllSearchItems";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<TagSearchItem>>(str);
            return obj;
        }

        public async Task<List<TagSearchItem>> GetTagSearchItems(string tag)
        {
            string url = BASE_HOST + $"GetTagSearchItems?tag={tag}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);


            var str = JsonConvert.DeserializeObject<string>(response.Content);
            var obj = JsonConvert.DeserializeObject<List<TagSearchItem>>(str);
            return obj;
        }





        public async Task<InterfaceLocalization> GetInterfaceLocalization(string langCode)
        {
            string url = BASE_HOST + $"GetInterfaceLocalization?langCode={langCode}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<InterfaceLocalization>(response.Content);
            return obj;
        }
    }
}
