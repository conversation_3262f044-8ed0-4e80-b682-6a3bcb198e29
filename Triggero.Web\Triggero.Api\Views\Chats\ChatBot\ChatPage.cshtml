﻿@using MarkupCreator.Helpers.Converters
@using Triggero.Models.Messengers.ChatBot;
@using Triggero.Models.Tickets
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "Чат с пользователем";
}
@model ChatBotChat


 <!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="chat">
            <div class="chat-header">
                <div class="profile">
                    <div class="avatar"></div>
                    <div class="info">
                        <div class="username">@Model.User.Name @Model.User.Surname</div>
                       @* <div class="status">
                            <div class="online"></div>
                            <div class="text">Online</div>
                        </div>*@
                    </div>
                </div>
            </div>

            <div class="chat-body">

               @* <div class="left">
                    <div class="avatar"></div>
                    <div class="message">
                        @Model.Ticket.Description
                    </div>
                </div>*@

                @foreach (var msg in Model.Messages)
                {
                    if (msg.ChatSide == Triggero.Models.Enums.ChatSide.Admin)
                    {
                        <div class="right">
                            <div class="avatar" style="background: url('')"></div>
                            <div class="message">
                                @msg.Text
                            </div>
                            @if (msg.Attachments.Any())
                            {
                               <a class="reply">@msg.Attachments.Count рекомендации от бота</a>
                            }
                           
                        </div>
                    }
                    else if (msg.ChatSide == Triggero.Models.Enums.ChatSide.User)
                    {
                         <div class="left">
                            <div class="avatar" style="background: url('@Model.User.Avatar?.AvatarPath')"></div>
                            <div class="message">
                                 @msg.Text
                            </div>    
                        </div>
                    }
                }

            </div>

            <form asp-action="SendMessage" asp-controller="ChatBot" asp-route-chatId="@Model.Id" method="post" enctype="multipart/form-data" class="chat-footer">

                <input type="text" name="Text" class="form-control form-send" placeholder="Введите сообщение">
                <button type="submit" class="button-classic">Отправить</button>
            </form>
            
@*             <div class="chat-footer">
                <a asp-action="SetStatus" asp-controller="ChatBot" asp-route-ticketId="@Model.Id" asp-route-status="3">
                    <button class="button-classic">Установить статус "Оказана помощь"</button>
                </a>
            </div> *@
        </div>
    </div>
</div>