﻿<?xml version="1.0" encoding="UTF-8" ?>
<views:Canvas
    x:Class="Triggero.MauiClient.Views.ListCategoriesView"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:triggeroV2="clr-namespace:Triggero.Mobile;assembly=Triggero.Mobile"
    xmlns:viewModels="clr-namespace:Triggero.Mobile.ViewModels;assembly=Triggero.Mobile"
    xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="this"
    x:DataType="viewModels:BaseCategoriesViewModel"
    Gestures="Lock"
    HardwareAcceleration="Enabled"
    HorizontalOptions="Fill"
    VerticalOptions="Fill">

    <views:SkiaLayout
        HorizontalOptions="Fill"
        VerticalOptions="Fill">

        <views:SkiaScroll
            FrictionScrolled="0.35"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <views:SkiaLayout
                x:Name="StackCells"
                HorizontalOptions="Fill"
                ItemTemplate="{Binding ItemTemplate}"
                ItemsSource="{Binding Items}"
                RecyclingTemplate="Disabled"
                Spacing="12"
                Type="Column"
                UseCache="Image" />

        </views:SkiaScroll>


        <!--<views:SkiaLabel
            UseCache="Operations"
            Margin="8"
            BackgroundColor="Black"
            HorizontalOptions="Start"
            InputTransparent="True"
            Text="{Binding Source={x:Reference StackCells}, Path=DebugString}"
            VerticalOptions="Start" />-->

        <!--  FPS  -->
        <views:SkiaLabelFps
            Margin="0,0,4,84"
            BackgroundColor="DarkRed"
            ForceRefresh="False"
            HorizontalOptions="End"
            IsVisible="{x:Static triggeroV2:Globals.ShowFPS}"
            Rotation="-45"
            TextColor="White"
            VerticalOptions="End" />

    </views:SkiaLayout>

</views:Canvas>