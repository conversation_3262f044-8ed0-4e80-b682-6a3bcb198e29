﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.MoodTracker;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Localization.Practices.Categories;
using Triggero.Models.MoodTracker;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class FactorDetailLocalizationsController : AbsController
    {
        public FactorDetailLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/FactorDetails")]
        public IActionResult FactorDetails()
        {
            var cats = DB.FactorDetails
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            cats.Reverse();

            var vm = new LocalizationListVM<FactorDetail>
            {
                Items = cats,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\MoodTracker\FactorDetails\FactorDetails.cshtml", vm);
        }







        [Route("Localizations/CreateFactorDetailLocalization")]
        public IActionResult CreateFactorDetailLocalization(int id)
        {
            var vm = new LocalizationVM<FactorDetailLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\MoodTracker\FactorDetails\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreateFactorDetailLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateFactorDetailLocalizationPOST([FromForm] LocalizationVM<FactorDetailLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();

            var cat = DB.FactorDetails.FirstOrDefault(o => o.Id == vm.Id);
            cat.Localizations.Add(vm.Localization);

            DB.FactorDetails.Update(cat);
            await DB.SaveChangesAsync();
            return RedirectToAction("FactorDetails", "FactorDetailLocalizations");
        }





        [Route("Localizations/UpdateFactorDetailLocalization")]
        public IActionResult UpdateFactorDetailLocalization(int id)
        {
            var cat = DB.FactorDetails
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<FactorDetailLocalization>
            {
                Id = id,
                Localization = cat.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\MoodTracker\FactorDetails\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdateFactorDetailLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateFactorDetailLocalizationPOST([FromForm] LocalizationVM<FactorDetailLocalization> vm)
        {
            DB.FactorDetailLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("FactorDetails", "FactorDetailLocalizations");
        }

    }
}
