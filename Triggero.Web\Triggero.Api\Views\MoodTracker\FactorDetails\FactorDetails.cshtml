﻿@using Triggero.Models.MoodTracker
@using Triggero.Models.Practices
@using Triggero.Models.Practices.Categories
@using Triggero.Models.Tests
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "ПОДПУНКТЫ ФАКТОРОВ";
}
@model List<FactorDetail>

  <!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                    <th scope="col">ID</th>
                    <th scope="col">Название</th>
                    <th scope="col">Фактор</th>
                    <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>


                    @foreach(var item in Model)
                    {
                         <tr>
                            <th scope="row">#@item.Id</th>
                            <td>@item.Title</td>
                            <td>@item.Factor.Title</td>
                            <td>
                                <div class="interect">
                                    <a asp-action="UpdateFactorDetail" asp-controller="FactorDetails" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                    <a onclick="confirmDeleting('deleteItem(@item.Id)')" class="delete"><i class="fa-solid fa-trash"></i></a>
                                </div>
                            </td>
                        </tr>    
                    }

                               
                </tbody>
            </table>

            <div class="mb-3 position-right">
                <a asp-action="CreateFactorDetail" asp-controller="FactorDetails" class="button-classic">Добавить</a>
            </div>

            <script>
                $(document).ready( function () {
                    $('#tablemanager').DataTable();
                } );
            </script>

             <script>
                async function deleteItem(id) {
                    await fetch(document.location.origin + '/DeleteFactorDetail?id=' + id,
                        {
                            method: 'DELETE'
                        });
                }
            </script>

        </div>
    </div>
</div>