﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@{
    ViewData["Title"] = "РЕДАКТИРОВАНИЕ НОВОСТИ";
}
@model Post

 <!-- CONTENT -->
<form asp-action="UpdatePostPOST" asp-controller="Posts" method="post" enctype="multipart/form-data" class="content">

    <input type="hidden" asp-for="Id" value="@Model.Id"/>
    <input type="hidden" asp-for="ImgPath" value="@Model.ImgPath"/>
    <input type="hidden" asp-for="Watches" value="@Model.Watches"/>
    <input type="hidden" asp-for="Likes" value="@Model.Likes"/>
    <input type="hidden" asp-for="Dislikes" value="@Model.Dislikes"/>

    <div class="row">
        <div class="page">
            <div class="mb-3">
                <label for="" class="formlabel">Загрузить изображение</label>
                <div class="content-load">
                    <label class="loadfile1" for="pct" style="background-size: 40% 100%;"><i class="fa-solid fa-images"></i></label>
                    <input class="pctfile" name="file" type="file" id="pct">
                  
                    <script>
                        document.querySelector(".pctfile").addEventListener("change", function () {
                        if (this.files[0]) {
                        var fr = new FileReader();
                  
                        fr.addEventListener("load", function () {
                            document.querySelector(".loadfile1").style.backgroundImage = "url(" + fr.result + ")";
                            document.querySelector(".loadfile1").style.color = "transparent";
                        }, false);
                  
                        fr.readAsDataURL(this.files[0]);
                        }
                    });
                    </script>
                </div>
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Текст</label>
                <textarea class="form-control" required asp-for="Description" rows="3" placeholder=""></textarea>
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>