using System.ComponentModel;
using System.Windows.Input;
using DrawnUi.Draw;
using Triggero.MobileMaui.Services;

namespace Triggero.MobileMaui.Views
{
    public partial class FooterNavigation : DrawnUi.Views.Canvas
    {
        public FooterNavigation()
        {
            InitializeComponent();
            BindingContext = this;
        }

        // MainPage reference
        public static readonly BindableProperty MainPageProperty =
            BindableProperty.Create(nameof(MainPage), typeof(MainPage), typeof(FooterNavigation));

        public MainPage MainPage
        {
            get { return (MainPage)GetValue(MainPageProperty); }
            set { SetValue(MainPageProperty, value); }
        }

        // Selection state properties
        private bool _isMainPageSelected;
        public bool IsMainPageSelected
        {
            get => _isMainPageSelected;
            set
            {
                if (_isMainPageSelected != value)
                {
                    _isMainPageSelected = value;
                    OnPropertyChanged();

                    if (value)
                    {
                        IsLibraryPageSelected = false;
                        IsTestsPageSelected = false;
                        IsChatBotPageSelected = false;

                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            if (MainPage != null)
                            {
                                MainPage.SetView(MainPage.SubViewHome ?? new HomeView());
                            }
                        });
                    }
                }
            }
        }

        private bool _isLibraryPageSelected;
        public bool IsLibraryPageSelected
        {
            get => _isLibraryPageSelected;
            set
            {
                if (_isLibraryPageSelected != value)
                {
                    _isLibraryPageSelected = value;
                    OnPropertyChanged();

                    if (value)
                    {
                        IsMainPageSelected = false;
                        IsTestsPageSelected = false;
                        IsChatBotPageSelected = false;

                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            if (MainPage != null)
                            {
                                MainPage.SetView(MainPage.SubViewLibrary ?? new LibraryView());
                            }
                        });
                    }
                }
            }
        }

        private bool _isTestsPageSelected;
        public bool IsTestsPageSelected
        {
            get => _isTestsPageSelected;
            set
            {
                if (_isTestsPageSelected != value)
                {
                    _isTestsPageSelected = value;
                    OnPropertyChanged();

                    if (value)
                    {
                        IsMainPageSelected = false;
                        IsLibraryPageSelected = false;
                        IsChatBotPageSelected = false;

                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            if (MainPage != null)
                            {
                                MainPage.SetView(MainPage.SubViewTestCategories ?? new TestsView());
                            }
                        });
                    }
                }
            }
        }

        private bool _isChatBotPageSelected;
        public bool IsChatBotPageSelected
        {
            get => _isChatBotPageSelected;
            set
            {
                if (_isChatBotPageSelected != value)
                {
                    _isChatBotPageSelected = value;
                    OnPropertyChanged();

                    if (value)
                    {
                        IsMainPageSelected = false;
                        IsLibraryPageSelected = false;
                        IsTestsPageSelected = false;

                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            if (MainPage != null)
                            {
                                MainPage.SetView(MainPage.SubViewChatBot ?? new ChatBotView());
                            }
                        });
                    }
                }
            }
        }

        // Event handlers for tab taps
        private void OnHomeTapped(object? sender, TappedEventArgs e)
        {
            IsMainPageSelected = true;
        }

        private void OnLibraryTapped(object? sender, TappedEventArgs e)
        {
            IsLibraryPageSelected = true;
        }

        private void OnTestsTapped(object? sender, TappedEventArgs e)
        {
            IsTestsPageSelected = true;
        }

        private void OnChatBotTapped(object? sender, TappedEventArgs e)
        {
            IsChatBotPageSelected = true;
        }

        private void OnPlusTapped(object? sender, TappedEventArgs e)
        {
            // TODO: Implement mood tracker functionality
            // This was the original GoToMoodTracker command
            try
            {
                ServiceHelper.ToastMessage.ShortAlert("Mood Tracker - Coming Soon!");
                
                // TODO: MAUI Migration - Implement mood tracker navigation
                // Original Xamarin code:
                // Mobile.App.OpenPage(new TrackerStart());
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Plus button error: {ex.Message}");
            }
        }

        public void UnselectFooter()
        {
            IsMainPageSelected = false;
            IsLibraryPageSelected = false;
            IsTestsPageSelected = false;
            IsChatBotPageSelected = false;
        }

        // Commands for programmatic access
        public ICommand CommandSelectHome => new Command(() => IsMainPageSelected = true);
        public ICommand CommandSelectLibrary => new Command(() => IsLibraryPageSelected = true);
        public ICommand CommandSelectTests => new Command(() => IsTestsPageSelected = true);
        public ICommand CommandSelectChatBot => new Command(() => IsChatBotPageSelected = true);

        // Use inherited PropertyChanged from DrawnUi.Draw.Canvas
        protected new void OnPropertyChanged([System.Runtime.CompilerServices.CallerMemberName] string? propertyName = null)
        {
            base.OnPropertyChanged(propertyName);
        }
    }
}
