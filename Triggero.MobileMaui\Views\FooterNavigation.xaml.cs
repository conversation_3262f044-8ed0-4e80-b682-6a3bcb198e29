using System.Threading.Tasks;
using System.Windows.Input;
using Microsoft.Maui.Controls;
using DrawnUi.Maui.Views;

// TODO: MAUI Migration - Update these imports when available
// using Triggero.Mobile.Enums;
// using Triggero.Mobile.Extensions.Helpers;
// using Triggero.Mobile.Views;
// using Triggero.Mobile.Views.Pages;
// using Triggero.Mobile.Views.Pages.MoodTracker;

namespace Triggero.MobileMaui.Views
{
    public partial class FooterNavigation : DrawnUi.Maui.Views.Canvas
    {
        public FooterNavigation()
        {
            InitializeComponent();
        }

        // TODO: MAUI Migration - Update MainPage reference when MainPage is available
        public static readonly BindableProperty MainPageProperty =
              BindableProperty.Create(nameof(MainPage), typeof(object), typeof(FooterNavigation));
        public object MainPage
        {
            get { return GetValue(MainPageProperty); }
            set { SetValue(MainPageProperty, value); }
        }


        //public static readonly BindableProperty BottomOffsetProperty = BindableProperty.Create(nameof(BottomOffset),
        //typeof(double),
        //typeof(Footer),
        //0.0, propertyChanged: NeedApplyProperties);

        //private static void NeedApplyProperties(BindableObject bindable, object oldvalue, object newvalue)
        //{
        //    if (bindable is Footer control)
        //    {
        //        control.Update();
        //    }
        //}

        //public double BottomOffset
        //{
        //    get { return (double)GetValue(BottomOffsetProperty); }
        //    set { SetValue(BottomOffsetProperty, value); }
        //}


        public void UnselectFooter()
        {
            IsMainPageSelected = false;
            IsLibraryPageSelected = false;
            IsTestsPageSelected = false;
            IsChatBotPageSelected = false;
        }

        #region Выбор радиобаттонов

        public ICommand CommandSelectLibrary => new Command((ctx) =>
        {
            IsLibraryPageSelected = true;
        });

        public ICommand CommandSelectHome => new Command((ctx) =>
        {
            IsMainPageSelected = true;
        });

        public ICommand CommandSelectTests => new Command((ctx) =>
        {
            IsTestsPageSelected = true;
        });

        public ICommand CommandSelectChatBot => new Command((ctx) =>
        {
            IsChatBotPageSelected = true;
        });

        private bool isMainPageSelected;
        public bool IsMainPageSelected
        {
            get => isMainPageSelected;
            set
            {
                //if (value != isMainPageSelected)
                {
                    isMainPageSelected = value;

                    if (value)
                    {
                        IsTestsPageSelected = false;
                        IsChatBotPageSelected = false;
                        IsLibraryPageSelected = false;

                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            if (MainPage != null)
                            {
                                //go async baby
                                Tasks.StartDelayed(TimeSpan.FromMilliseconds(1), () =>
                                {
                                    MainThread.BeginInvokeOnMainThread(async () =>
                                    {
                                        await IconHome.Animate();
                                        //AnimateIcon(mainRb, mainBtnGrid, "footerMainAnimation.gif", App.This.Interface.MainPage.FooterMain, 19, 24);
                                    });
                                });

                                MainPage.SetView(MainPage.SubViewHome);
                            }

                        });

                    }
                    OnPropertyChanged();
                }
            }
        }

        private bool isLibraryPageSelected;
        public bool IsLibraryPageSelected
        {
            get => isLibraryPageSelected;
            set
            {
                //if (value != isLibraryPageSelected)
                {
                    isLibraryPageSelected = value;
                    OnPropertyChanged(nameof(IsLibraryPageSelected));

                    if (value)
                    {
                        IsChatBotPageSelected = false;
                        IsMainPageSelected = false;
                        IsTestsPageSelected = false;

                        var user = AuthHelper.User;

                        //go async baby
                        Tasks.StartDelayed(TimeSpan.FromMilliseconds(1), () =>
                        {
                            MainThread.BeginInvokeOnMainThread(async () =>
                            {
                                //AnimateIcon(libraryRb, libraryBtnGrid, "footerLibraryAnimation.gif", App.This.Interface.MainPage.FooterLibrary, 21, 24);
                                await IconLibrary.Animate();
                            });
                        });


                        MainThread.BeginInvokeOnMainThread(async () =>
                        {

                            bool needPay = false;

                            if (!AuthHelper.IsLibraryActivated)
                            {
                                needPay = Mobile.App.OpenNeedToPayNow();
                            }

                            if (!needPay)
                            {
                                if (MainPage.SubViewLibrary is null)
                                {
                                    MainPage.SubViewLibrary = new LibraryView() { LibrarySectionType = LibrarySectionType.Exercises };

                                    MainPage.LibraryGrid.Children.Add(MainPage.SubViewLibrary);
                                }

                                if (!MainPage.SubViewLibrary.IsRendered)
                                {
                                    await MainPage.SubViewLibrary.Render();
                                }

                                //MainPage.SubViewLibrary.LibrarySectionType = LibrarySectionType.Exercises;

                                MainPage.SetView(MainPage.SubViewLibrary);
                            }


                        });



                    }
                }

            }
        }

        private bool isTestsPageSelected;
        public bool IsTestsPageSelected
        {
            get => isTestsPageSelected;
            set
            {
                //if (value != isTestsPageSelected)
                {
                    isTestsPageSelected = value;
                    OnPropertyChanged(nameof(IsTestsPageSelected));

                    if (value)
                    {
                        IsChatBotPageSelected = false;
                        IsMainPageSelected = false;
                        IsLibraryPageSelected = false;

                        var user = AuthHelper.User;

                        //go async baby
                        Tasks.StartDelayed(TimeSpan.FromMilliseconds(1), () =>
                        {
                            MainThread.BeginInvokeOnMainThread(() =>
                            {
                                IconTests.Animate();
                                //AnimateIcon(testsRb, testsBtnGrid, "footerTestsAnimation.gif", App.This.Interface.MainPage.Tests, 19, 24);
                            });
                        });


                        MainThread.BeginInvokeOnMainThread(() =>
                        {

                            bool needPay = false;

                            if (!AuthHelper.IsTestsActivated)
                            {
                                needPay = Mobile.App.OpenNeedToPayNow();
                            }

                            if (!needPay)
                            {
                                if (MainPage.SubViewTestCategories is null)
                                {
                                    MainPage.SubViewTestCategories = new TestsView();
                                    MainPage.TestsGrid.Children.Add(MainPage.SubViewTestCategories);
                                }

                                if (!MainPage.SubViewTestCategories.IsRendered)
                                {
                                    MainPage.SubViewTestCategories.Render();
                                }

                                MainPage.SetView(MainPage.SubViewTestCategories);
                            }

                        });
                    }
                }
            }
        }

        private bool isChatBotPageSelected;
        public bool IsChatBotPageSelected
        {
            get => isChatBotPageSelected;
            set
            {
                //if (value != isChatBotPageSelected)
                {
                    isChatBotPageSelected = value;
                    OnPropertyChanged(nameof(IsChatBotPageSelected));

                    if (value)
                    {
                        IsTestsPageSelected = false;
                        IsMainPageSelected = false;
                        IsLibraryPageSelected = false;

                        var user = AuthHelper.User;

                        //go async baby
                        Tasks.StartDelayed(TimeSpan.FromMilliseconds(1), () =>
                        {
                            MainThread.BeginInvokeOnMainThread(() =>
                            {
                                //AnimateIcon(chatBotRb, chatBotBtnGrid, "footerChatBotAnimation.gif", App.This.Interface.MainPage.ChatBot, 19, 24);
                                IconChatBot.Animate();
                            });
                        });


                        MainThread.BeginInvokeOnMainThread(() =>
                        {
                            bool needPay = false;

                            if (!AuthHelper.IsChatBotActivated)
                            {
                                needPay = Mobile.App.OpenNeedToPayNow();
                            }

                            if (!needPay)
                            {
                                if (MainPage.SubViewChatBot is null)
                                {
                                    MainPage.SubViewChatBot = new ChatBotView();
                                    MainPage.ChatBotGrid.Children.Add(MainPage.SubViewChatBot);
                                }

                                if (!MainPage.SubViewChatBot.IsRendered)
                                {
                                    MainPage.SubViewChatBot.LoadChat();
                                }

                                MainPage.SetView(MainPage.SubViewChatBot);
                            }

                        });


                    }
                }

            }
        }

        #endregion

        private ICommand goToMoodTracker;
        public ICommand GoToMoodTracker
        {
            get => goToMoodTracker ??= new RelayCommand(async obj =>
            {

                //go async baby
                Tasks.StartDelayed(TimeSpan.FromMilliseconds(1), () =>
                {
                    MainThread.BeginInvokeOnMainThread(async () =>
                    {
                        await IconPlus.Animate();
                    });
                });

                MainThread.BeginInvokeOnMainThread(() =>
                  {
                      bool needPay = false;

                      if (!AuthHelper.IsLibraryActivated)
                      {
                          needPay = Mobile.App.OpenNeedToPayNow();
                      }

                      if (!needPay)
                      {
                          Mobile.App.OpenPage(new TrackerStart());
                          // await App.Current.MainPage.Navigation.PushPopupAsync(new TrackerStartPopup());
                      }

                  });

            });
        }


        #region Private methods

        private double GetMicroFontSize()
        {
            if (Device.RuntimePlatform == Device.Android)
                return 10;
            else if (Device.RuntimePlatform == Device.iOS)
                return 12;
            return 10;
        }

        private async void AnimateIcon(View rb, Grid layout, string gifPath, string text, double width, double height, bool hasText = true)
        {
            var stackLayout = new StackLayout
            {
                VerticalOptions = rb.VerticalOptions,
                HorizontalOptions = rb.HorizontalOptions,
                Margin = rb.Margin
            };
            var animationImage = new SvgCachedImage
            {
                Source = ImageSource.FromFile(gifPath),
                WidthRequest = width,
                HeightRequest = height,
                HorizontalOptions = LayoutOptions.Center,
                VerticalOptions = LayoutOptions.Start
            };
            stackLayout.Children.Add(animationImage);


            if (hasText)
            {
                var label = new Label
                {
                    Text = text,
                    TextColor = Color.FromHex("#10ABB8"),
                    HorizontalOptions = LayoutOptions.Center,
                    HorizontalTextAlignment = TextAlignment.Center,
                    FontSize = GetMicroFontSize()
                };
                stackLayout.Children.Add(label);
            }




            await Task.Delay(100);
            //Если не отрендерился, то нихуя не анимируем
            if (rb.Height > 0)
            {
                var margin = rb.Margin;
                rb.Margin = new Thickness(200, 200, 0, 0);
                layout.Children.Add(stackLayout);

                await Task.Delay(1800);

                layout.Children.Remove(stackLayout);
                rb.Margin = margin;
            }




        }
        #endregion

    }
}