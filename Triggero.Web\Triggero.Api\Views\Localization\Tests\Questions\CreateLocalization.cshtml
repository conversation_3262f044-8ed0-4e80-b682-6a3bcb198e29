﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Localization
@using Triggero.Models.Localization.Practices
@using Triggero.Models.Localization.Tests;
@using Triggero.Models.Localization.Tests.Questions;
@using Triggero.Models.Tests.QuestionOptions;
@using Triggero.Web.ViewModels.Localization
@using Triggero.Web.ViewModels.Localization.Tests;
@{
    ViewData["Title"] = $"СОЗДАНИЕ ПЕРЕВОДА ДЛЯ ВОПРОСА \"{Model.Question.Text}\"";
    ViewData["ShowLang"] = true;
}
@model TestQuestionLocalizationVM

 <!-- CONTENT -->
<form asp-action="CreateQuestionLocalizationPOST" asp-controller="TestQuestionsLocalization" method="post" enctype="multipart/form-data" class="content">

    <input type="hidden" asp-for="Id" value="@Model.Id" />

    <div class="row">
        <div class="page">
            
            <div class="mb-3">
                <label for="" class="formlabel">Текст</label>
                <textarea class="form-control" required asp-for="Localization.Text" rows="3" placeholder=""></textarea>
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>

            @if (Model.Question.Options.Any(o => o is SimpleQuestionOption))
            {
                <table class="table" id="tablemanager">
                    <thead class="tableheader">
                        <tr>
                            <th scope="col">Вариант ответа</th>
                            <th scope="col">Языки</th>
                            <th scope="col">Действия</th>
                        </tr>
                    </thead>
                    <tbody>

                        @foreach (var item in Model.Question.Options.Where(o => o is SimpleQuestionOption).Cast<SimpleQuestionOption>())
                        {
                            <tr>
                                <td>@item.Text</td>
                                <td>Русский @string.Join(' ',item.Localizations.Select(o => o.Language).Select(o => o.Title))</td>
                                <td>
                                    <div class="interect">
                                        @if (item.Localizations.Any(o => o.LanguageId == Model.CurrentLanguage.Id))
                                        {
                                            <a asp-action="UpdateSimpleOptionLocalization" asp-controller="TestOptionsLocalization" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                        }
                                        else
                                        {
                                            <a asp-action="CreateSimpleOptionLocalization" asp-controller="TestOptionsLocalization" asp-route-id="@item.Id" class="edit"><i class="fa-solid fa-pencil"></i></a>
                                        }
                                    </div>
                                </td>
                            </tr>
                        }
                    </tbody>
                </table>


                <script>
                    $(document).ready(function () {
                        $('.table').DataTable();
                    });
                </script>
            }

        </div>
    </div>
</form>