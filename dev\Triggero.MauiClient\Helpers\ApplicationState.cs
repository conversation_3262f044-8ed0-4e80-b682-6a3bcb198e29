﻿using AppoMobi.Specials;
using Newtonsoft.Json;
using System.Diagnostics;
using System.IO;
using System.Threading.Tasks;
using Triggero.MauiClient.Helpers.Modules;
using Triggero.MauiClient.Helpers.Modules.Pools;

namespace Triggero.MauiClient.Helpers
{
    public static class ApplicationState
    {

        public static string DownloadsPath = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + @"/downloads";

        public static UserFavorites UserFavorites { get; set; } = new UserFavorites();
        public static SavedData Data { get; set; } = new SavedData();
        public static ConfigData ConfigData { get; set; } = new ConfigData();
        public static TodayData TodayData { get; set; } = new TodayData();
        public static MessengersData Messengers { get; set; } = new MessengersData();

        public static ChatBotData ChatBotData { get; set; } = new ChatBotData();


        public static PostponedRequests PostponedRequests { get; set; } = new PostponedRequests();

        public static async Task<bool> LoadData()
        {
            try
            {
                if (!ConnectionHelper.HasInternet())
                {
                    Data = GetFromFile<SavedData>(SavedData.FilePath);
                    Messengers = GetFromFile<MessengersData>(MessengersData.FilePath);
                    ChatBotData = GetFromFile<ChatBotData>(ChatBotData.FilePath);
                }

                //ConfigData = GetFromFile<ConfigData>(ConfigData.FilePath); //грузится в App.xaml.cs
                TodayData = GetFromFile<TodayData>(TodayData.FilePath);
                PostponedRequests = GetFromFile<PostponedRequests>(PostponedRequests.FilePath);

                AuthHelper.Login(ConfigData.User, ConfigData.AuthToken);

                if (!AuthHelper.IsAuthorized)
                {
                    return false;
                }

                //refresh
                await AuthHelper.ReloadUser();

                Tasks.StartDelayed(TimeSpan.FromMilliseconds(50), async () =>
                {
                    try
                    {
                        Debug.WriteLine("*** Loading favorites..");

                        await UserFavorites.LoadFavorites();

                        //var favs = await TriggeroMobileAPI.GeneralMethods.UserMethods.UserFavoritesMethods.GetUserFavoritesAll(AppUser.Id);
                        //UserFavorites.SetFavoriteExercises(favs.Exercises);
                        //UserFavorites.SetFavoriteTest(favs.Tests);
                        //UserFavorites.SetFavoriteTopics(favs.Topics);
                        //UserFavorites.SetFavoritePractices(favs.Practices);

                        Debug.WriteLine("*** Favorites loaded!");
                    }
                    catch (Exception e)
                    {
                        Super.Log(e);
                    }
                });

                return true;
            }
            catch (Exception e)
            {
                Super.Log(e);

                AuthHelper.UserId = 0;
            }

            return false;
        }

        private static bool savingBusy;
        public static void SaveChangesToMemory()
        {
            savingBusy = true;
            Task.Run(async () =>
            {

                try
                {
                    SaveToFile(Data, SavedData.FilePath);
                    SaveToFile(ConfigData, ConfigData.FilePath);
                    SaveToFile(TodayData, TodayData.FilePath);
                    SaveToFile(Messengers, MessengersData.FilePath);
                    SaveToFile(ChatBotData, ChatBotData.FilePath);
                    SaveToFile(PostponedRequests, PostponedRequests.FilePath);
                }
                catch (Exception e)
                {
                    Console.WriteLine(e);
                    throw;
                }
                finally
                {
                    savingBusy = false;
                }

            }).ConfigureAwait(false);
        }


        public static T GetFromFile<T>(string filepath) where T : class, new()
        {
            T obj = null;
            if (File.Exists(filepath))
            {
                using (StreamReader file = File.OpenText(filepath))
                {
                    try
                    {
                        var json = file.ReadToEnd();
                        obj = JsonConvert.DeserializeObject<T>(json);

                    }
                    catch { }
                }
            }
            if (obj is null) obj = new T();
            return obj;
        }
        public static void SaveToFile(object file, string filepath)
        {
            try
            {
                var json = JsonConvert.SerializeObject(file);
                File.WriteAllText(filepath, json);
            }
            catch (Exception e)
            {
                Super.Log(e);
            }
        }
    }
}
