﻿using System.Collections.Generic;

namespace CRMWeb.Helpers.Html
{
    public class ClassesToHtmlHelper<T> where T : class
    {
        public static string Convert(List<T> list,T item)
        {
            string html = "";

            foreach (var value in list)
            {
                int id = (int)value.GetType().GetProperty("Id").GetValue(value);
                if (item == value)
                {
                    html += $"<option selected value=\"{id}\">{value}</option>\r\n";
                }
                else
                {
                    html += $"<option value=\"{id}\">{value}</option>\r\n";
                }
            }
            return html;
        }
        public static string Convert(List<T> list, int? itemId)
        {
            string html = "";

            foreach (var value in list)
            {
                int id = (int)value.GetType().GetProperty("Id").GetValue(value);
                if (itemId == id)
                {
                    html += $"<option selected value=\"{id}\">{value}</option>\r\n";
                }
                else
                {
                    html += $"<option value=\"{id}\">{value}</option>\r\n";
                }
            }
            return html;
        }
        public static string ConvertExcluding(List<T> list, int? itemId,int exlcludingId)
        {
            string html = "";

            foreach (var value in list)
            {
                int id = (int)value.GetType().GetProperty("Id").GetValue(value);
                if (exlcludingId == id)
                {
                    continue;
                }
                else if (itemId == id)
                {
                    html += $"<option selected value=\"{id}\">{value}</option>\r\n";
                }
                else
                {
                    html += $"<option value=\"{id}\">{value}</option>\r\n";
                }
            }
            return html;
        }
        public static string ConvertExcluding(List<T> list, int exlcludingId)
        {
            string html = "";

            foreach (var value in list)
            {
                int id = (int)value.GetType().GetProperty("Id").GetValue(value);
                if (exlcludingId == id)
                {
                    continue;
                }
                else
                {
                    html += $"<option value=\"{id}\">{value}</option>\r\n";
                }
            }
            return html;
        }
        public static string Convert(List<T> list)
        {
            string html = "";

            foreach (var value in list)
            {
                int id = (int)value.GetType().GetProperty("Id").GetValue(value);
                html += $"<option value=\"{id}\">{value}</option>\r\n";
            }
            return html;
        }
    }
}
