{
  "ConnectionStrings": {
    "Local": "server=localhost;user=api;password=XXXXXXXX;port=3306;database=triggero;Convert Zero Datetime=true;",
    "Legacy": "server=************;user=root;password=********;port=3306;database=triggero;Convert Zero Datetime=true;",
    },
  "Jwt": {
    "Key": "TokenSeedNiceGuy!tyjYes75ughGahJoy",
    "Issuer": "Api"
  },
  "Files": {
    "ImagesRoot": "uploads",
    "FilesRoot": "files"
  },
  "Logging": {
    "LogLevel": {
      "Default": "Warning",
      "Microsoft.AspNetCore": "Warning"
    }
  }
}
