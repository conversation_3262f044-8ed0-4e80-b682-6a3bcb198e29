using Android.App;
using Android.Content;
using Android.Graphics;
using Android.OS;
using Android.Views;
using AndroidX.Core.View;
using DrawnUi.Models;
using Microsoft.Maui.Platform;
using Triggero.Mobile.Abstractions;

namespace Triggero.MobileMaui.Platforms.Android
{
    public class PlatformUi : IPlatformUi
    {
        public PlatformUi()
        {
            Instance = this;
        }

        public bool OpenUrl(string url)
        {
            try
            {
                var uri = global::Android.Net.Uri.Parse(url);
                var intent = new Intent(Intent.ActionView, uri);
                intent.SetFlags(ActivityFlags.NewTask);
                Platform.CurrentActivity?.StartActivity(intent);
                return true;
            }
            catch (Exception e)
            {
                System.Diagnostics.Debug.WriteLine($"[PlatformUi] OpenUrl failed: {e.Message}");
                return false;
            }
        }

        public void Command(string command)
        {
            // TODO: Xamarin code had specific command handling
            // Original command handling was empty in Android version
        }

        public Activity? Activity => Platform.CurrentActivity as Activity;

        public static PlatformUi? Instance { get; set; }

        public Screen Screen { get; } = new();

        public void ApplyTheme()
        {
            if (Activity?.Window == null) return;

            try
            {
                // TODO: Xamarin code - needs adaptation for MAUI
                // Original code handled status bar styling and navigation bar colors
                if (Build.VERSION.SdkInt > BuildVersionCodes.M)
                {
                    var flags = Activity.Window.DecorView.SystemUiVisibility;
                    Activity.Window.DecorView.SystemUiVisibility = flags | (StatusBarVisibility)SystemUiFlags.LightStatusBar;
                }

                SetNavigationBarColor(Colors.White, Colors.White, false);
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PlatformUi] ApplyTheme failed: {ex.Message}");
            }
        }

        public void Init(params object[] args)
        {
            try
            {
                if (args.Length > 0 && args[0] is Activity activity)
                {
                    // TODO: Store activity reference if needed
                    // Activity property is read-only, need different approach
                }

                // Initialize screen properties
                var displayMetrics = global::Android.App.Application.Context.Resources?.DisplayMetrics;
                if (displayMetrics != null)
                {
                    Screen.Density = displayMetrics.Density;
                    Screen.WidthDip = displayMetrics.WidthPixels / displayMetrics.Density;
                    Screen.HeightDip = displayMetrics.HeightPixels / displayMetrics.Density;
                }

                // TODO: Xamarin code - calculate safe area insets
                // Original code calculated insets for notches and navigation bars
                CalculateSafeAreaInsets();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PlatformUi] Init failed: {ex.Message}");
            }
        }

        private void CalculateSafeAreaInsets()
        {
            try
            {
                if (Activity?.Window?.DecorView == null) return;

                if (Build.VERSION.SdkInt >= BuildVersionCodes.R)
                {
                    var windowInsets = Activity.Window.DecorView.RootWindowInsets;
                    if (windowInsets != null)
                    {
                        var insets = windowInsets.GetInsets(WindowInsetsCompat.Type.SystemBars());
                        Screen.TopInset = insets.Top;
                        Screen.BottomInset = insets.Bottom;
                        Screen.LeftInset = insets.Left;
                        Screen.RightInset = insets.Right;
                    }
                }
                else
                {
                    // Fallback for older Android versions
                    // TODO: Xamarin code had more complex inset calculation
                    Screen.TopInset = 24; // Default status bar height
                    Screen.BottomInset = 0;
                    Screen.LeftInset = 0;
                    Screen.RightInset = 0;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PlatformUi] CalculateSafeAreaInsets failed: {ex.Message}");
            }
        }

        public void SetNavigationBarColor(Microsoft.Maui.Graphics.Color colorBar, Microsoft.Maui.Graphics.Color colorSeparator, bool isLight)
        {
            try
            {
                if (Activity?.Window == null) return;

                // MAUI Colors with .ToPlatform() extension
                if (Build.VERSION.SdkInt >= BuildVersionCodes.Lollipop)
                {
                    Activity.Window.SetNavigationBarColor(colorBar.ToPlatform());

                    if (Build.VERSION.SdkInt >= BuildVersionCodes.P)
                    {
                        Activity.Window.NavigationBarDividerColor = colorSeparator.ToPlatform();
                    }

                    if (Build.VERSION.SdkInt >= BuildVersionCodes.O)
                    {
                        var flags = Activity.Window.DecorView.SystemUiVisibility;
                        if (isLight)
                        {
                            Activity.Window.DecorView.SystemUiVisibility = flags | (StatusBarVisibility)SystemUiFlags.LightNavigationBar;
                        }
                        else
                        {
                            Activity.Window.DecorView.SystemUiVisibility = flags & ~(StatusBarVisibility)SystemUiFlags.LightNavigationBar;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"[PlatformUi] SetNavigationBarColor failed: {ex.Message}");
            }
        }

        public void HideStatusBar()
        {
            // TODO: Xamarin code was empty for Android
            // Status bar hiding might need implementation
        }

        public void ShowStatusBar()
        {
            // TODO: Xamarin code was empty for Android  
            // Status bar showing might need implementation
        }
    }
}
