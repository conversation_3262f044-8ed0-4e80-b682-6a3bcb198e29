﻿using Newtonsoft.Json;
using Odintcovo.API.Helpers;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Models;
using Triggero.Models.Enums;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;

namespace MobileAPIWrapper.Methods.Library
{
    public class TopicsMethods
    {
        public static string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("Topics/");


        public async Task<List<TopicCategory>> GetTopicCategories()
        {
            string url = BASE_HOST + "GetTopicCategories";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<TopicCategory>>(response.Content);
            return obj;
        }


        public async Task<List<Topic>> GetTopics()
        {
            string url = BASE_HOST + "GetTopics";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Topic>>(response.Content);
            return obj;
        }
        public async Task<List<Topic>> GetTopicsChunk(int count, int offset)
        {
            string url = BASE_HOST + $"GetTopics?count={count}&offset={offset}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Topic>>(response.Content);
            return obj;
        }








        public async Task<List<Topic>> GetTopicsByCategory(int categoryId)
        {
            string url = BASE_HOST + $"GetTopicsByCategory?categoryId={categoryId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Topic>>(response.Content);
            return obj;
        }
        public async Task<List<Topic>> GetTopicsByCategoryChunk(int categoryId, int count, int offset)
        {
            string url = BASE_HOST + $"GetTopicsByCategoryChunk?categoryId={categoryId}&count={count}&offset={offset}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Topic>>(response.Content);
            return obj;
        }







        public async Task<List<Topic>> GetTopicsByTag(string tag)
        {
            string url = BASE_HOST + $"GetTopicsByTag?tag={tag}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<Topic>>(response.Content);
            return obj;
        }





        public async Task<Topic> GetTopic(int id)
        {
            string url = BASE_HOST + $"GetTopic?id={id}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<Topic>(response.Content);
            return obj;
        }


        #region Лайк/Дизлайк, просмотры

        public async Task<RateType> GetRate(int userId, int topicId)
        {
            string url = BASE_HOST + $"GetRate?userId={userId}&topicId={topicId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<RateType>(response.Content);
            return obj;
        }
        public async Task<bool> ToggleRate(int userId, int topicId, RateType rateType)
        {
            string url = BASE_HOST + $"ToggleRate?userId={userId}&topicId={topicId}&rateType={(int)rateType}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }
        public async Task AddWatch(int topicId)
        {
            string url = BASE_HOST + $"AddWatch?topicId={topicId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);
        }

        #endregion
    }
}
