﻿using MobileAPIWrapper;
using Newtonsoft.Json;
using System.IO;
using System.Threading.Tasks;
using Triggero.Domain.Models;
using Triggero.MauiClient.Helpers;
using Triggero.Models.Messengers.ChatBot;
using Triggero.Models.Messengers.Support;
using Xamarin.Essentials;
using Xamarin.Forms;

namespace Triggero.MauiClient.Helpers.Modules
{
    public class MessengersData
    {
        public static string FilePath = Environment.GetFolderPath(Environment.SpecialFolder.Personal) + @"/messengersData.json";
        public ChatBotChat ChatBotChat { get; set; }
        public SupportChat SupportChat { get; set; }


        #region Get chats
        public async Task<ChatBotChat> GetChatBotChat()
        {
            if (Connectivity.NetworkAccess == NetworkAccess.Internet)
            {
                ChatBotChat = await TriggeroMobileAPI.MessengersMethods.ChatBotMessengerMethods.GetChat(AuthHelper.UserId);
                SaveChangesToMemory();
            }
            return ChatBotChat;
        }
        public async Task<SupportChat> GetSupportChat()
        {
            if (Connectivity.NetworkAccess == NetworkAccess.Internet)
            {
                SupportChat = await TriggeroMobileAPI.MessengersMethods.SupportMessengerMethods.GetChat(AuthHelper.UserId);
                SaveChangesToMemory();
            }
            return SupportChat;
        }
        #endregion

        #region Send messages
        public async Task<bool> SendChatBotMessage(ChatBotMessage message)
        {
            if (Connectivity.NetworkAccess != NetworkAccess.Internet) return false;

            await TriggeroMobileAPI.MessengersMethods.ChatBotMessengerMethods.SendMessage(AuthHelper.UserId, message);
            ChatBotChat.Messages.Add(message);

            SaveChangesToMemory();
            return true;
        }
        public async Task<bool> SendSupportMessage(SupportChatMessage message)
        {
            if (Connectivity.NetworkAccess != NetworkAccess.Internet) return false;

            await TriggeroMobileAPI.MessengersMethods.SupportMessengerMethods.SendMessage(AuthHelper.UserId, message);
            SupportChat.Messages.Add(message);

            SaveChangesToMemory();
            return true;
        }
        #endregion

        #region Chat bot long polling
        private int chatBotPollingId = -1;
        private int chatBotMessagesCount;
        public async Task StartChatBotLongPolling(int messagesCount)
        {
            await Task.Run(() =>
            {
                chatBotPollingId = new Random().Next(1, 3333333);
                chatBotMessagesCount = messagesCount;

                int currentPollingId = chatBotPollingId;

                Device.StartTimer(TimeSpan.FromSeconds(5), () =>
                {
                    if (chatBotPollingId != currentPollingId) return false; //Лонг полл отключен
                    if (Connectivity.NetworkAccess != NetworkAccess.Internet) return false; //Нет инета, вырубаем


                    ChatBotLongPollTick();

                    return true;
                });

            });
        }

        private async Task ChatBotLongPollTick()
        {
            await Task.Run(async () =>
            {
                var res = await TriggeroMobileAPI.MessengersMethods.ChatBotMessengerMethods.LongPollingV2(AuthHelper.UserId, chatBotMessagesCount);
                chatBotMessagesCount = res.TotalMessagesCount;
                MainThread.BeginInvokeOnMainThread(() => { OnChatBotLongPollingGotResponse(res); });
            });
        }

        public void StopChatBotLongPolling()
        {
            chatBotPollingId = -1;
            chatBotMessagesCount = 0;
        }


        public event EventHandler<ChatBotLongPollItem> ChatBotLongPollingGotResponse;
        private void OnChatBotLongPollingGotResponse(ChatBotLongPollItem item)
        {
            ChatBotLongPollingGotResponse?.Invoke(ChatBotChat, item);
        }

        #endregion

        #region Support chat long polling
        private int supportPollingId = -1;
        private int supportMessagesCount;
        public async Task StartSupportLongPolling(int messagesCount)
        {
            await Task.Run(() =>
            {
                supportPollingId = new Random().Next(1, 3333333);
                supportMessagesCount = messagesCount;

                int currentPollingId = supportPollingId;

                Device.StartTimer(TimeSpan.FromSeconds(5), () =>
                {
                    if (supportPollingId != currentPollingId) return false; //Лонг полл отключен
                    if (Connectivity.NetworkAccess != NetworkAccess.Internet) return false; //Нет инета, вырубаем


                    SupportLongPollTick();

                    return true;
                });

            });
        }
        private async Task SupportLongPollTick()
        {
            await Task.Run(async () =>
            {
                var res = await TriggeroMobileAPI.MessengersMethods.SupportMessengerMethods.LongPollingV2(AuthHelper.UserId, supportMessagesCount);
                if (res != null)
                {
                    supportMessagesCount = res.TotalMessagesCount;
                    MainThread.BeginInvokeOnMainThread(() => { OnSupportLongPollingGotResponse(res); });
                }

            });
        }


        public void StopSupportLongPolling()
        {
            supportPollingId = -1;
        }

        public event EventHandler<SupportChatLongPollItem> SupportLongPollingGotResponse;
        private void OnSupportLongPollingGotResponse(SupportChatLongPollItem item)
        {
            SupportLongPollingGotResponse?.Invoke(SupportChat, item);
        }

        #endregion

        public void SaveChangesToMemory()
        {
            try
            {
                var json = JsonConvert.SerializeObject(this);
                File.WriteAllText(FilePath, json);
            }
            catch { }
        }
    }
}
