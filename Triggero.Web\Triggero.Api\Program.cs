using AppoMobi.Specials;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.FileProviders;
using Microsoft.IdentityModel.Tokens;
using System.Reflection;
using System.Text;
using Triggero.Application.Abstractions;
using Triggero.Application.Extensions;
using Triggero.Application.Services;
using Triggero.Database;
using Triggero.Domain.Extensions;
using Triggero.Domain.Jobs;
using Triggero.Web.Controllers;


namespace Triggero.Api
{
    public class Program
    {

        static async Task Main(string[] args)
        {
            var builder = WebApplication.CreateBuilder(new WebApplicationOptions
            {
                ContentRootPath = Path.GetDirectoryName(Assembly.GetEntryAssembly().Location),
                Args = args
            });

            Console.WriteLine($"Started at {AppContext.BaseDirectory} env: {builder.Environment.EnvironmentName}");

            var configuration = new ConfigurationBuilder()
                .SetBasePath(AppContext.BaseDirectory)
                .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
                .AddJsonFile("secrets.json", optional: false, reloadOnChange: true)
                .AddJsonFile($"appsettings.{builder.Environment.EnvironmentName}.json", optional: true, reloadOnChange: true)
                .AddEnvironmentVariables()
                .Build();

            builder.Configuration.AddConfiguration(configuration);
            builder.Services.AddApplication();

            builder.Services.AddHttpContextAccessor();

            //CACHE
            builder.Services.AddMemoryCache(options =>
            {
                options.SizeLimit = 1024 * 1024 * 250; // MB
                options.ExpirationScanFrequency = TimeSpan.FromMinutes(10);
            });

            // API
            builder.Services.AddControllers(options =>
            {
                options.Conventions.Add(new ApiExplorerConvention());
            });

            // WEB
            builder.Services.AddControllersWithViews();
            //.AddApplicationPart(typeof(HomeController).Assembly) // Add controllers from the external assembly
            //.AddRazorRuntimeCompilation(options =>
            //{
            //    var assembly = typeof(HomeController).Assembly;
            //    var fileProvider = new EmbeddedFileProvider(assembly);
            //    options.FileProviders.Add(fileProvider);
            //});

            // Add Razor Pages from the external assembly
            builder.Services.AddRazorPages()
                .AddApplicationPart(typeof(HomeController).Assembly)
                .AddRazorRuntimeCompilation(options =>
                {
                    var assembly = typeof(HomeController).Assembly;
                    var fileProvider = new EmbeddedFileProvider(assembly);
                    options.FileProviders.Add(fileProvider);
                });

            builder.Services.AddSwaggerForApi();

            builder.Services.AddResponseCaching();

            #region DATABASE

#if DEBUG
            var connectionString = Secrets.ConnectionString;
            //var connectionString = configuration["ConnectionStrings:Local"];
#else
            var connectionString = Secrets.ConnectionString;
#endif

            builder.Services.AddScoped<DatabaseMigrator>();
            builder.Services.AddDbContext<DatabaseContext>(options =>
                options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString))
#if DEBUG
                .EnableSensitiveDataLogging()  // Enable sensitive data logging for debugging
                .LogTo(Console.WriteLine, LogLevel.Information));  // Log EF Core operations to console           
#else
            );
#endif

            #endregion

            #region AUTH

            builder.Services.AddTransient<ISmsService, SmsService>();

            builder.Services.AddTransient<IAccountManagerService, IdentityService>();

            builder.Services.AddAuthentication(options =>
                {
                    options.DefaultAuthenticateScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                    options.DefaultChallengeScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                    options.DefaultScheme = CookieAuthenticationDefaults.AuthenticationScheme; // Ensure the cookie scheme is used by default
                })
                .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options =>
                {
                    options.LoginPath = new Microsoft.AspNetCore.Http.PathString("/Admin/Login");
                    options.AccessDeniedPath = new Microsoft.AspNetCore.Http.PathString("/Admin/Login");
                })
                .AddJwtBearer(JwtBearerDefaults.AuthenticationScheme, options =>
                {
                    options.SaveToken = true;
                    options.TokenValidationParameters = new TokenValidationParameters()
                    {
                        ValidateIssuer = true,
                        ValidateAudience = true,
                        ValidateLifetime = true,
                        ValidateIssuerSigningKey = true,
                        ValidIssuer = configuration["Jwt:Issuer"],
                        ValidAudience = configuration["Jwt:Issuer"],
                        IssuerSigningKey = new SymmetricSecurityKey(Encoding.UTF8.GetBytes(configuration["Jwt:Key"]))
                    };
                });


            builder.Services.AddAuthorization(options =>
            {
                options.AddPolicy("TokenOnly", policy =>
                {
                    policy.AuthenticationSchemes.Add(JwtBearerDefaults.AuthenticationScheme);
                    policy.RequireAuthenticatedUser();
                });
            });

            #endregion

            #region BACKGROUND TASKS

            //builder.Services.AddSingleton<IBackgroundTaskQueue, BackgroundTaskQueue>();
            //builder.Services.AddHostedService<AdminBackgroundService>();

            #endregion

            var app = builder.Build();

            app.UseWhen(context => context.Request.Path.StartsWithSegments("/admin"), appBuilder =>
            {
                if (app.Environment.IsDevelopment())
                {
                    app.UseDeveloperExceptionPage();
                }
                else
                {
                    app.UseExceptionHandler("/Admin/Home/Error");
                }
            });

            if (!app.Environment.IsDevelopment())
            {
                app.UseHsts();
            }

            app.UseHttpsRedirection();
            app.UseStaticFiles();

            // Serve static files from the web project's wwwroot folder
            var webProjectRoot = Path.Combine(
                Path.GetDirectoryName(Assembly.GetEntryAssembly().Location),
                "wwwroot");
            if (Directory.Exists(webProjectRoot))
            {
                app.UseStaticFiles(new StaticFileOptions
                {
                    FileProvider = new PhysicalFileProvider(webProjectRoot),
                    RequestPath = "/admin"
                });
            }

            app.Use(async (context, next) =>
            {
                if (context.Request.Path == "/")
                {
                    context.Response.Redirect("/admin");
                    return;
                }
                await next();
            });

            // Use the custom middleware for debugging
#if DEBUG
            app.UseMiddleware<TokenDebugMiddleware>();
#endif

            app.UseRouting();
            app.UseAuthentication();
            app.UseAuthorization();


            // Enable middleware to serve generated Swagger as a JSON endpoint.
            app.UseSwagger();
            app.UseSwaggerUI(c =>
            {
                c.SwaggerEndpoint("/swagger/v1/swagger.json", "Triggero v1.1");
                c.RoutePrefix = "api-help";
            });

            app.UseResponseCaching();

            // Map API controllers
            app.MapControllers();

            // Map web controllers with /Admin prefix
            app.MapControllerRoute(
                name: "admin",
                pattern: "Admin/{controller=Home}/{action=Index}/{id?}"
            );

            //app.MapRazorPages();

            // Migrate and populate database once at startup
            using (var scope = app.Services.CreateScope())
            {
                var hosting = scope.ServiceProvider.GetRequiredService<IWebHostEnvironment>();
                if (string.IsNullOrWhiteSpace(hosting.WebRootPath))
                {
                    hosting.WebRootPath = Path.Combine(Directory.GetCurrentDirectory(), "wwwroot");
                }
                Console.WriteLine($"Hosting info: www {hosting.WebRootPath}");

                var seeder = scope.ServiceProvider.GetRequiredService<DatabaseMigrator>();
                await seeder.PrepareDatabase();
            }

            //todo enable after windows is off!!!
            if (!app.Environment.IsDevelopment())
            {
                Tasks.StartDelayed(TimeSpan.FromSeconds(2), async () =>
                {
                    try
                    {

                        await AutoPaymentJob.StartJob(app.Services);

                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }

                });

                Tasks.StartDelayed(TimeSpan.FromSeconds(3), async () =>
                {
                    try
                    {

                        await PushNotificationJob.StartJob();
                    }
                    catch (Exception e)
                    {
                        Console.WriteLine(e);
                    }

                });

            }

            Console.WriteLine($"Started.");

            await app.RunAsync();
        }
    }
}
