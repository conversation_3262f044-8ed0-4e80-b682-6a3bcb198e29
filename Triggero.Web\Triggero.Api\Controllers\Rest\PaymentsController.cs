﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Triggero.Database;
using Triggero.Domain.Abstractions;
using Triggero.Domain.Models;
using Triggero.Domain.Models.Enums;
using Triggero.Models.Enums;
using Triggero.Models.General;
using Triggero.Models.General.UserData;
using Yandex.Checkout.V3;
using Message = Yandex.Checkout.V3.Message;

namespace Triggero.Domain.Controllers.Rest
{
    [ApiController]
    [Route("[controller]")]
    public class PaymentsController : ApiController
    {





        private readonly IHttpContextAccessor httpContextAccessor;
        private readonly IConfiguration _configuration;
        private readonly Client _clientUKassa;

        public PaymentsController(
            IConfiguration config, //for secrets
            DatabaseContext db, IHttpContextAccessor accessor) : base(db)
        {
            httpContextAccessor = accessor;
            _configuration = config;

            _clientUKassa = new Yandex.Checkout.V3.Client(
                shopId: _configuration["Secrets:UKassa:ShopId"],
                secretKey: _configuration["Secrets:UKassa:Key"]);
        }

        [Route("PaySubscription"), HttpPost]
        public async Task<CreatedPaymentModel> PaySubscription(int userID, SubscriptionPaymentSettings settings)
        {

            return await PaySubscription(DB, userID, settings);
        }

        protected async Task<CreatedPaymentModel> PaySubscription(
            DatabaseContext context,
            int userID,
            SubscriptionPaymentSettings settings)
        {
            try
            {
                var user = context.Users.Include(o => o.UserSubscription)
                             .FirstOrDefault(o => o.Id == userID);


                decimal sum = 0;
                var plan = context.Plans.FirstOrDefault(o => o.BuiltInPlanType == settings.Duration);

                switch (settings.SubType)
                {
                    case SubscriptionType.Trial:
                    if (settings.IsBindingPayment)
                    {
                        sum = 1;
                    }
                    else
                    {
                        sum = (decimal)plan.TotalPrice;
                    }
                    break;

                    case SubscriptionType.Full:
                    sum = (decimal)plan.TotalPrice;
                    break;

                    case SubscriptionType.Custom:

                    decimal sumPerMonth = 0;

                    foreach (var optionId in settings.planOptionIds)
                    {
                        var option = context.PlanOptions.FirstOrDefault(o => o.Id == optionId);
                        sumPerMonth += (decimal)option.Price;
                    }

                    if (settings.Duration == BuiltInPlanType.Year)
                    {
                        sumPerMonth = sumPerMonth * 12;
                    }
                    else
                    if (settings.Duration == BuiltInPlanType.ThreeMonths)
                    {
                        sumPerMonth = sumPerMonth * 3;
                    }

                    sumPerMonth -= sumPerMonth / 100m * (decimal)plan.Discount;

                    sum = sumPerMonth;

                    break;
                }

                var userPayment = new UserPayment()
                {
                    Description = "",
                    IsBindingPayment = settings.IsBindingPayment,
                    Sum = sum,
                    SubscriptionSettingsJson = JsonConvert.SerializeObject(settings),
                    PaymentId = "",
                    UserId = userID
                };

                var createdPayment = new CreatedPaymentModel();

                if (settings.PaymentMethod == PaymentMethodEnum.Yookassa)
                {

                    var newPayment = new NewPayment
                    {
                        Amount = new Amount { Value = sum, Currency = "RUB" },
                        Confirmation = new Confirmation
                        {
                            Type = ConfirmationType.Redirect,
                            ReturnUrl = $"https://triggero.ru",
                        },
                        Capture = true,
                        SavePaymentMethod = true,

                        //  Description = "Подписка в приложении Triggero",

                        Receipt = new Receipt
                        {
                            Customer = new Customer
                            {
                                Email = user.NormalizeEmail(user.Email),
                                Phone = user.Phone.Trim(), ///+79143996306
                                //FullName = $"Иванов {user.Name} Иванович"
                            },
                            Items = new List<ReceiptItem>
                            {
                                new ReceiptItem
                                {
                                    Amount = new Amount
                                    {
                                        Currency = "RUB",
                                        Value = sum,
                                    },
                                    PaymentMode = PaymentMode.FullPayment,
                                    Description = "Подписка в приложении Triggero",
                                    Quantity = 1.00m,
                                    PaymentSubject = PaymentSubject.Service,
                                    VatCode = VatCode.Vat0,

                                }
                            },
                            TaxSystemCode = TaxSystem.Simplified,
                        },

                    };

                    if (!settings.IsBindingPayment)
                    {
                        //  newPayment.PaymentMethodId = user.UserSubscription.SavedPaymentId;
                    }
                    else
                    {
                        newPayment.PaymentMethodData = new PaymentMethod
                        {
                            Type = "bank_card",
                        };
                    }


                    Payment payment = _clientUKassa.CreatePayment(newPayment);

                    userPayment.PaymentId = payment.Id;
                    userPayment.Provider = WebPaymentVendor.UKassa;

                    createdPayment = new CreatedPaymentModel
                    {
                        ConfirmationUrl = payment.Confirmation.ConfirmationUrl,
                        PaymentId = userPayment.PaymentId
                    };
                }
                else if (settings.PaymentMethod == PaymentMethodEnum.Appstore)
                {
                    userPayment.Provider = WebPaymentVendor.AppStore;

                    createdPayment = new CreatedPaymentModel
                    {
                        PaymentId = userPayment.PaymentId
                    };

                    user.UserSubscription.BindedCardNumber = "";
                    user.UserSubscription.SavedPaymentId = "";
                    user.UserSubscription.AllowRecurrent = false;
                }


                user.Payments.Add(userPayment);
                context.Users.Update(user);
                context.SaveChanges();

                return createdPayment;
            }
            catch (Exception ex)
            {
                return new CreatedPaymentModel
                {
                    ConfirmationUrl = ex.ToString()
                };
            }

        }


        public static async Task<CreatedPaymentModel> PaySubscriptionYookassaAuto(
            DatabaseContext db,
            Client clientUKassa,
            int userID,
            SubscriptionPaymentSettings settings)
        {
            try
            {
                var user = db.Users.Include(o => o.UserSubscription)
                             .FirstOrDefault(o => o.Id == userID);


                decimal sum = 0;
                var plan = db.Plans.FirstOrDefault(o => o.BuiltInPlanType == settings.Duration);

                switch (settings.SubType)
                {
                    case SubscriptionType.Trial:
                    sum = (decimal)plan.TotalPrice;
                    break;
                    case SubscriptionType.Full:
                    sum = (decimal)plan.TotalPrice;
                    break;
                    case SubscriptionType.Custom:

                    decimal sumPerMonth = 0;

                    foreach (var optionId in settings.planOptionIds)
                    {
                        var option = db.PlanOptions.FirstOrDefault(o => o.Id == optionId);
                        sumPerMonth += (decimal)option.Price;
                    }

                    if (settings.Duration == BuiltInPlanType.Year)
                    {
                        sumPerMonth = sumPerMonth * 12;
                    }
                    sumPerMonth -= sumPerMonth / 100m * (decimal)plan.Discount;

                    sum = sumPerMonth;
                    break;
                }

                var newPayment = new NewPayment
                {
                    Amount = new Amount { Value = sum, Currency = "RUB" },
                    Confirmation = new Confirmation
                    {
                        Type = ConfirmationType.Redirect,
                        ReturnUrl = $"https://triggero.ru",
                    },
                    Capture = true,
                    PaymentMethodId = user.UserSubscription.SavedPaymentId,
                    Receipt = new Receipt
                    {
                        Customer = new Customer
                        {
                            Email = user.NormalizeEmail(user.Email),
                            Phone = user.Phone
                        },
                        Items = new List<ReceiptItem>
                            {
                                new ReceiptItem
                                {
                                    Amount = new Amount
                                    {
                                        Currency = "RUB",
                                        Value = sum,
                                    },
                                    PaymentMode = PaymentMode.FullPayment,
                                    Description = "Подписка в приложении Triggero",
                                    Quantity = 1.00m,
                                    PaymentSubject = PaymentSubject.Service,
                                    VatCode = VatCode.Vat0,

                                }
                            },
                        TaxSystemCode = TaxSystem.Simplified,
                    },

                };

                Payment payment = clientUKassa.CreatePayment(newPayment);

                if (payment.Status == PaymentStatus.Canceled)
                {
                    user.UserSubscription.SavedPaymentId = "";
                    user.UserSubscription.BindedCardNumber = "";
                    user.UserSubscription.AllowRecurrent = false;

                    return new CreatedPaymentModel();
                }

                user.Payments.Add(new UserPayment()
                {
                    Description = "",
                    Sum = sum,
                    SubscriptionSettingsJson = JsonConvert.SerializeObject(settings),
                    UserId = userID,
                    PaymentId = payment.Id
                });
                db.Users.Update(user);
                db.SaveChanges();

                SetUserSubscriptionYookassaAuto(db, user, settings);


                return new CreatedPaymentModel
                {
                    PaymentId = payment.Id
                };
            }
            catch (Exception ex)
            {
                return new CreatedPaymentModel
                {
                    ConfirmationUrl = ex.ToString()
                };
            }
        }

        [Route("CheckPayment"), HttpGet]
        public async Task<bool> CheckPayment(string paymentId)
        {
            var payment = await DB.UserPayments.FirstOrDefaultAsync(o => o.PaymentId == paymentId);

            return payment.IsPaid;
        }

        [Route("UnbindCard"), HttpGet]
        public async Task UnbindCard(int userId)
        {

            var user = DB.Users.Include(o => o.UserSubscription)
                               .FirstOrDefault(o => o.Id == userId);

            user.UserSubscription.SavedPaymentId = "";
            user.UserSubscription.BindedCardNumber = "";
            user.UserSubscription.AllowRecurrent = false;

            DB.Users.Update(user);
            await DB.SaveChangesAsync();
        }



        [Route("Webhook"), HttpGet, HttpPost, HttpPut]
        public object Webhook()
        {

            try
            {
                var req = httpContextAccessor.HttpContext.Request;
                Message message = Client.ParseMessage(req.Method, req.ContentType, req.Body);
                Payment payment = message?.Object;

                var userPayment = DB.UserPayments.FirstOrDefault(o => o.PaymentId == payment.Id);

                var settings = JsonConvert.DeserializeObject<SubscriptionPaymentSettings>(userPayment.SubscriptionSettingsJson);

                var user = DB.Users.Include(o => o.UserSubscription).ThenInclude(o => o.UserSubsctiptionOptions)
                                                                    .FirstOrDefault(o => o.Id == userPayment.UserId);

                //var subscription = DB.Plans.FirstOrDefault(o => o.BuiltInPlanType == settings.Duration);


                try
                {



                    if (message?.Event == Event.PaymentWaitingForCapture && payment.Paid)
                    {

                        // 4. Подтвердите готовность принять платеж
                        var paidPayment = _clientUKassa.CapturePayment(payment.Id);

                        //Рефанд платежа(если пробная подписка)
                        try
                        {
                            MakeRefund(payment, user);
                        }
                        catch { }


                        SetUserSubscription(user, settings, userPayment, message);

                    }
                    else if (message?.Event == Event.PaymentSucceeded)
                    {

                        //Рефанд платежа(если пробная подписка)
                        try
                        {
                            MakeRefund(payment, user);
                        }
                        catch { }

                        SetUserSubscription(user, settings, userPayment, message);

                    }

                }
                catch (Exception ex)
                {
                    return "2" + ex.ToString();
                }
            }
            catch (Exception ex)
            {

                return "1" + ex.ToString();
            }



            return "Успех ебать";
        }

        private void MakeRefund(Payment payment, User user)
        {
            //Рефанд платежа(если пробная подписка)
            if (payment.Amount.Value == 1.0m)
            {
                _clientUKassa.CreateRefund(new NewRefund
                {
                    Amount = new Amount
                    {
                        Currency = "RUB",
                        Value = 1.0m
                    },
                    Description = "Возврат закрепительного платежа",
                    PaymentId = payment.Id,
                    Receipt = new Receipt
                    {
                        Customer = new Customer
                        {
                            Email = user.Email,
                            Phone = user.Phone,
                            FullName = "Иванов Иван Иванович"
                        },
                        Items = new List<ReceiptItem>
                                    {
                                        new ReceiptItem
                                        {
                                            Amount = new Amount
                                            {
                                                Currency = "RUB",
                                                Value = 1.0m,
                                            },
                                            PaymentMode = PaymentMode.FullPayment,
                                            Description = "Подписка в приложении Triggero",
                                            Quantity = 1.00m,
                                            PaymentSubject = PaymentSubject.Service,
                                            VatCode = VatCode.Vat0

                                        }
                                    },
                        TaxSystemCode = TaxSystem.Simplified
                    }
                });
            }
        }



        [Route("SetPaidViaAppstore"), HttpGet, HttpPost, HttpPut]
        public object SetPaidViaAppstore(string userPaymentId)
        {
            try
            {
                var userPayment = DB.UserPayments.FirstOrDefault(o => o.PaymentId == userPaymentId);
                var settings = JsonConvert.DeserializeObject<SubscriptionPaymentSettings>(userPayment.SubscriptionSettingsJson);

                var user = DB.Users.Include(o => o.UserSubscription).ThenInclude(o => o.UserSubsctiptionOptions)
                                                                    .FirstOrDefault(o => o.Id == userPayment.UserId);
                var subscription = DB.Plans.FirstOrDefault(o => o.BuiltInPlanType == settings.Duration);



                SetUserSubscriptionAppstore(user, settings, userPayment);

            }
            catch (Exception ex)
            {
                return ex.ToString();
            }
            return "Успех";
        }





        private async Task SetUserSubscription(
            User user,
            SubscriptionPaymentSettings settings,
            UserPayment payment,
            Message message)
        {

            user.UserSubscription.SavedPaymentId = message?.Object?.PaymentMethod?.Id;
            if (string.IsNullOrEmpty(user.UserSubscription.SavedPaymentId))
            {
                user.UserSubscription.SavedPaymentId = message?.Object?.PaymentMethodId;
            }

            user.UserSubscription.BindedCardNumber = message?.Object?.PaymentMethod?.Card?.Last4;

            user.UserSubscription.SubscriptionType = settings.SubType;
            user.UserSubscription.SubscriptionDuration = settings.Duration;

            user.UserSubscription.UserSubsctiptionOptions.Clear();
            foreach (var optionId in settings.planOptionIds)
            {
                user.UserSubscription.UserSubsctiptionOptions.Add(new UserSubsctiptionOption
                {
                    PlanOptionId = optionId,
                });
            }


            if (settings.Duration == BuiltInPlanType.Month)
            {

                if (!user.UserSubscription.IsSubscriptionEnded)
                {
                    user.UserSubscription.SubscriptionBefore = user.UserSubscription.SubscriptionBefore.AddMonths(1);
                }
                else
                {
                    user.UserSubscription.SubscriptionBefore = DateTime.UtcNow.AddMonths(1);
                }
            }
            else if (settings.Duration == BuiltInPlanType.Year)
            {

                if (!user.UserSubscription.IsSubscriptionEnded)
                {
                    user.UserSubscription.SubscriptionBefore = user.UserSubscription.SubscriptionBefore.AddYears(1);
                }
                else
                {
                    user.UserSubscription.SubscriptionBefore = DateTime.UtcNow.AddYears(1);
                }

            }
            else if (settings.Duration == BuiltInPlanType.ThreeMonths)
            {

                if (!user.UserSubscription.IsSubscriptionEnded)
                {
                    user.UserSubscription.SubscriptionBefore = user.UserSubscription.SubscriptionBefore.AddMonths(3);
                }
                else
                {
                    user.UserSubscription.SubscriptionBefore = DateTime.UtcNow.AddMonths(3);
                }

            }


            payment.IsPaid = true;
            DB.UserPayments.Update(payment);

            DB.Users.Update(user);
            await DB.SaveChangesAsync();
        }

        private static void SetUserSubscriptionYookassaAuto(DatabaseContext db, User user, SubscriptionPaymentSettings settings)
        {
            user.UserSubscription.SubscriptionType = settings.SubType;
            user.UserSubscription.SubscriptionDuration = settings.Duration;

            user.UserSubscription.UserSubsctiptionOptions.Clear();
            foreach (var optionId in settings.planOptionIds)
            {
                user.UserSubscription.UserSubsctiptionOptions.Add(new UserSubsctiptionOption
                {
                    PlanOptionId = optionId,
                });
            }

            if (user.UserSubscription.SubscriptionType == SubscriptionType.Trial)
            {
                user.UserSubscription.SubscriptionType = SubscriptionType.Full;
            }

            if (settings.Duration == BuiltInPlanType.Month)
            {

                if (!user.UserSubscription.IsSubscriptionEnded)
                {
                    user.UserSubscription.SubscriptionBefore = user.UserSubscription.SubscriptionBefore.AddMonths(1);
                }
                else
                {
                    user.UserSubscription.SubscriptionBefore = DateTime.UtcNow.AddMonths(1);
                }
            }
            else if (settings.Duration == BuiltInPlanType.Year)
            {

                if (!user.UserSubscription.IsSubscriptionEnded)
                {
                    user.UserSubscription.SubscriptionBefore = user.UserSubscription.SubscriptionBefore.AddYears(1);
                }
                else
                {
                    user.UserSubscription.SubscriptionBefore = DateTime.UtcNow.AddYears(1);
                }
            }
            else if (settings.Duration == BuiltInPlanType.ThreeMonths)
            {

                if (!user.UserSubscription.IsSubscriptionEnded)
                {
                    user.UserSubscription.SubscriptionBefore = user.UserSubscription.SubscriptionBefore.AddMonths(3);
                }
                else
                {
                    user.UserSubscription.SubscriptionBefore = DateTime.UtcNow.AddMonths(3);
                }
            }

            db.Users.Update(user);
            db.SaveChanges();
        }

        private async Task SetUserSubscriptionAppstore(User user, SubscriptionPaymentSettings settings, UserPayment payment)
        {
            user.UserSubscription.SavedPaymentId = "";
            user.UserSubscription.BindedCardNumber = "";

            user.UserSubscription.SubscriptionType = settings.SubType;
            user.UserSubscription.SubscriptionDuration = settings.Duration;

            user.UserSubscription.UserSubsctiptionOptions.Clear();
            foreach (var optionId in settings.planOptionIds)
            {
                user.UserSubscription.UserSubsctiptionOptions.Add(new UserSubsctiptionOption
                {
                    PlanOptionId = optionId,
                });
            }

            if (settings.Duration == BuiltInPlanType.Month)
            {
                user.UserSubscription.SubscriptionBefore = user.UserSubscription.SubscriptionBefore.AddMonths(1);
            }
            else if (settings.Duration == BuiltInPlanType.Year)
            {
                user.UserSubscription.SubscriptionBefore = user.UserSubscription.SubscriptionBefore.AddYears(1);
            }


            payment.IsPaid = true;
            DB.UserPayments.Update(payment);

            DB.Users.Update(user);

            await DB.SaveChangesAsync();
        }
    }
}
