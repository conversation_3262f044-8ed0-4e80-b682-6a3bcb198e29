﻿using Newtonsoft.Json;
using Odintcovo.API.Helpers;
using RestSharp;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using Triggero.Domain.Models;
using Triggero.Models.General;
using Triggero.Models.General.UserStats;

namespace MobileAPIWrapper.Methods.General.Users
{
    public class UserStatisticsMethods
    {
        public static string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("UserStatistics/");


        #region Статистика по прохождению тестов, практик, упражнений и дыши и др.
        public async Task<UserStatistics> GetUserStatistics(int userId)
        {
            string url = BASE_HOST + $"GetUserStatistics?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<UserStatistics>(response.Content);
            return obj;
        }
        public async Task<List<TestPassingResult>> GetPassedTests(int userId)
        {
            string url = BASE_HOST + $"GetPassedTests?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<TestPassingResult>>(response.Content);
            return obj;
        }
        public async Task<List<ExercisePassingResult>> GetPassedExercises(int userId)
        {
            string url = BASE_HOST + $"GetPassedExercises?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<List<ExercisePassingResult>>(response.Content);
            return obj;
        }




        public async Task AddTestPassingResult(int userId, TestPassingResult result)
        {
            string url = BASE_HOST + $"AddTestPassingResult?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put, result);
        }
        public async Task AddExercisePassingResult(int userId, ExercisePassingResult result)
        {
            string url = BASE_HOST + $"AddExercisePassingResult?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put, result);
        }
        public async Task AddPracticePassingResult(int userId, PracticePassingResult result)
        {
            string url = BASE_HOST + $"AddPracticePassingResult?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put, result);
        }
        public async Task AddBreathPracticePassingResult(int userId, BreathPracticePassingResult result)
        {
            string url = BASE_HOST + $"AddBreathPracticePassingResult?userId={userId}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put, result);
        }

        #endregion
    }
}
