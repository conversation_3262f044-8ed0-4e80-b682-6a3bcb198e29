﻿using Triggero.Models.Practices.Categories;

namespace Triggero.MauiClient.ViewModels;

public class ListCategoriesPracticesViewModel : BaseCategoriesViewModel
{
    public override Color ThemeColor
    {
        get
        {
            return Color.FromHex("#F8F8FF");
        }
    }

    public override async Task InitializeAsyc()
    {
        try
        {

            var items = await ApplicationState.Data.GetPracticeCategories();

            items.Insert(0, new PracticeCategory
            {
                Title = App.This.Interface.Library.Library.AllPractices,
                Description = App.This.Interface.Library.Library.AllPracticesDescription,
                ImgPath = "/built_in/images/allPractices.png"
            });

            MainThread.BeginInvokeOnMainThread(() =>
            {
                ItemTemplate = new DataTemplate(() =>
                {
                    return new CellCategoryDrawn();//PracticeCategoryCard();
                });

                Items.AddRange(items);

                //preload all images..
                //var cancel = new CancellationTokenSource(TimeSpan.FromSeconds(10));
                //CancelPreload = cancel;
                //SkiaImageManager.Instance.PreloadImages(items.Select(s => s.ImgPath.AddBaseUrl(Triggero.Mobile.Constants.UrlContent)), cancel);
            });

            IsInitialized = true;
        }
        catch (Exception ex)
        {
            Super.Log(ex);
        }


    }

    protected override void OnViewTapped(SkiaControl control)
    {
        //if (control.BindingContext is PracticeCategory model)
        //{
        //    App.OpenView(new PracticesView(model));
        //}
        App.OpenView(new ListElementsView(control.BindingContext as AbstractCategory));

    }

}