﻿@using Triggero.Models
@using Triggero.Models.Plans
@using Triggero.Models.Practices
@using Triggero.Web.ViewModels.Localization
@using Triggero.Web.ViewModels.Stats;
@using Triggero.Web.ViewModels.Tickets
 
 
@{
    ViewData["Title"] = "ПОЛЬЗОВАТЕЛИ";
}
@model UsersVM

 
<!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">    
                    <tr>
                        <td>Всего пользователей: @Model.Total</td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                        <td></td>
                    </tr>
                <tr>
                    <th scope="col">ID</th>
                    <th>Зарегистрирован</th>
                    <th>Имя</th>
                    <th>Email</th>
                    <th>Телефон</th>
                    <th>Подписка</th>
                    <th>Платил</th>
                </tr>
                </thead>
                <tbody>


                    @foreach (var item in Model.Users)
                    {
                        <tr>
                            <td scope="row">#@item.Id</td>
                            <td><span style="display: none;">@item.CreatedAt.Ticks</span>@item.CreatedAt.ToString("dd.MM.yyyy")</td>
                            <td>@item.Name
                                @* <br/>@item.Role *@
                            </td>
                            <td>@item.Email</td>
                            <td>@item.Phone</td>
                            <td>
                                @item.Subscription.Type<br/>
                                до @item.Subscription.Expires.ToString("dd.MM.yyyy")
                            </td>
                            <td><span style="display: none;">@item.LastPayed.GetValueOrDefault().Ticks</span>
                                @if (item.LastPayed != null)
                                {
                                    <span>@item.LastPayed.GetValueOrDefault().ToLocalTime().ToString("dd.MM.yyyy")</span>
                                }
                            </td>
                        </tr>
                    }
                </tbody>
            </table>

@*            <div class="mb-3 position-right">
                <a asp-action="CreatePost" asp-controller="Posts" class="button-classic">Добавить</a>
            </div>*@

            <script>
                $(document).ready( function () {
                    $('#tablemanager').DataTable();
                } );
            </script>
        </div>
    </div>
</div>