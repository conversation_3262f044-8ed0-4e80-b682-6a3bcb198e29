﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Practices;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class BreathPracticeLocalizationsController : AbsController
    {
        public BreathPracticeLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/BreathPractices")]
        public IActionResult BreathPractices()
        {
            var posts = DB.BreathPractices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            posts.Reverse();

            var vm = new LocalizationListVM<BreathPractice>
            {
                Items = posts,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Practices\BreathPractices\BreathPractices.cshtml", vm);
        }





        [Route("Localizations/CreateBreathPracticeLocalization")]
        public IActionResult CreateBreathPracticeLocalization(int id)
        {
            var vm = new LocalizationVM<BreathPracticeLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Practices\BreathPractices\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreateBreathPracticeLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateBreathPracticeLocalizationPOST([FromForm] LocalizationVM<BreathPracticeLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();
            vm.Localization.AudioPath = await SetAttachmentIfHas(vm.Localization.AudioPath);

            var practice = DB.BreathPractices.FirstOrDefault(o => o.Id == vm.Id);
            practice.Localizations.Add(vm.Localization);

            DB.BreathPractices.Update(practice);
            await DB.SaveChangesAsync();
            return RedirectToAction("BreathPractices", "BreathPracticeLocalizations");
        }





        [Route("Localizations/UpdateBreathPracticeLocalization")]
        public IActionResult UpdateBreathPracticeLocalization(int id)
        {
            var practice = DB.BreathPractices
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<BreathPracticeLocalization>
            {
                Id = id,
                Localization = practice.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Practices\BreathPractices\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdateBreathPracticeLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateBreathPracticeLocalizationPOST([FromForm] LocalizationVM<BreathPracticeLocalization> vm)
        {
            DB.BreathPracticeLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("BreathPractices", "BreathPracticeLocalizations");
        }

    }
}
