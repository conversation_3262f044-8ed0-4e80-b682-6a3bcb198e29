/* ===========================================================
 * trumbowyg.upload.js v1.2
 * Upload plugin for Trumbowyg
 * http://alex-d.github.com/Trumbowyg
 * ===========================================================
 * Author : <PERSON> (Alex-D)
 *          Twitter : @AlexandreDemode
 *          Website : alex-d.fr
 * Mod by : <PERSON>-ru
 *          Twitter : @Aleksandr_ru
 *          Website : aleksandr.ru
 */
!function(r){"use strict";var a={serverPath:"",fileFieldName:"fileToUpload",data:[],headers:{},xhrFields:{},urlPropertyName:"file",statusPropertyName:"success",success:void 0,error:void 0,imageWidthModalEdit:!1};function o(r,a){var e=a.shift(),l=a;if(null!==r){if(0===l.length)return r[e];if("object"==typeof r)return o(r[e],l)}return r}!function(){if(!r.trumbowyg.addedXhrProgressEvent){var a=r.ajaxSettings.xhr;r.ajaxSetup({xhr:function(){var r=this,o=a();return o&&"object"==typeof o.upload&&void 0!==r.progressUpload&&o.upload.addEventListener("progress",(function(a){r.progressUpload(a)}),!1),o}}),r.trumbowyg.addedXhrProgressEvent=!0}}(),r.extend(!0,r.trumbowyg,{langs:{en:{upload:"Upload",file:"File",uploadError:"Error"},sl:{upload:"Naloži datoteko",file:"Datoteka",uploadError:"Napaka"},by:{upload:"Загрузка",file:"Файл",uploadError:"Памылка"},cs:{upload:"Nahrát obrázek",file:"Soubor",uploadError:"Chyba"},da:{upload:"Upload",file:"Fil",uploadError:"Fejl"},de:{upload:"Hochladen",file:"Datei",uploadError:"Fehler"},et:{upload:"Lae üles",file:"Fail",uploadError:"Viga"},fr:{upload:"Envoi",file:"Fichier",uploadError:"Erreur"},hu:{upload:"Feltöltés",file:"Fájl",uploadError:"Hiba"},ja:{upload:"アップロード",file:"ファイル",uploadError:"エラー"},ko:{upload:"그림 올리기",file:"파일",uploadError:"에러"},pt_br:{upload:"Enviar do local",file:"Arquivo",uploadError:"Erro"},ru:{upload:"Загрузка",file:"Файл",uploadError:"Ошибка"},sk:{upload:"Nahrať",file:"Súbor",uploadError:"Chyba"},tr:{upload:"Yükle",file:"Dosya",uploadError:"Hata"},zh_cn:{upload:"上传",file:"文件",uploadError:"错误"},zh_tw:{upload:"上傳",file:"文件",uploadError:"錯誤"}},plugins:{upload:{init:function(e){e.o.plugins.upload=r.extend(!0,{},a,e.o.plugins.upload||{});var l={fn:function(){e.saveRange();var a,l=e.o.prefix,t={file:{type:"file",required:!0,attributes:{accept:"image/*"}},alt:{label:"description",value:e.getRangeText()}};e.o.plugins.upload.imageWidthModalEdit&&(t.width={value:""});var d=!1,i=e.openModalInsert(e.lang.upload,t,(function(t){if(!d){d=!0;var u=new FormData;u.append(e.o.plugins.upload.fileFieldName,a),e.o.plugins.upload.data.map((function(r){u.append(r.name,r.value)})),r.map(t,(function(r,a){"file"!==a&&u.append(a,r)})),0===r("."+l+"progress",i).length&&r("."+l+"modal-title",i).after(r("<div/>",{class:l+"progress"}).append(r("<div/>",{class:l+"progress-bar"}))),r.ajax({url:e.o.plugins.upload.serverPath,headers:e.o.plugins.upload.headers,xhrFields:e.o.plugins.upload.xhrFields,type:"POST",data:u,cache:!1,dataType:"json",processData:!1,contentType:!1,progressUpload:function(a){r("."+l+"progress-bar").css("width",Math.round(100*a.loaded/a.total)+"%")},success:function(a){if(e.o.plugins.upload.success)e.o.plugins.upload.success(a,e,i,t);else if(o(a,e.o.plugins.upload.statusPropertyName.split("."))){var l=o(a,e.o.plugins.upload.urlPropertyName.split("."));e.execCmd("insertImage",l,!1,!0);var u=r('img[src="'+l+'"]:not([alt])',e.$box);u.attr("alt",t.alt),e.o.plugins.upload.imageWidthModalEdit&&parseInt(t.width)>0&&u.attr({width:t.width}),setTimeout((function(){e.closeModal()}),250),e.$c.trigger("tbwuploadsuccess",[e,a,l])}else e.addErrorOnModalField(r("input[type=file]",i),e.lang[a.message]),e.$c.trigger("tbwuploaderror",[e,a]);d=!1},error:e.o.plugins.upload.error||function(){e.addErrorOnModalField(r("input[type=file]",i),e.lang.uploadError),e.$c.trigger("tbwuploaderror",[e]),d=!1}})}}));r("input[type=file]").on("change",(function(r){try{a=r.target.files[0]}catch(o){a=r.target.value}}))}};e.addBtnDef("upload",l)}}}})}(jQuery);