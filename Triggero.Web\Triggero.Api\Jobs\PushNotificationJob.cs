﻿using Microsoft.EntityFrameworkCore;
using Triggero.Domain.Models;
using Newtonsoft.Json;
using System.Collections.Generic;
using Triggero.Database;
using Triggero.Domain.Controllers;
using Triggero.Models;
using Triggero.Models.Enums;
using Triggero.Models.General;
using Triggero.Models.Practices;
using Triggero.Models.Tests;
using Triggero.PushNotifications;
using Yandex.Checkout.V3;

namespace Triggero.Domain.Jobs
{
    public class PushNotificationJob
    {
        public static async Task StartJob()
        {


            await Task.Run(async () =>
            {

                Console.WriteLine($"Started PushNotificationJob");

                while (true)
                {

                    try
                    {

                        using (var db = new DatabaseContext())
                        {


                            var users = db.Users.Include(o => o.NotificationSettings)
                                                .Include(o => o.Devices)
                                    .Where(o => o.NotificationSettings.ShouldNotifyTrackerFilling
                                             || o.NotificationSettings.ShouldNotifyBreathTime)
                                    .ToList();

                            foreach (var user in users)
                            {

                                var userLocalTime = DateTime.UtcNow.Add(user.NotificationSettings.BaseUtcOffset);


                                if (user.NotificationSettings.ShouldNotifyTrackerFilling
                                    && CheckTime(userLocalTime, user.NotificationSettings.NotifyTrackerFillingTime)
                                    && !NotifiedTrackerUsers.ContainsKey(user.Id))
                                {
                                    foreach (var device in user.Devices)
                                    {
                                        try
                                        {
                                            PushNotificationsHelper.SendNotification(device.FirebaseToken, "Напоминание", "Заполните трекер настроения");
                                            NotifiedTrackerUsers.Add(user.Id, DateTime.UtcNow.Hour);
                                        }
                                        catch (Exception ex) { }
                                    }
                                }

                                if (user.NotificationSettings.ShouldNotifyBreathTime
                                   && CheckTime(userLocalTime, user.NotificationSettings.NotifyBreathTime)
                                   && !NotifiedBreathUsers.ContainsKey(user.Id))
                                {
                                    foreach (var device in user.Devices)
                                    {
                                        try
                                        {
                                            PushNotificationsHelper.SendNotification(device.FirebaseToken, "Напоминание", "Выполните дыхательную практику");
                                            NotifiedBreathUsers.Add(user.Id, DateTime.UtcNow.Hour);
                                        }
                                        catch (Exception ex) { }
                                    }
                                }

                            }



                        }
                    }
                    catch (Exception ex)
                    {
                    }

                    ClearNotifiedIfNextHour(NotifiedBreathUsers);
                    ClearNotifiedIfNextHour(NotifiedTrackerUsers);

                    await Task.Delay(TimeSpan.FromMinutes(1));
                }
            });
        }



        private static bool CheckTime(DateTime userLocalTime, TimeSpan time)
        {

            return time.Hours == userLocalTime.Hour
                && time.Minutes >= time.Minutes;

        }
        private static void ClearNotifiedIfNextHour(Dictionary<int, int> items)
        {
            for (int i = items.Count - 1; i >= 0; i--)
            {
                var item = items.ElementAt(i);
                if (item.Value != DateTime.UtcNow.Hour)
                {
                    items.Remove(item.Key);
                }

            }
        }


        //userID - hour
        private static Dictionary<int, int> NotifiedTrackerUsers = new Dictionary<int, int>();
        //userID - hour
        private static Dictionary<int, int> NotifiedBreathUsers = new Dictionary<int, int>();
    }
}
