﻿using Microsoft.AspNetCore.Builder;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.Extensions.Logging;

// using Microsoft.AspNetCore.Identity;
// using Microsoft.EntityFrameworkCore;
// using Microsoft.Extensions.Configuration;
// using Microsoft.Extensions.DependencyInjection;
using System.Reflection;
using Triggero.Application.Extensions;
using Triggero.Application.Services;
using Triggero.Common.Helpers;
using Triggero.Database;
using Triggero.Models.Enums;


var builder = WebApplication.CreateBuilder(new WebApplicationOptions
{
    ContentRootPath = Path.GetDirectoryName(Assembly.GetEntryAssembly().Location),
    Args = args
});

Console.WriteLine($"Started at {AppContext.BaseDirectory} env: {builder.Environment.EnvironmentName}");

var configuration = new ConfigurationBuilder()
    .SetBasePath(AppContext.BaseDirectory)
    .AddJsonFile("appsettings.json", optional: false, reloadOnChange: true)
    .AddJsonFile("secrets.json", optional: false, reloadOnChange: true)
    .AddEnvironmentVariables()
    .Build();

builder.Configuration.AddConfiguration(configuration);
builder.Services.AddApplication();

#region DATABASE

var connectionString = configuration["ConnectionStrings:ctxProduction"];
//var connectionString = configuration["ConnectionStrings:ctxDev"]; //LOCAL DEBUG
//var connectionString = Secrets.ConnectionString; //RELEASE

builder.Services.AddScoped<DatabaseMigrator>();
builder.Services.AddDbContext<DatabaseContext>(options =>
    options.UseMySql(connectionString, ServerVersion.AutoDetect(connectionString))
#if DEBUG
        .EnableSensitiveDataLogging()  // Enable sensitive data logging for debugging
        .LogTo(Console.WriteLine, LogLevel.Information));  // Log EF Core operations to console           
#else
            );
#endif

#endregion

var app = builder.Build();

using (var scope = app.Services.CreateScope())
{
    var db = scope.ServiceProvider.GetRequiredService<DatabaseContext>();

    Console.WriteLine($"Db: {db.Database.GetDbConnection().ConnectionString}");

    //Console.WriteLine("Attention WILL BE MIGRATED!!!! SURE?, press any key to continue..");
    //Console.ReadKey();
    //await db.Database.MigrateAsync();

    db.ChangeTracker.AutoDetectChangesEnabled = false;
    var count = 0;

    async Task FormatPhoneNumbers()
    {
        var items = db.Users
            //.Include(i => i.PaymentNotifications)
            .ToList();

        Console.WriteLine($"Processing {items.Count}....");

        foreach (var item in items)
        {
            try
            {
                var phone = PhoneConverter.ImportPhoneNumber(item.Phone, true);
                if (!string.IsNullOrEmpty(phone))
                {
                    if (phone != item.Phone)
                    {
                        Console.WriteLine($"[CHANGED] {item.GlobalId} [{item.Phone}] => [{phone}]");
                        item.Phone = phone;
                        db.Entry(item).State = EntityState.Modified;
                    }
                }
                else
                {
                    Console.WriteLine($"{item.GlobalId} {item.Name} [{item.Phone}]");
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            count++;
        }

        await db.SaveChangesAsync();
    }

    async Task ReadSubscriptions()
    {

        var startDate = new DateTime(2023, 04, 12); //запуск ю-кассы
        var validDate = DateTime.Now;

        var items = db.Users
            .Include(i => i.UserSubscription)
            .Include(i => i.Payments)
            .Where(p => p.Payments.Any(a => a.IsPaid && a.Sum > 1 && a.Date > startDate) && p.UserSubscription.SubscriptionBefore > validDate)
            .ToList();

        Console.WriteLine($"Processing {items.Count}.... up to {validDate:dd-MM-yy}");

        foreach (var item in items)
        {
            try
            {
                var paied = item.Payments.MaxBy(m => m.Date);
                var info = $"{paied.Date:dd-MM-yy} {paied.Sum:0.00}руб.";

                Console.WriteLine($"{item.GlobalId} {item.Name} {item.Phone} {item.Email} {info} [{item.UserSubscription.SubscriptionType}] => {item.UserSubscription.SubscriptionBefore:dd-MM-yy}");

                if (item.UserSubscription.SubscriptionType == SubscriptionType.Trial)
                {
                    var stop = 1;
                }
            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            count++;
        }

    }

    async Task CheckAccount()
    {

        var items = db.Users
            .Include(i => i.UserSubscription)
            .Include(i => i.Payments)
            .Where(p => p.Email == "<EMAIL>".ToLower())
            .ToList();

        foreach (var item in items)
        {
            try
            {
                var paied = item.Payments.MaxBy(m => m.Date);
                var info = $"{paied.Date:dd-MM-yy} {paied.Sum:0.00}руб.";

                Console.WriteLine($"{item.GlobalId} {item.Name} {item.Phone} {item.Email} {info} [{item.UserSubscription.SubscriptionType}] => {item.UserSubscription.SubscriptionBefore:dd-MM-yy}");

                if (item.UserSubscription.SubscriptionType == SubscriptionType.Trial)
                {
                    var stop = 1;
                }

            }
            catch (Exception e)
            {
                Console.WriteLine(e);
            }

            count++;
        }

    }

    async Task DeleteUser()
    {
        var delete = db.Users
            .First(p => p.Email == "<EMAIL>".ToLower());

        db.Entry(delete).State = EntityState.Deleted;
        await db.SaveChangesAsync();
    }

    async Task GetUser()
    {
        var user = db.Users
            .First(p => p.Email == "<EMAIL>".ToLower());

        Console.WriteLine($"USER {user.Email} [{user.Phone}] pass: {user.Password}");
    }

    var _subscriptions = scope.ServiceProvider.GetRequiredService<SubscriptionService>();
    //    var tel = "79165501133";
    //  var user = await db.Users
    //    .Include(i => i.Payments)
    //  .FirstOrDefaultAsync(o => o.Phone == tel);
    //await _subscriptions.SetUserSubscriptionFull(user.GlobalId, DateTime.UtcNow.AddYears(50));

    //await _subscriptions.SetUserSubscriptionNotPayed(user.GlobalId);
    //foreach (var userPayment in user.Payments)
    //{
    //    userPayment.DeliveryStatus = OrderDeliveryStatus.None;
    //}
    //await db.SaveChangesAsync();

    var payments = scope.ServiceProvider.GetRequiredService<PaymentsService>();


    //TEST PAYMENTS

    var users = await db.Users
        .Include(i => i.Payments)
        .Where(o => o.IsDeleted == false &&
                    o.Payments.Any(s => s.PaymentStatus == OrderPaymentStatus.None)).ToListAsync();
    var total = users.Count;
    var index = 0;
    foreach (var user in users)
    {
        index++;
        Console.WriteLine($"Processing user {index}/{total}..       ");
        await payments.CheckAndApplyPayments(user.Id);
    }

    Console.WriteLine($"\r\nFinished!");

    //var user = await db.Users
    //    .Include(x => x.Payments)
    //    .FirstOrDefaultAsync(p => p.Email == "babushka@net");

    //await payments.CheckAndApplyPayments(user.Id);

    //------------------

    //await FormatPhoneNumbers();
    //await ReadSubscriptions();
    //await GetUser();
    //await CheckAccount();


    //foreach (var order in db.Orders.ToList())
    //{
    //    order.CreatedAt = order.CreatedDate;
    //    db.Entry(order).State = EntityState.Modified;
    //    count++;
    //}
    //await db.SaveChangesAsync();

    //var users = db.TeacherProfiles
    //    .Include(x => x.ApplicationUser)
    //    .Include(c => c.Contacts)
    //    .Where(x => x.ApplicationUserId == null).ToList();

    ////import all contacts into appropriate ApplicationUser props
    //foreach (var teacherProfile in users)
    //{
    //    if (teacherProfile.Contacts.Any())
    //    {
    //        teacherProfile.ApplicationUser.Viber = teacherProfile.Contacts.FirstOrDefault(x => x.MessengerType == MessengerType.Viber)?.Link;
    //        teacherProfile.ApplicationUser.Telegram = teacherProfile.Contacts.FirstOrDefault(x => x.MessengerType == MessengerType.Telegram)?.Link;
    //        teacherProfile.ApplicationUser.WhatsApp = teacherProfile.Contacts.FirstOrDefault(x => x.MessengerType == MessengerType.WhatsApp)?.Link;
    //        teacherProfile.ApplicationUser.Facebook = teacherProfile.Contacts.FirstOrDefault(x => x.MessengerType == MessengerType.Facebook)?.Link;
    //        teacherProfile.ApplicationUser.Skype = teacherProfile.Contacts.FirstOrDefault(x => x.MessengerType == MessengerType.Skype)?.Link;
    //        count++;
    //        db.Entry(teacherProfile.ApplicationUser).State = EntityState.Modified;
    //    }
    //}
    //await db.SaveChangesAsync();
    //Console.WriteLine($"Updated {count} profiles");

    //foreach (var item in users)
    //{
    //    var group = db.Groups.FirstOrDefault(x => x.TeacherProfileId == item.Id);

    //    if (group != null)
    //    {
    //        group.TeacherProfileId = null;
    //        group.Teacher = null;
    //        db.Entry(group).State = EntityState.Modified;
    //        await db.SaveChangesAsync();

    //        Console.WriteLine($"Problem {item.Id} ({group.Name})");
    //    }

    //    db.Entry(item).State = EntityState.Deleted;
    //    await db.SaveChangesAsync();


    //var maybe = Helpers.ImportPhoneNumber(item.PhoneNumber);
    //if (maybe == null)
    //{
    //    Console.WriteLine($"Problem {item.PhoneNumber} ({item.FirstName} {item.LastName})");
    //}
    //else
    //{
    //    item.PhoneNumber = maybe;
    //    db.Entry(item).State = EntityState.Modified;
    //    count++;
    //}
    //}



    /*

    var duplicatePhoneNumbers = db.Users
        .GroupBy(u => u.Email)
        .Where(g => g.Count() > 1 && !string.IsNullOrEmpty(g.Key)) // Filter out null or empty phone numbers
        .Select(g => new
        {
            Meta = g.Select(x => new
            {
                x.Id,
                x.FirstName,
                x.LastName,
                x.Email,
            }),
            Email = g.Key,
            Count = g.Count()
        })
        .ToList();

    foreach (var duplicate in duplicatePhoneNumbers)
    {
        Console.WriteLine($"Email: {duplicate.Email} has {duplicate.Count} duplicates:");
        foreach (var userMeta in duplicate.Meta)
        {
            //var student = db.Students.FirstOrDefault(x => x.ApplicationUserId == userMeta.Id);
            //if (student == null)
            //{
            //    Console.WriteLine("------------no student for:");
            //    var delete = db.Users.FirstOrDefault(x => x.Id == userMeta.Id);
            //    db.Entry(delete).State = EntityState.Deleted;
            //}
            Console.WriteLine($"    Id: {userMeta.Id}, First Name: {userMeta.FirstName}, Last Name: {userMeta.LastName}, Phone Number: {userMeta.Email}");
        }
    }
    */

    /*
    var duplicatePhoneNumbers = db.Users
        .GroupBy(u => u.PhoneNumber)
        .Where(g => g.Count() > 1 && !string.IsNullOrEmpty(g.Key)) // Filter out null or empty phone numbers
        .Select(g => new
        {
            Meta = g.Select(x => new
            {
                x.Id,
                x.FirstName,
                x.LastName,
                x.PhoneNumber,
            }),
            PhoneNumber = g.Key,
            Count = g.Count()
        })
        .ToList();

    foreach (var duplicate in duplicatePhoneNumbers)
    {
        Console.WriteLine($"Phone number: {duplicate.PhoneNumber} has {duplicate.Count} duplicates:");
        foreach (var userMeta in duplicate.Meta)
        {
            //var student = db.Students.FirstOrDefault(x => x.ApplicationUserId == userMeta.Id);
            //if (student == null)
            //{
            //    Console.WriteLine("------------no student for:");
            //    var delete = db.Users.FirstOrDefault(x => x.Id == userMeta.Id);
            //    db.Entry(delete).State = EntityState.Deleted;
            //}
            Console.WriteLine($"    Id: {userMeta.Id}, First Name: {userMeta.FirstName}, Last Name: {userMeta.LastName}, Phone Number: {userMeta.PhoneNumber}");
        }
    }
    */


    // await db.SaveChangesAsync();




    //await db.SaveChangesAsync();


    //attach students to applicationusers
    //foreach (var student in db.Students
    //             .Include(x => x.ApplicationUser)
    //             .ToList())
    //{
    //    if (student.ApplicationUser == null)
    //    {
    //        var appUser = db.Users.FirstOrDefault(x => x.UserName == student.PhoneNumber);
    //        if (appUser != null)
    //        {
    //            student.ApplicationUser = appUser;
    //            appUser.PhoneNumber = student.PhoneNumber;

    //            db.Entry(student).State = EntityState.Modified;
    //            db.Entry(appUser).State = EntityState.Modified;

    //            count++;
    //            await db.SaveChangesAsync();
    //        }
    //    }
    //}



    // Collect emails and phone numbers from  managers, teachers into users
    //foreach (var item in db.Students
    //             .Include(x => x.ApplicationUser)
    //             .ToList())
    //{
    //    if (item.ApplicationUser == null)
    //    {
    //        Console.WriteLine($"Warning user {item.Id} {item.FirstName} {item.LastName} has no User");
    //    }
    //    else
    //    {
    //        item.ApplicationUser.FirstName = item.FirstName;
    //        item.ApplicationUser.LastName = item.LastName;
    //        item.ApplicationUser.PhotoUrl = item.PhotoUrl;

    //        db.Entry(item.ApplicationUser).State = EntityState.Modified;

    //        await db.SaveChangesAsync();
    //    }

    //    count++;
    //}


    Console.WriteLine($"processed {count}");

    /*
    #region import cameras

    var importedList = app.Configuration.GetSection("Streams").Get<List<GroupStreamingInfo>>();

    if (importedList.Any())
    {
        db.ChangeTracker.AutoDetectChangesEnabled = false;

        Console.Write($"DATABASE {app.Environment.EnvironmentName} CAMERAS will be DESTROYED!");
        Console.Write("Confirm YES/no? ");
        var confirm = Console.ReadLine();

        if (confirm == "YES")
        {
            var modelType = typeof(Camera);
            var tableName = db.Model.GetEntityTypes().First((t) => t.ClrType == modelType).GetAnnotation("Relational:TableName")
                .Value!.ToString();
            var sql = $"DELETE FROM public.\"{tableName}\";";
            var i = db.Database.ExecuteSqlRaw(sql);

            db.ChangeTracker.AutoDetectChangesEnabled = false;
            db.ChangeTracker.LazyLoadingEnabled = false;

            var index = 1;

            foreach (var dto in importedList)
            {
                string lastSegment = dto.Url.Split('/').Last();
                var add = new Camera()
                {
                    //  GroupId = dto.GroupId,
                    Url = dto.Url,
                    Description = lastSegment
                };

                db.Cameras.Add(add);
                await db.SaveChangesAsync();

                var group = db.Groups.FirstOrDefault(x => x.Id == dto.GroupId);
                if (group != null)
                {
                    add.CameraLinks.Add(new()
                    {
                        CameraId = add.Id,
                        GroupId = group.Id
                    });
                    db.Entry(add).State = EntityState.Modified;
                    await db.SaveChangesAsync();
                }

                Console.WriteLine($"Saved {index++}/{importedList.Count}.");
            }


            Console.WriteLine("FINISHED! Press ANY key to continue..");
            Console.ReadKey();



        }
    }

    #endregion


    if (!db.Regions.Any())
    {
        db.Regions.Add(new()
        {
            Name = "Минск"
        });
        await db.SaveChangesAsync();

        var region = db.Regions.First();

        var prods = await db.Products.ToListAsync();
        foreach (var prod in prods)
        {
            prod.RegionId = region.Id;
        }

        await db.SaveChangesAsync();
    }
    */

    ////Scan and build rooms
    //var countRooms = db.Rooms.Count();
    //if (countRooms == 0)
    //{
    //    var offices = db.Offices.ToList();
    //    foreach (var office in offices)
    //    {
    //        //create list of unique rooms of this office
    //        //take from groups belonging to this office
    //        var rooms = db.Groups.Where(x => x.OfficeId == office.Id).Select(x => x.Room).Distinct().ToList();
    //        foreach (var room in rooms)
    //        {
    //            var add = new Room()
    //            {
    //                Name = room,
    //                OfficeId = office.Id
    //            };
    //            db.Rooms.Add(add);
    //            await db.SaveChangesAsync();
    //        }
    //    }
    //}
    //else
    //{
    //    Console.WriteLine($"Rooms: {countRooms}, db: {db.Database.GetDbConnection().ConnectionString}");
    //}

    ////attach real existing rooms to groups
    ////find existing room by name
    //var groups = db.Groups
    //    .Include(i => i.OfficeRoom)
    //    .ToList();
    //foreach (var group in groups)
    //{
    //    //if (group.OfficeRoom == null)
    //    {
    //        var maybe = await db.Rooms.FirstOrDefaultAsync(x => x.Name == group.Room && x.OfficeId == group.OfficeId);
    //        if (maybe != null)
    //        {
    //            group.OfficeRoom = maybe;
    //            db.Entry(group).State = EntityState.Modified;
    //            await db.SaveChangesAsync();
    //        }
    //    }
    //}

}

Console.WriteLine("FINISHED! Press ANY key to continue..");
Console.ReadKey();


/*

using (DatabaseContext db = new DatabaseContext())
{

    foreach (var item in db.Exercises)
    {

        if (string.IsNullOrEmpty(item.IconImgPath)) continue;

        //string path = Path.Combine("C:\\Users\\<USER>\\Desktop\\adminpanel\\wwwroot\\uploads", item.IconImgPath.Replace("/uploads/",""));
        //string path2 = Path.Combine("C:\\Users\\<USER>\\Desktop\\adminpanel\\wwwroot\\uploads", "mn_" + item.IconImgPath.Replace("/uploads/", ""));

        //var img = Image.FromFile(path);
        //Bitmap bmp = new Bitmap(img, img.Width / 2, img.Height / 2);
        //bmp.Save(path2);

        item.IconImgPath = item.IconImgPath.Replace("mn_/uploads/", "/uploads/mn_");
        db.Exercises.Update(item);
    }

    foreach (var item in db.Practices)
    {
        if (string.IsNullOrEmpty(item.IconImgPath)) continue;

        //string path = Path.Combine("C:\\Users\\<USER>\\Desktop\\adminpanel\\wwwroot\\uploads", item.IconImgPath.Replace("/uploads/", ""));
        //string path2 = Path.Combine("C:\\Users\\<USER>\\Desktop\\adminpanel\\wwwroot\\uploads", "mn_" + item.IconImgPath.Replace("/uploads/", ""));

        //var img = Image.FromFile(path);
        //Bitmap bmp = new Bitmap(img, img.Width / 2, img.Height / 2);
        //bmp.Save(path2);

        item.IconImgPath = item.IconImgPath.Replace("mn_/uploads/", "/uploads/mn_");
        db.Practices.Update(item);
    }

    foreach (var item in db.Tests)
    {
        if (string.IsNullOrEmpty(item.IconImgPath)) continue;

        //string path = Path.Combine("C:\\Users\\<USER>\\Desktop\\adminpanel\\wwwroot\\uploads", item.IconImgPath.Replace("/uploads/", ""));
        //string path2 = Path.Combine("C:\\Users\\<USER>\\Desktop\\adminpanel\\wwwroot\\uploads", "mn_" + item.IconImgPath.Replace("/uploads/", ""));

        //var img = Image.FromFile(path);
        //Bitmap bmp = new Bitmap(img, img.Width / 2, img.Height / 2);
        //bmp.Save(path2);

        item.IconImgPath = item.IconImgPath.Replace("mn_/uploads/", "/uploads/mn_");
        db.Tests.Update(item);
    }

    foreach (var item in db.Topics)
    {
        if (string.IsNullOrEmpty(item.IconImgPath)) continue;

        //string path = Path.Combine("C:\\Users\\<USER>\\Desktop\\adminpanel\\wwwroot\\uploads", item.IconImgPath.Replace("/uploads/", ""));
        //string path2 = Path.Combine("C:\\Users\\<USER>\\Desktop\\adminpanel\\wwwroot\\uploads", "mn_" + item.IconImgPath.Replace("/uploads/", ""));

        //var img = Image.FromFile(path);
        //Bitmap bmp = new Bitmap(img, img.Width / 2, img.Height / 2);
        //bmp.Save(path2);

        item.IconImgPath = item.IconImgPath.Replace("mn_/uploads/", "/uploads/mn_");
        db.Topics.Update(item);
    }

    db.SaveChanges();
}

Console.ReadLine();

*/

