/**
 * Trumbowyg v2.26.0 - A lightweight WYSIWYG editor
 * Trumbowyg plugin stylesheet
 * ------------------------
 * @link http://alex-d.github.io/Trumbowyg
 * @license MIT
 * <AUTHOR> (Alex-D)
 *         Twitter : @AlexandreDemode
 *         Website : alex-d.fr
 */

.trumbowyg-symbol-\&nbsp-dropdown-button {
  display: none !important; }

.trumbowyg-symbol-\&nbsp-dropdown-button + button {
  clear: both; }

.trumbowyg-dropdown-specialChars {
  width: 248px;
  padding: 5px 3px 3px; }

.trumbowyg-dropdown-specialChars button {
  display: block;
  position: relative;
  float: left;
  height: 26px;
  width: 26px;
  padding: 0;
  margin: 2px;
  line-height: 24px;
  text-align: center; }
  .trumbowyg-dropdown-specialChars button:hover::after, .trumbowyg-dropdown-specialChars button:focus::after {
    display: block;
    position: absolute;
    top: -5px;
    left: -5px;
    height: 27px;
    width: 27px;
    background: inherit;
    -webkit-box-shadow: #000 0 0 2px;
            box-shadow: #000 0 0 2px;
    z-index: 10;
    background-color: transparent; }

.trumbowyg .specialChars {
  width: 22px;
  height: 22px;
  display: inline-block; }
