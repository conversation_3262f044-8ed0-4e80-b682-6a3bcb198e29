﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Localization
@using Triggero.Models.Localization.Practices
@using Triggero.Web.ViewModels.Localization
@{
    ViewData["Title"] = "СОЗДАНИЕ ПЕРЕВОДА";
    ViewData["ShowLang"] = true; 
}
@model LocalizationVM<PracticeLocalization>

 <!-- CONTENT -->
<form asp-action="CreatePracticeLocalizationPOST" asp-controller="PracticeLocalizations" method="post" enctype="multipart/form-data" class="content">

    <input type="hidden" asp-for="Id" value="@Model.Id" />

    <div class="row">
        <div class="page">
            
            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Localization.Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Текст</label>
                <textarea id="editor-input" required asp-for="Localization.Description" rows="3"></textarea>
                <script>
                    $('#editor-input')
                        .trumbowyg({
                            btnsDef: {
                                // Create a new dropdown
                                image: {
                                    dropdown: ['insertImage', 'noembed'],
                                    ico: 'insertImage'
                                }
                            },
                            // Redefine the button pane
                            btns: [
                                ['viewHTML'],
                                ['formatting'],
                                ['strong', 'em', 'del'],
                                ['superscript', 'subscript'],
                                ['link'],
                                ['image'], // Our fresh created dropdown
                                ['justifyLeft', 'justifyCenter', 'justifyRight', 'justifyFull'],
                                ['unorderedList', 'orderedList'],
                                ['horizontalRule'],
                                ['removeformat'],
                                ['fullscreen']
                            ]
                        });
                </script>
            </div>
           

            <div class="mb-3">
                <label for="" class="formlabel">Аудиозапись</label>
                <input type="file" name="file" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Основные теги</label>
                <input type="text" asp-for="Localization.MainTags" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Вторичные теги</label>
                <input type="text" asp-for="Localization.SecondaryTags" required class="form-control" placeholder="">
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>