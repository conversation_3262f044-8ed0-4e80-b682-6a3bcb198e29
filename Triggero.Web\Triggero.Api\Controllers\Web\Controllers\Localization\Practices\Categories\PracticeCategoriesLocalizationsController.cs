﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Localization.Practices.Categories;
using Triggero.Models.Practices;
using Triggero.Models.Practices.Categories;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class PracticeCategoriesLocalizationsController : AbsController
    {
        public PracticeCategoriesLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/PracticeCategories")]
        public IActionResult PracticeCategories()
        {
            var cats = DB.PracticeCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            cats.Reverse();

            var vm = new LocalizationListVM<PracticeCategory>
            {
                Items = cats,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Practices\Categories\PracticeCategories\PracticeCategories.cshtml", vm);
        }







        [Route("Localizations/CreatePracticeCategoryLocalization")]
        public IActionResult CreatePracticeCategoryLocalization(int id)
        {
            var vm = new LocalizationVM<PracticeCategoryLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Practices\Categories\PracticeCategories\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreatePracticeCategoryLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreatePracticeCategoryLocalizationPOST([FromForm] LocalizationVM<PracticeCategoryLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();

            var cat = DB.PracticeCategories.FirstOrDefault(o => o.Id == vm.Id);
            cat.Localizations.Add(vm.Localization);

            DB.PracticeCategories.Update(cat);
            await DB.SaveChangesAsync();
            return RedirectToAction("PracticeCategories", "PracticeCategoriesLocalizations");
        }





        [Route("Localizations/UpdatePracticeCategoryLocalization")]
        public IActionResult UpdatePracticeCategoryLocalization(int id)
        {
            var cat = DB.PracticeCategories
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<PracticeCategoryLocalization>
            {
                Id = id,
                Localization = cat.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Practices\Categories\PracticeCategories\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdateExerciseCategoryLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdatePracticeCategoryLocalizationPOST([FromForm] LocalizationVM<PracticeCategoryLocalization> vm)
        {
            DB.PracticeCategoryLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("PracticeCategories", "PracticeCategoriesLocalizations");
        }

    }
}
