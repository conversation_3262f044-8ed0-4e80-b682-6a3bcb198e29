﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.General;
using Triggero.Models.Plans;
using Triggero.Web.Abstractions;

namespace Triggero.Web.Controllers
{
    [Authorize]
    public class PromocodesController : AbsController
    {
        public PromocodesController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Promocodes")]
        public IActionResult Promocodes()
        {
            var posts = DB.Promocodes.Where(o => !o.IsDeleted).ToList();
            posts.Reverse();
            return View(@"Views\Plans\Promocodes\Promocodes.cshtml", posts);
        }

        //[Route("CreatePromocode")]
        public IActionResult CreatePromocode()
        {
            return View(@"Views\Plans\Promocodes\CreatePromocode.cshtml");
        }

        [HttpPost]
        //[Route("CreatePromocodePOST"), HttpPost]
        public async Task<IActionResult> CreatePromocodePOST([FromForm] Promocode promocode)
        {
            DB.Promocodes.Add(promocode);
            await DB.SaveChangesAsync();
            return RedirectToAction("Promocodes", "Promocodes");
        }


        //[Route("UpdatePromocode")]
        public IActionResult UpdatePromocode(int id)
        {
            var promocode = DB.Promocodes.FirstOrDefault(o => o.Id == id);
            return View(@"Views\Plans\Promocodes\UpdatePromocode.cshtml", promocode);
        }

        [HttpPost]
        //[Route("UpdatePromocodePOST"), HttpPost]
        public async Task<IActionResult> UpdatePromocodePOST([FromForm] Promocode promocode)
        {
            DB.Promocodes.Update(promocode);
            await DB.SaveChangesAsync();
            return RedirectToAction("Promocodes", "Promocodes");
        }

        [HttpDelete]
        //[Route("DeletePromocode"), HttpDelete]
        public async Task<IActionResult> DeletePromocode(int id)
        {
            var promocode = DB.Promocodes.FirstOrDefault(o => o.Id == id);
            promocode.IsDeleted = true;
            DB.Promocodes.Update(promocode);
            await DB.SaveChangesAsync();
            return RedirectToAction("Promocodes", "Promocodes");
        }
    }
}
