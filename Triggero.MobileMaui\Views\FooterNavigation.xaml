<?xml version="1.0" encoding="UTF-8" ?>
<views:Canvas
    x:Class="Triggero.MobileMaui.Views.FooterNavigation"
    xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
    xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
    xmlns:views="http://schemas.appomobi.com/drawnUi/2023/draw"
    x:Name="this"
    Gestures="Lock"
    HardwareAcceleration="Disabled"
    HorizontalOptions="Fill"
    VerticalOptions="Start">

    <!--  GRID  -->
    <views:SkiaLayout
        ColumnDefinitions="1*,1*,90,1*,1*"
        ColumnSpacing="0"
        HorizontalOptions="Fill"
        RowSpacing="0"
        Type="Grid"
        UseCache="GPU">

        <views:SkiaLayout.RowDefinitions>
            <RowDefinition Height="34" />
            <RowDefinition Height="68" />
            <RowDefinition Height="{x:Static mobile:Globals.BottomInsets}" />
        </views:SkiaLayout.RowDefinitions>

        <!--Colored background-->
        <views:SkiaControl
            Grid.Row="1"
            Grid.RowSpan="2"
            Grid.ColumnSpan="5"
            BackgroundColor="#FAFFFFFF"
            HorizontalOptions="Fill"
            VerticalOptions="Fill" />

        <!--  HOME  -->
        <views:SkiaLayout
            Grid.Row="1"
            Grid.Column="0"
            HorizontalOptions="Fill"
            Type="Grid"
            VerticalOptions="Fill">

            <views:SkiaLayout
                views:AddGestures.CommandTapped="{Binding Source={Reference this}, Path=CommandSelectHome}"
                HorizontalOptions="Fill"
                Type="Column"
                UseCache="Image"
                VerticalOptions="Center">

                <!-- TODO: MAUI Migration - Replace with LottieIcon when available -->
                <views:SkiaLabel
                    Text="🏠"
                    FontSize="24"
                    HorizontalOptions="Center"
                    TextColor="{Binding Source={x:Reference this}, Path=IsMainPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#10ABB8|#B0B0B0'}" />

                <views:SkiaLabel
                    FontSize="10"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    MaxLines="1"
                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.FooterMain}"
                    TextColor="{x:StaticResource lightBlueColor}">
                    <views:SkiaControl.Triggers>
                        <DataTrigger
                            Binding="{Binding Source={Reference this}, Path=IsMainPageSelected}"
                            TargetType="views:SkiaLabel"
                            Value="True">
                            <Setter Property="TextColor" Value="{x:StaticResource blueColor}" />
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding Source={Reference this}, Path=IsMainPageSelected}"
                            TargetType="views:SkiaLabel"
                            Value="False">
                            <Setter Property="TextColor" Value="{x:StaticResource lightBlueColor}" />
                        </DataTrigger>
                    </views:SkiaControl.Triggers>
                </views:SkiaLabel>

            </views:SkiaLayout>

        </views:SkiaLayout>

        <!--  LIBRARY  -->
        <views:SkiaLayout
            Grid.Row="1"
            Grid.Column="1"
            views:AddGestures.CommandTapped="{Binding Source={Reference this}, Path=CommandSelectLibrary}"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <views:SkiaLayout
                HorizontalOptions="Fill"
                Type="Column"
                UseCache="Image"
                VerticalOptions="Center">

                <!-- TODO: MAUI Migration - Replace with LottieIcon when available -->
                <views:SkiaLabel
                    Text="📚"
                    FontSize="24"
                    HorizontalOptions="Center"
                    TextColor="{Binding Source={x:Reference this}, Path=IsLibraryPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#10ABB8|#B0B0B0'}" />

                <views:SkiaLabel
                    FontSize="10"
                    HorizontalOptions="Center"
                    HorizontalTextAlignment="Center"
                    MaxLines="1"
                    Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.FooterLibrary}"
                    TextColor="{x:StaticResource lightBlueColor}">
                    <views:SkiaControl.Triggers>
                        <DataTrigger
                            Binding="{Binding Source={Reference this}, Path=IsLibraryPageSelected}"
                            TargetType="views:SkiaLabel"
                            Value="True">
                            <Setter Property="TextColor" Value="{x:StaticResource blueColor}" />
                        </DataTrigger>
                        <DataTrigger
                            Binding="{Binding Source={Reference this}, Path=IsLibraryPageSelected}"
                            TargetType="views:SkiaLabel"
                            Value="False">
                            <Setter Property="TextColor" Value="{x:StaticResource lightBlueColor}" />
                        </DataTrigger>
                    </views:SkiaControl.Triggers>
                </views:SkiaLabel>

            </views:SkiaLayout>


        </views:SkiaLayout>

        <!--  MOOD TRACKER  -->
        <views:SkiaLayout
            Grid.Row="0"
            Grid.RowSpan="2"
            Grid.Column="2"
            views:AddGestures.CommandTapped="{Binding Source={Reference this}, Path=GoToMoodTracker}"
            HorizontalOptions="Fill"
            Tag="Spanned"
            TranslationY="0"
            UseCache="Image"
            VerticalOptions="Fill">

            <views:SkiaShape
                Tag="CIRCLE"
                BackgroundColor="White"
                HorizontalOptions="Fill"
                LockRatio="-1"
                Type="Circle"
                VerticalOptions="Start" />

            <!-- TODO: MAUI Migration - Replace with LottieIcon when available -->
            <views:SkiaLabel
                LockRatio="-1"
                Tag="PLUS"
                Margin="5"
                Text="+"
                FontSize="32"
                TextColor="#10ABB8"
                HorizontalOptions="Fill"
                VerticalOptions="Start"
                HorizontalTextAlignment="Center"
                VerticalTextAlignment="Center" />

        </views:SkiaLayout>

        <!--  TESTS  -->
        <views:SkiaLayout
            Grid.Row="1"
            Grid.Column="3"
            views:AddGestures.CommandTapped="{Binding Source={Reference this}, Path=CommandSelectTests}"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">

            <views:SkiaLayout
                HorizontalOptions="Fill"
                UseCache="Image"
                VerticalOptions="Fill">

                <views:SkiaLayout
                    HorizontalOptions="Fill"
                    Type="Column"
                    VerticalOptions="Center">

                    <!-- TODO: MAUI Migration - Replace with LottieIcon when available -->
                    <views:SkiaLabel
                        Text="📝"
                        FontSize="24"
                        HorizontalOptions="Center"
                        TextColor="{Binding Source={x:Reference this}, Path=IsTestsPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#10ABB8|#B0B0B0'}" />

                    <views:SkiaLabel
                        FontSize="10"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        MaxLines="1"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.Tests}"
                        TextColor="{x:StaticResource lightBlueColor}">
                        <views:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={Reference this}, Path=IsTestsPageSelected}"
                                TargetType="views:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{x:StaticResource blueColor}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={Reference this}, Path=IsTestsPageSelected}"
                                TargetType="views:SkiaLabel"
                                Value="False">
                                <Setter Property="TextColor" Value="{x:StaticResource lightBlueColor}" />
                            </DataTrigger>
                        </views:SkiaControl.Triggers>
                    </views:SkiaLabel>

                </views:SkiaLayout>

            </views:SkiaLayout>

        </views:SkiaLayout>

        <!--  CHAT  -->
        <views:SkiaLayout
            Grid.Row="1"
            Grid.Column="4"
            views:AddGestures.CommandTapped="{Binding Source={Reference this}, Path=CommandSelectChatBot}"
            HorizontalOptions="Fill"
            VerticalOptions="Fill">
            <views:SkiaLayout
                HorizontalOptions="Fill"
                UseCache="Image"
                VerticalOptions="Fill">

                <views:SkiaLayout
                    HorizontalOptions="Fill"
                    Type="Column"
                    VerticalOptions="Center">

                    <!-- TODO: MAUI Migration - Replace with LottieIcon when available -->
                    <views:SkiaLabel
                        Text="🤖"
                        FontSize="24"
                        HorizontalOptions="Center"
                        TextColor="{Binding Source={x:Reference this}, Path=IsChatBotPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#10ABB8|#B0B0B0'}" />

                    <views:SkiaLabel
                        FontSize="10"
                        HorizontalOptions="Center"
                        HorizontalTextAlignment="Center"
                        MaxLines="1"
                        Text="{Binding Source={x:Static mobile:App.This}, Path=Interface.MainPage.ChatBot}"
                        TextColor="{x:StaticResource lightBlueColor}">
                        <views:SkiaControl.Triggers>
                            <DataTrigger
                                Binding="{Binding Source={Reference this}, Path=IsChatBotPageSelected}"
                                TargetType="views:SkiaLabel"
                                Value="True">
                                <Setter Property="TextColor" Value="{x:StaticResource blueColor}" />
                            </DataTrigger>
                            <DataTrigger
                                Binding="{Binding Source={Reference this}, Path=IsChatBotPageSelected}"
                                TargetType="views:SkiaLabel"
                                Value="False">
                                <Setter Property="TextColor" Value="{x:StaticResource lightBlueColor}" />
                            </DataTrigger>
                        </views:SkiaControl.Triggers>
                    </views:SkiaLabel>

                </views:SkiaLayout>

            </views:SkiaLayout>


        </views:SkiaLayout>

    </views:SkiaLayout>

</views:Canvas>