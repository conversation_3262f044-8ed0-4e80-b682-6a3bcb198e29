<?xml version="1.0" encoding="UTF-8" ?>
<draw:Canvas x:Class="Triggero.MobileMaui.Views.FooterNavigation"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw"
             x:Name="this"
             Gestures="Lock"
             HorizontalOptions="Fill"
             VerticalOptions="Start">

    <!-- GRID LAYOUT -->
    <draw:SkiaLayout ColumnDefinitions="1*,1*,90,1*,1*"
                     ColumnSpacing="0"
                     HorizontalOptions="Fill"
                     RowSpacing="0"
                     Type="Grid"
                     UseCache="GPU">

        <draw:SkiaLayout.RowDefinitions>
            <RowDefinition Height="34" />
            <RowDefinition Height="68" />
            <RowDefinition Height="34" />
        </draw:SkiaLayout.RowDefinitions>

        <!-- Colored background -->
        <draw:SkiaControl Grid.Row="1"
                          Grid.RowSpan="2"
                          Grid.ColumnSpan="5"
                          BackgroundColor="#FAFFFFFF"
                          HorizontalOptions="Fill"
                          VerticalOptions="Fill" />

        <!-- HOME TAB -->
        <draw:SkiaLayout Grid.Row="1"
                         Grid.Column="0"
                         HorizontalOptions="Fill"
                         Type="Grid"
                         VerticalOptions="Fill">

            <draw:SkiaLayout HorizontalOptions="Fill"
                             Type="Column"
                             UseCache="Image"
                             VerticalOptions="Center">

                <!-- TODO: Replace with proper icon -->
                <draw:SkiaLabel Text="🏠"
                                FontSize="24"
                                HorizontalOptions="Center"
                                TextColor="{Binding Source={x:Reference this}, Path=IsMainPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#007ACC|#8E8E93'}" />

                <draw:SkiaLabel FontSize="10"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                MaxLines="1"
                                Text="Home"
                                TextColor="{Binding Source={x:Reference this}, Path=IsMainPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#007ACC|#8E8E93'}" />

            </draw:SkiaLayout>

            <!-- Tap gesture for Home -->
            <draw:SkiaHotspot HorizontalOptions="Fill"
                              VerticalOptions="Fill"
                              BackgroundColor="Transparent"
                              Tapped="OnHomeTapped" />

        </draw:SkiaLayout>

        <!-- LIBRARY TAB -->
        <draw:SkiaLayout Grid.Row="1"
                         Grid.Column="1"
                         HorizontalOptions="Fill"
                         Type="Grid"
                         VerticalOptions="Fill">

            <draw:SkiaLayout HorizontalOptions="Fill"
                             Type="Column"
                             UseCache="Image"
                             VerticalOptions="Center">

                <!-- TODO: Replace with proper icon -->
                <draw:SkiaLabel Text="📚"
                                FontSize="24"
                                HorizontalOptions="Center"
                                TextColor="{Binding Source={x:Reference this}, Path=IsLibraryPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#007ACC|#8E8E93'}" />

                <draw:SkiaLabel FontSize="10"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                MaxLines="1"
                                Text="Library"
                                TextColor="{Binding Source={x:Reference this}, Path=IsLibraryPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#007ACC|#8E8E93'}" />

            </draw:SkiaLayout>

            <!-- Tap gesture for Library -->
            <draw:SkiaControl HorizontalOptions="Fill"
                              VerticalOptions="Fill"
                              BackgroundColor="Transparent">
                <draw:SkiaControl.GestureRecognizers>
                    <TapGestureRecognizer Tapped="OnLibraryTapped" />
                </draw:SkiaControl.GestureRecognizers>
            </draw:SkiaControl>

        </draw:SkiaLayout>

        <!-- CENTER PLUS BUTTON -->
        <draw:SkiaLayout Grid.Row="1"
                         Grid.Column="2"
                         HorizontalOptions="Fill"
                         Type="Grid"
                         VerticalOptions="Fill">

            <draw:SkiaShape BackgroundColor="#007ACC"
                            CornerRadius="25"
                            HeightRequest="50"
                            HorizontalOptions="Center"
                            VerticalOptions="Center"
                            WidthRequest="50">

                <draw:SkiaLabel Text="+"
                                FontSize="24"
                                FontAttributes="Bold"
                                HorizontalOptions="Center"
                                VerticalOptions="Center"
                                TextColor="White" />

            </draw:SkiaShape>

            <!-- Tap gesture for Plus -->
            <draw:SkiaControl HorizontalOptions="Fill"
                              VerticalOptions="Fill"
                              BackgroundColor="Transparent">
                <draw:SkiaControl.GestureRecognizers>
                    <TapGestureRecognizer Tapped="OnPlusTapped" />
                </draw:SkiaControl.GestureRecognizers>
            </draw:SkiaControl>

        </draw:SkiaLayout>

        <!-- TESTS TAB -->
        <draw:SkiaLayout Grid.Row="1"
                         Grid.Column="3"
                         HorizontalOptions="Fill"
                         Type="Grid"
                         VerticalOptions="Fill">

            <draw:SkiaLayout HorizontalOptions="Fill"
                             Type="Column"
                             UseCache="Image"
                             VerticalOptions="Center">

                <!-- TODO: Replace with proper icon -->
                <draw:SkiaLabel Text="📝"
                                FontSize="24"
                                HorizontalOptions="Center"
                                TextColor="{Binding Source={x:Reference this}, Path=IsTestsPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#007ACC|#8E8E93'}" />

                <draw:SkiaLabel FontSize="10"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                MaxLines="1"
                                Text="Tests"
                                TextColor="{Binding Source={x:Reference this}, Path=IsTestsPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#007ACC|#8E8E93'}" />

            </draw:SkiaLayout>

            <!-- Tap gesture for Tests -->
            <draw:SkiaControl HorizontalOptions="Fill"
                              VerticalOptions="Fill"
                              BackgroundColor="Transparent">
                <draw:SkiaControl.GestureRecognizers>
                    <TapGestureRecognizer Tapped="OnTestsTapped" />
                </draw:SkiaControl.GestureRecognizers>
            </draw:SkiaControl>

        </draw:SkiaLayout>

        <!-- CHATBOT TAB -->
        <draw:SkiaLayout Grid.Row="1"
                         Grid.Column="4"
                         HorizontalOptions="Fill"
                         Type="Grid"
                         VerticalOptions="Fill">

            <draw:SkiaLayout HorizontalOptions="Fill"
                             Type="Column"
                             UseCache="Image"
                             VerticalOptions="Center">

                <!-- TODO: Replace with proper icon -->
                <draw:SkiaLabel Text="🤖"
                                FontSize="24"
                                HorizontalOptions="Center"
                                TextColor="{Binding Source={x:Reference this}, Path=IsChatBotPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#007ACC|#8E8E93'}" />

                <draw:SkiaLabel FontSize="10"
                                HorizontalOptions="Center"
                                HorizontalTextAlignment="Center"
                                MaxLines="1"
                                Text="Chat"
                                TextColor="{Binding Source={x:Reference this}, Path=IsChatBotPageSelected, Converter={StaticResource BoolToColorConverter}, ConverterParameter='#007ACC|#8E8E93'}" />

            </draw:SkiaLayout>

            <!-- Tap gesture for ChatBot -->
            <draw:SkiaControl HorizontalOptions="Fill"
                              VerticalOptions="Fill"
                              BackgroundColor="Transparent">
                <draw:SkiaControl.GestureRecognizers>
                    <TapGestureRecognizer Tapped="OnChatBotTapped" />
                </draw:SkiaControl.GestureRecognizers>
            </draw:SkiaControl>

        </draw:SkiaLayout>

    </draw:SkiaLayout>

</draw:Canvas>
