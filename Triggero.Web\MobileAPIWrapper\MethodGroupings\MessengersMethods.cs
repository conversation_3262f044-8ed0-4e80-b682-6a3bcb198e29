﻿using MobileAPIWrapper.Methods.Library;
using MobileAPIWrapper.Methods.Messengers;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MobileAPIWrapper.MethodGroupings
{
    public class MessengersMethods
    {
        public ChatBotMessengerMethods ChatBotMessengerMethods { get; set; } = new ChatBotMessengerMethods();
        public SupportMessengerMethods SupportMessengerMethods { get; set; } = new SupportMessengerMethods();
    }
}
