﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Practices.Categories;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Practices;

namespace Triggero.Web.Controllers.Practices
{
    [Authorize]
    public class PracticeCategoriesController : AbsController
    {
        public PracticeCategoriesController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("PracticeCategories")]
        public IActionResult PracticeCategories()
        {
            var cats = DB.PracticeCategories.Where(o => !o.IsDeleted).ToList();
            cats.Reverse();
            return View(@"Views\Practices\Categories\PracticeCategories\PracticeCategories.cshtml", cats);
        }


        //[Route("CreatePracticeCategory")]
        public IActionResult CreatePracticeCategory()
        {
            return View(@"Views\Practices\Categories\PracticeCategories\CreatePracticeCategory.cshtml");
        }

        [HttpPost]
        //[Route("CreatePracticeCategoryPOST"), HttpPost]
        public async Task<IActionResult> CreatePracticeCategoryPOST([FromForm] PracticeCategory category)
        {
            category.ImgPath = await SetAttachmentIfHas(category.ImgPath);
            DB.PracticeCategories.Add(category);
            await DB.SaveChangesAsync();
            return RedirectToAction("PracticeCategories", "PracticeCategories");
        }

        //[Route("UpdatePracticeCategory")]
        public IActionResult UpdatePracticeCategory(int id)
        {
            var cat = DB.PracticeCategories.FirstOrDefault(o => o.Id == id);
            return View(@"Views\Practices\Categories\PracticeCategories\UpdatePracticeCategory.cshtml", cat);
        }

        [HttpPost]
        //[Route("UpdatePracticeCategoryPOST"), HttpPost]
        public async Task<IActionResult> UpdatePracticeCategoryPOST([FromForm] PracticeCategory category)
        {
            category.ImgPath = await SetAttachmentIfHas(category.ImgPath);
            DB.PracticeCategories.Update(category);
            await DB.SaveChangesAsync();
            return RedirectToAction("PracticeCategories", "PracticeCategories");
        }

        [HttpDelete]
        //[Route("DeletePracticeCategory"), HttpDelete]
        public async Task<IActionResult> DeletePracticeCategory(int id)
        {
            var cat = DB.PracticeCategories.FirstOrDefault(o => o.Id == id);
            cat.IsDeleted = true;
            DB.PracticeCategories.Update(cat);
            await DB.SaveChangesAsync();
            return RedirectToAction("PracticeCategories", "PracticeCategories");
        }
    }
}
