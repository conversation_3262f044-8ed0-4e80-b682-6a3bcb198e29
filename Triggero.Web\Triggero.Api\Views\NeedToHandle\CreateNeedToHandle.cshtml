﻿@using CRMWeb.Helpers.Html
@using Triggero.Models.General.Influence;
@{
    ViewData["Title"] = "СОЗДАНИЕ ПУНКТА НУЖНО ПРОРАБОТАТЬ";
}
@model NeedToHandle

 <!-- CONTENT -->
<form asp-action="CreateNeedToHandlePOST" asp-controller="NeedToHandle" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">

            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>