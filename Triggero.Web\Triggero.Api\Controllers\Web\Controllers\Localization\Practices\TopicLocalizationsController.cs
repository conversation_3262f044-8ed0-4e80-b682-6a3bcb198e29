﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using Triggero.Database;
using Triggero.Models;
using Triggero.Models.Localization;
using Triggero.Models.Localization.Practices;
using Triggero.Models.Practices;
using Triggero.Web.Abstractions;
using Triggero.Web.ViewModels.Localization;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class TopicLocalizationsController : AbsController
    {
        public TopicLocalizationsController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("Localizations/Topics")]
        public IActionResult Topics()
        {
            var topics = DB.Topics
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .Where(o => !o.IsDeleted)
                .ToList();
            topics.Reverse();

            var vm = new LocalizationListVM<Topic>
            {
                Items = topics,
                CurrentLanguage = GetCurrentLanguage()
            };
            return View(@"Views\Localization\Practices\Topics\Topics.cshtml", vm);
        }







        [Route("Localizations/CreateTopicLocalization")]
        public IActionResult CreateTopicLocalization(int id)
        {
            var vm = new LocalizationVM<TopicLocalization>
            {
                Id = id,
            };
            return View(@"Views\Localization\Practices\Topics\CreateLocalization.cshtml", vm);
        }
        [Route("Localizations/CreateTopicLocalizationPOST"), HttpPost]
        public async Task<IActionResult> CreateTopicLocalizationPOST([FromForm] LocalizationVM<TopicLocalization> vm)
        {
            vm.Localization.Language = GetCurrentLanguage();

            var topic = DB.Topics.FirstOrDefault(o => o.Id == vm.Id);
            topic.Localizations.Add(vm.Localization);

            DB.Topics.Update(topic);
            await DB.SaveChangesAsync();
            return RedirectToAction("Topics", "TopicLocalizations");
        }





        [Route("Localizations/UpdateTopicLocalization")]
        public IActionResult UpdateTopicLocalization(int id)
        {
            var topic = DB.Topics
                .Include(o => o.Localizations).ThenInclude(o => o.Language)
                .FirstOrDefault(o => !o.IsDeleted && o.Id == id);
            var lang = GetCurrentLanguage();

            var vm = new LocalizationVM<TopicLocalization>
            {
                Id = id,
                Localization = topic.Localizations.FirstOrDefault(o => o.LanguageId == lang.Id)
            };
            return View(@"Views\Localization\Practices\Topics\UpdateLocalization.cshtml", vm);
        }
        [Route("Localizations/UpdateTopicLocalizationPOST"), HttpPost]
        public async Task<IActionResult> UpdateTopicLocalizationPOST([FromForm] LocalizationVM<TopicLocalization> vm)
        {
            DB.TopicLocalizations.Update(vm.Localization);
            await DB.SaveChangesAsync();
            return RedirectToAction("Topics", "TopicLocalizations");
        }

    }
}
