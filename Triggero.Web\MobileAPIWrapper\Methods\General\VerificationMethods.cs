﻿using Newtonsoft.Json;
using Odintcovo.API.Helpers;
using RestSharp;

namespace MobileAPIWrapper.Methods.General
{
    public class VerificationMethods
    {
        private string BASE_HOST = TriggeroMobileAPI.AddBaseUrl("Verification/");

        public async Task<bool> IsCodeRight(string address, string code)
        {
            string url = BASE_HOST + $"IsCodeRight?address={address}&code={code}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Get);

            var obj = JsonConvert.DeserializeObject<bool>(response.Content);
            return obj;
        }
        public async Task SendSMS(string phone)
        {
            string url = BASE_HOST + $"SendSMS?phone={phone}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);
        }

        public async Task<bool> SendEmailCode(string email)
        {
            string url = BASE_HOST + $"SendEmailCode?email={email}";
            var response = await RequestHelper.ExecuteRequestAsync(url, Method.Put);
            return response.IsSuccessful;
        }
    }
}
