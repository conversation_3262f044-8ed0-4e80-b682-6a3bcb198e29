﻿@using MarkupCreator.Helpers.Converters
@using Triggero.Models.Messengers.ChatBot;
@using Triggero.Models.Tickets
@using Triggero.Web.ViewModels;
@{
    ViewData["Title"] = "ЧАТЫ ИЗ ЧАТ-БОТА";
}
@model PaginationVM<ChatBotChat>

  <!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                    <th scope="col">ID</th>
                    <th scope="col">Пользователь</th>
                    <th scope="col">Последний ответ</th>
                    <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model.Items)
                    {
                        <tr>
                            <th scope="row">#@item.Id</th>
                            <td>@item.User.Name @item.User.Surname</td>
                            <td>@EnumDescriptionHelper.GetDescription(item.Messages.LastOrDefault().ChatSide)</td>
                            <td>
                                <div class="interect">
                                    <a asp-action="ChatPage" asp-controller="ChatBot" asp-route-id="@item.Id" class="reply"><i class="fa-solid fa-reply-all"></i></a>
                                </div>
                            </td>
                        </tr>
                    }                
                </tbody>
            </table>

            @if (Model.Items.Any() || Model.PagesCount>1)
            {
                <nav aria-label="Page navigation example">
                    <ul class="pagination justify-content-center">

                        @{
                            bool hasLastPageBtn = false;
                            bool wasSet = false;
                        }


                        @if (Model.CurrentPage > 1)
                        {
                            <li class="page-item">
                                <a class="page-link navi-go" asp-action="Chats" asp-controller="ChatBot" asp-route-page="@(Model.CurrentPage-1)" aria-label="Previous">
                                    <i class="fa-solid fa-chevron-left"></i>
                                </a>
                            </li>
                            <li class="page-item"><a class="page-link" asp-action="Chats" asp-controller="ChatBot" asp-route-page="1">1</a></li>
                        }
                        else
                        {
                            <li class="page-item"><a class="page-link active" asp-action="Chats" asp-controller="ChatBot" asp-route-page="1">1</a></li>
                        }

                        @if (Model.CurrentPage - 3 > 3)
                        {
                            <li class="page-item"><a class="page-link">...</a></li>
                        }


                        @for (int i = Model.CurrentPage - 3; i < Model.CurrentPage; i++)
                        {

                            @if (i == Model.PagesCount)
                            {
                                hasLastPageBtn = true;
                            }
                            @if (i <= 3 && !wasSet)
                            {
                                wasSet = true;
                                i = 2;
                                @if (i >= Model.CurrentPage) break;
                            }
                            <li class="page-item"><a class="page-link" asp-action="Chats" asp-controller="ChatBot" asp-route-page="@i">@i</a></li>
                        }

                        @if (Model.CurrentPage > 1 && Model.CurrentPage < Model.PagesCount)
                        {
                            <li class="page-item active"><a class="page-link" href="#">@Model.CurrentPage</a></li>
                        }



                        @for (int i = Model.CurrentPage + 1; i < Model.CurrentPage + 3; i++)
                        {
                            @if (i == Model.PagesCount)
                            {
                                hasLastPageBtn = true;
                            }
                            else if (i > Model.PagesCount)
                            {
                                break;
                            }
                            <li class="page-item"><a class="page-link" asp-action="Chats" asp-controller="ChatBot" asp-route-page="@i">@i</a></li>
                            if (i + 2 == Model.PagesCount)
                            {
                                i++;
                                <li class="page-item"><a class="page-link" asp-action="Chats" asp-controller="ChatBot" asp-route-page="@i">@i</a></li>
                                break;
                            }
                        }

                        @if (Model.CurrentPage + 3 < Model.PagesCount - 1)
                        {
                            <li class="page-item"><a class="page-link">...</a></li>
                        }

                        @if (!hasLastPageBtn)
                        {
                            <li class="page-item"><a class="page-link" asp-action="Chats" asp-controller="ChatBot" asp-route-page="@Model.PagesCount">@Model.PagesCount</a></li>
                        }



                        @if (Model.CurrentPage < Model.PagesCount && Model.PagesCount > 0)
                        {
                            <li class="page-item">
                                <a class="page-link navi-go" asp-action="Chats" asp-controller="ChatBot" asp-route-page="@(Model.CurrentPage+1)" aria-label="Next">
                                    <i class="fa-solid fa-chevron-right"></i>
                                </a>
                            </li>
                        }


                    </ul>
                </nav>
            }

           

      
        </div>
    </div>
</div>