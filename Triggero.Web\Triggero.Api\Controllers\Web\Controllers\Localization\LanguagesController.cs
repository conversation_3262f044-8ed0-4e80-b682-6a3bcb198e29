﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models.General;
using Triggero.Web.Abstractions;

namespace Triggero.Web.Controllers.Localization
{
    [Authorize]
    public class LanguagesController : AbsController
    {
        public LanguagesController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        //[Route("Languages")]
        public IActionResult Languages()
        {
            var languages = DB.Languages.ToList();
            languages.Reverse();
            return View(@"Views\Localization\Languages\Languages.cshtml", languages);
        }

        //[Route("CreateLanguage")]
        public IActionResult CreateLanguage()
        {
            return View(@"Views\Localization\Languages\CreateLanguage.cshtml");
        }

        [HttpPost]
        //[Route("CreateLanguagePOST"), HttpPost]
        public async Task<IActionResult> CreateLanguagePOST([FromForm] Language language)
        {
            language.ImgPath = await SetAttachmentIfHas(language.ImgPath);
            DB.Languages.Add(language);
            await DB.SaveChangesAsync();

            DB.MakeInterfaceLocalization(language.Id);
            return RedirectToAction("Languages", "Languages");
        }

        //[Route("UpdateLanguage")]
        public IActionResult UpdateLanguage(int id)
        {
            var lang = DB.Languages.FirstOrDefault(o => o.Id == id);
            return View(@"Views\Localization\Languages\UpdateLanguage.cshtml", lang);
        }

        [HttpPost]
        //[Route("UpdateLanguagePOST"), HttpPost]
        public async Task<IActionResult> UpdateLanguagePOST([FromForm] Language language)
        {
            language.ImgPath = await SetAttachmentIfHas(language.ImgPath);
            DB.Languages.Update(language);
            await DB.SaveChangesAsync();
            return RedirectToAction("Languages", "Languages");
        }

        [HttpDelete]
        //[Route("DeleteLanguage"), HttpDelete]
        public async Task<IActionResult> DeleteLanguage(int id)
        {
            var lang = DB.Languages.FirstOrDefault(o => o.Id == id);
            DB.Languages.Remove(lang);
            await DB.SaveChangesAsync();
            return RedirectToAction("Languages", "Languages");
        }


        //[HttpGet, Route("LangMainPage")]
        public async Task<IActionResult> LangMainPage(int langId)
        {
            await SetLanguageCookie(langId);
            return View(@"Views\Localization\MainPage.cshtml");
        }


        //[HttpGet, Route("GetLanguageCookie")]
        public async Task<Language> GetLanguageCookie()
        {
            if (HttpContext.Request.Cookies.ContainsKey("Language"))
            {
                int id = Convert.ToInt32(HttpContext.Request.Cookies["Language"]);
                return DB.Languages.FirstOrDefault(o => o.Id == id);
            }
            return null;
        }

        //[HttpGet, Route("SetLanguageCookie")]
        public async Task SetLanguageCookie(int langId)
        {
            if (HttpContext.Request.Cookies.ContainsKey("Language"))
            {
                HttpContext.Response.Cookies.Delete("Language");
            }
            HttpContext.Response.Cookies.Append("Language", langId.ToString());
        }

    }
}
