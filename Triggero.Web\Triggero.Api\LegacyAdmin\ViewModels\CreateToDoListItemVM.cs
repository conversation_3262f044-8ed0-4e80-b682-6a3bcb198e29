﻿using Triggero.Models;
using Triggero.Models.General.Influence;
using Triggero.Models.Practices;
using Triggero.Models.Tests;

namespace Triggero.Web.ViewModels
{
    public class CreateToDoListItemVM
    {
        public ToDoListItem ToDoListItem { get; set; } = new ToDoListItem();



        public List<Exercise> Exercises { get; set; } = new List<Exercise>();
        public List<Topic> Topics { get; set; } = new List<Topic>();
        public List<Practice> Practices { get; set; } = new List<Practice>();
        public List<Test> Tests { get; set; } = new List<Test>();
    }
}
