﻿@using Triggero.Models
@using Triggero.Models.Plans
@using Triggero.Models.Practices
@using Triggero.Web.ViewModels.Localization
@using Triggero.Web.ViewModels.Stats;
@using Triggero.Web.ViewModels.Tickets
@{
    ViewData["Title"] = "РЕГИСТРАЦИИ";
}
@model RegistrationsVM
<!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                        <th scope="col">Всего регистраций: @Model.Total</th>
                        <th scope="col"></th>
                    </tr>
                    <tr>
                        <th scope="col">Дата</th>
                        <th scope="col">Кол-во</th>
                    </tr>
                </thead>
                <tbody>

                    @foreach (var item in Model.Registrations.OrderByDescending(o => o.Date))
                    {
                         <tr>
                            <td><span style="display: none;">@item.Date.Ticks</span>@item.Date.ToString("dd.MM.yyyy")</td>
                            <td>@item.Count</td>
                        </tr>
                    }
                </tbody>
            </table>

@*            <div class="mb-3 position-right">
                <a asp-action="CreatePost" asp-controller="Posts" class="button-classic">Добавить</a>
            </div>*@

            <script>
                $(document).ready( function () {
                    $('#tablemanager').DataTable();
                } );
            </script>
        </div>
    </div>
</div>