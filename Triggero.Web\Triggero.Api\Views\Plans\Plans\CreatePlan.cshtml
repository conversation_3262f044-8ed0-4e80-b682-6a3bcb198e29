﻿@using CRMWeb.Helpers.Html
@using Triggero.Models
@using Triggero.Models.Plans
@using Triggero.Web.ViewModels.Plans
@{
    ViewData["Title"] = "СОЗДАНИЕ ТАРИФНОГО ПЛАНА";
}
@model CreatePlanVM

 <!-- CONTENT -->
<form onsubmit="onSumbit(event)" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">


            <div class="mb-3">
               <label for="" class="formlabel">Заголовок</label>
               <input type="text" asp-for="NewPlan.Title" id="plan_title" required class="form-control" placeholder="">
           </div>

            <div class="mb-3" style="display:none">
               <label for="" class="formlabel">Описание</label>
                <input type="text" asp-for="NewPlan.Description" id="plan_desc" class="form-control" placeholder="">
           </div>

            <div class="mb-3">
                <label for="" class="formlabel">Цена</label>
                <input type="number" asp-for="NewPlan.Price" id="plan_price" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Скидка, %</label>
                <input type="number" asp-for="NewPlan.Discount" id="plan_discount" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Кол-во дней</label>
                <input type="number" asp-for="NewPlan.Days" id="plan_days" required class="form-control" placeholder="">
            </div>


            
           @* <div class="mb-3">
                <label for="" class="formlabel">опции</label>
            </div>*@

            @foreach (var option in Model.Options)
            {
                <div class="form-check" hidden>
                    <input class="form-check-input" data-id="@option.Id" type="checkbox" value="" id="flexcheckdefault">
                    <label class="form-check-label" for="flexcheckdefault">
                        @option.Title (@option.Price руб.)
                    </label>
                </div>
            }

           

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>

<script>
    async function onSumbit(event){
        event.preventDefault();


        let selectedOptionIds = [];

        let checkboxes = document.querySelectorAll('.form-check-input');
        for (let i = 0; i < checkboxes.length;i++){

            let checkbox = checkboxes[i];

            if(checkbox.checked === true){
                let id = checkbox.getAttribute('data-id');
                selectedOptionIds.push(new Number(id));
            }
        }



        let obj = {
            NewPlan:{
                Id:0,
                Title: document.getElementById('plan_title').value,
                Description: document.getElementById('plan_desc').value,
                Price: new Number(document.getElementById('plan_price').value),
                Days: new Number(document.getElementById('plan_days').value),
                Discount: new Number(document.getElementById('plan_discount').value),
            },
            SelectedIds : selectedOptionIds       
        };

        await fetch(document.location.origin + `/CreatePlanPOST`, {
            method: 'POST', // or 'PUT'
            headers: {
            'Content-Type': 'application/json',
            },
            body: JSON.stringify(obj),
        })

        document.location.href = document.location.origin + `/Plans`;
    }
</script>