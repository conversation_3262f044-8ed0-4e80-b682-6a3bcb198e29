﻿using MobileAPIWrapper.Methods.General.Users;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Text;
using System.Threading.Tasks;

namespace MobileAPIWrapper.MethodGroupings
{
    public class UserMethods
    {
        public UserFavoritesMethods UserFavoritesMethods { get; set; } = new UserFavoritesMethods();
        public UserMoodTrackerMethods UserMoodTrackerMethods { get; set; } = new UserMoodTrackerMethods();
        public UsersMethods UsersMethods { get; set; } = new UsersMethods();
        public UserStatisticsMethods UserStatisticsMethods { get; set; } = new UserStatisticsMethods();
        public UserSubscriptionMethods SubscriptionMethods { get; set; } = new UserSubscriptionMethods();
    }
}
