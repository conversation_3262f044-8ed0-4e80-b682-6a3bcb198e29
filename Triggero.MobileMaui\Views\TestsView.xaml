<?xml version="1.0" encoding="utf-8" ?>
<ContentView x:Class="Triggero.MobileMaui.Views.TestsView"
             xmlns="http://schemas.microsoft.com/dotnet/2021/maui"
             xmlns:x="http://schemas.microsoft.com/winfx/2009/xaml"
             xmlns:draw="http://schemas.appomobi.com/drawnUi/2023/draw">

    <draw:Canvas HorizontalOptions="Fill" VerticalOptions="Fill">
        <draw:SkiaLayout Type="Column" 
                         Padding="20" 
                         Spacing="16"
                         HorizontalOptions="Fill" 
                         VerticalOptions="Fill">

            <!-- Header -->
            <draw:SkiaLabel Text="Tests"
                            FontSize="24"
                            FontAttributes="Bold"
                            TextColor="#007ACC"
                            HorizontalOptions="Center" />

            <!-- Content placeholder -->
            <draw:SkiaShape BackgroundColor="#F0F0F0"
                            CornerRadius="12"
                            Padding="16"
                            HorizontalOptions="Fill">
                
                <draw:SkiaLayout Type="Column" Spacing="8">
                    <draw:SkiaLabel Text="📝 Tests &amp; Categories"
                                    FontSize="18"
                                    FontAttributes="Bold"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="This is the Tests view placeholder. Will include test categories and collection views."
                                    FontSize="14"
                                    TextColor="#666666"
                                    MaxLines="3" />
                </draw:SkiaLayout>
                
            </draw:SkiaShape>

            <!-- TODO sections placeholder -->
            <draw:SkiaShape BackgroundColor="#E8F4FD"
                            CornerRadius="12"
                            Padding="16"
                            HorizontalOptions="Fill">
                
                <draw:SkiaLayout Type="Column" Spacing="8">
                    <draw:SkiaLabel Text="🔄 Migration TODO:"
                                    FontSize="16"
                                    FontAttributes="Bold"
                                    TextColor="#007ACC" />
                    
                    <draw:SkiaLabel Text="• Test categories collection"
                                    FontSize="12"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="• TestCard components migration"
                                    FontSize="12"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="• Search integration"
                                    FontSize="12"
                                    TextColor="#333333" />
                    
                    <draw:SkiaLabel Text="• All Tests category"
                                    FontSize="12"
                                    TextColor="#333333" />
                </draw:SkiaLayout>
                
            </draw:SkiaShape>

        </draw:SkiaLayout>
    </draw:Canvas>

</ContentView>
