﻿@using CRMWeb.Helpers.Html
@using Triggero.Models.MoodTracker
@using Triggero.Web.ViewModels.Factors
@{
    ViewData["Title"] = "РЕДАКТИРОВАНИЕ ПОДПУНКТА ФАКТОРОВ";
}
@model CreateFactorDetailVM

 <!-- CONTENT -->
<form asp-action="UpdateFactorDetailPOST" asp-controller="FactorDetails" method="post" enctype="multipart/form-data" class="content">
    <div class="row">
        <div class="page">

            <input type="hidden" asp-for="NewFactorDetail.Id" value="@Model.NewFactorDetail.Id" />
            <input type="hidden" asp-for="NewFactorDetail.ImgPath" value="@Model.NewFactorDetail.ImgPath" />

            <div class="mb-3">
                <label for="" class="formlabel">Загрузить изображение</label>
                <div class="content-load">
                    <label class="loadfile1" for="pct" style="background-size: 40% 100%;"><i class="fa-solid fa-images"></i></label>
                    <input class="pctfile" name="file" type="file" id="pct">

                    <script>
                        document.querySelector(".pctfile").addEventListener("change", function () {
                            if (this.files[0]) {
                                var fr = new FileReader();

                                fr.addEventListener("load", function () {
                                    document.querySelector(".loadfile1").style.backgroundImage = "url(" + fr.result + ")";
                                    document.querySelector(".loadfile1").style.color = "transparent";
                                }, false);

                                fr.readAsDataURL(this.files[0]);
                            }
                        });
                    </script>
                </div>
            </div>


            <div class="mb-3">
                <label for="" class="formlabel">Название</label>
                <input type="text" asp-for="NewFactorDetail.Title" required class="form-control" placeholder="">
            </div>

            <div class="mb-3">
                <label for="" class="formlabel">Фактор</label>
                <select class="form-select" asp-for="NewFactorDetail.FactorId" required>
                    @Html.Raw(@ClassesToHtmlHelper<Factor>.Convert(Model.Factors,Model.NewFactorDetail.FactorId))
                </select>
            </div>

            <div class="mb-3 position-right">
                <button type="submit" class="button-classic">Сохранить</button>
            </div>
        </div>
    </div>
</form>