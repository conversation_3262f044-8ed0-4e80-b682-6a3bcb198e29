﻿using Microsoft.EntityFrameworkCore;
using Newtonsoft.Json;
using Triggero.Database;
using Triggero.Domain.Controllers;
using Triggero.Domain.Controllers.Rest;
using Triggero.Domain.Models;
using Triggero.Models.Enums;
using Triggero.Models.General;
using Yandex.Checkout.V3;

namespace Triggero.Domain.Jobs
{

    public class AutoPaymentJob
    {

        public static async Task StartJob(IServiceProvider services)
        {
            await Task.Run(async () =>
            {
                var scope = services.CreateScope();
                var config = scope.ServiceProvider.GetRequiredService<IConfiguration>();

                Console.WriteLine($"Started AutoPaymentJob");

                var client = new Yandex.Checkout.V3.Client(
                    shopId: config["Secrets:UKassa:ShopId"],
                    secretKey: config["Secrets:UKassa:Key"]);

                while (true)
                {

                    try
                    {

                        using (var db = new DatabaseContext())
                        {


                            var users = db.Users.Include(o => o.UserSubscription).ThenInclude(o => o.UserSubsctiptionOptions).Where(o => !o.IsDeleted).ToList();

                            foreach (var user in users)
                            {


                                if (user.UserSubscription == null) continue;

                                if (!user.UserSubscription.IsSubscriptionEnded
                                    || !user.UserSubscription.AllowRecurrent
                                    || string.IsNullOrEmpty(user.UserSubscription.SavedPaymentId))
                                {
                                    continue;
                                }


                                try
                                {
                                    var planOptionIds = new List<int>();
                                    foreach (var option in user.UserSubscription.UserSubsctiptionOptions)
                                    {
                                        planOptionIds.Add(option.PlanOptionId);
                                    }

                                    var settings = new SubscriptionPaymentSettings
                                    {
                                        Duration = user.UserSubscription.SubscriptionDuration,
                                        IsBindingPayment = false,
                                        SubType = user.UserSubscription.SubscriptionType,
                                        planOptionIds = planOptionIds
                                    };

                                    var paymentInfo = await PaymentsController.PaySubscriptionYookassaAuto(db, client, user.Id, settings);
                                }
                                catch (Exception ex)
                                {
                                }


                                await Task.Delay(2000);

                            }

                        }
                    }
                    catch (Exception ex)
                    {
                    }


                    await Task.Delay(TimeSpan.FromHours(1));
                }
            });
        }

    }
}
