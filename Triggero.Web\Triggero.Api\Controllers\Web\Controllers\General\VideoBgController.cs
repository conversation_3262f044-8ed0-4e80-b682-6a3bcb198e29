﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Triggero.Database;
using Triggero.Models.General;
using Triggero.Web.Abstractions;

namespace Triggero.Web.Controllers.General
{
    [Authorize]
    public class VideoBgController : AbsController
    {
        public VideoBgController(DatabaseContext db, IWebHostEnvironment appEnv) : base(db, appEnv)
        {
        }

        [Route("VideoBGs")]
        public IActionResult VideoBGs()
        {
            var videos = DB.VideoBgs.Where(o => !o.IsDeleted).ToList();
            videos.Reverse();
            return View(@"Views\General\VideoBGs\VideoBGs.cshtml", videos);
        }




        [Route("CreateVideoBG")]
        public IActionResult CreateVideoBG()
        {
            return View(@"Views\General\VideoBGs\CreateVideoBG.cshtml");
        }
        [Route("CreateVideoBGPOST"), HttpPost]
        public async Task<IActionResult> CreateVideoBGPOST([FromForm] VideoBg video)
        {
            video.VideoPath = await SetAttachmentIfHas(video.VideoPath);
            DB.VideoBgs.Add(video);
            await DB.SaveChangesAsync();
            return RedirectToAction("VideoBGs", "VideoBg");
        }


        [Route("UpdateVideoBG")]
        public IActionResult UpdateVideoBG(int id)
        {
            var video = DB.VideoBgs.FirstOrDefault(o => o.Id == id);
            return View(@"Views\General\VideoBGs\UpdateVideoBG.cshtml", video);
        }
        [Route("UpdateVideoBGPOST"), HttpPost]
        public async Task<IActionResult> UpdateVideoBGPOST([FromForm] VideoBg video)
        {
            video.VideoPath = await SetAttachmentIfHas(video.VideoPath);
            DB.VideoBgs.Update(video);
            await DB.SaveChangesAsync();
            return RedirectToAction("VideoBGs", "VideoBg");
        }



        [Route("DeleteUserAvatar"), HttpDelete]
        public async Task<IActionResult> DeleteVideoBG(int id)
        {
            var avatar = DB.VideoBgs.FirstOrDefault(o => o.Id == id);
            avatar.IsDeleted = true;
            DB.VideoBgs.Update(avatar);
            await DB.SaveChangesAsync();
            return RedirectToAction("VideoBGs", "VideoBg");
        }
    }
}
