﻿using Triggero.Models.Localization;
using Triggero.Models.Practices.Categories;
using Triggero.Models.Tests;

namespace Triggero.MauiClient.Views
{

    public partial class CellCategoryDrawn : FastCellWithBanner
    {
        public CellCategoryDrawn()
        {
            InitializeComponent();
        }

        protected override bool ApplyContext()
        {
            var applied = base.ApplyContext();

            if (BindingContext is AbstractCategory item && this.Parent != null)
            {
                if (this.Parent.BindingContext is BaseCategoriesViewModel vm)
                {
                    this.Frame.BackgroundColor = vm.ThemeColor;
                }

                IEnumerable<LocalizationWithTitle> localizations = null;

                if (item is ExerciseCategory exercise)
                {
                    localizations = exercise.Localizations;
                }
                else
                if (item is TopicCategory topic)
                {
                    localizations = topic.Localizations;
                }
                else
                if (item is TestCategory test)
                {
                    localizations = test.Localizations;
                }
                else
                if (item is PracticeCategory practice)
                {
                    localizations = practice.Localizations;
                }

                if (localizations != null)
                {
                    titleLabel.Text = localizations.GetLocalizedTitle(LanguageHelper.LangCode, item.Title);
                    descLabel.Text = localizations.GetLocalizedDescription(LanguageHelper.LangCode, item.Description);
                }
                else
                {
                    titleLabel.Text = item.Title;
                    descLabel.Text = item.Description;
                }

                img.Source = Mobile.App.GetFullImageUrl(item.ImgPath, ThumbnailSize.Small, ThumbnailType.Png);

                if (item.Id != 0)
                {
                    titleLabel.FontAttributes = FontAttributes.None;
                }

                return true;
            }

            return applied;
        }




    }
}