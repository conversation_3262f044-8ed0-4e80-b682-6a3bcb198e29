﻿@using MarkupCreator.Helpers.Converters
@using Triggero.Models.Tickets
@{
    ViewData["Title"] = "ТИКЕТЫ";
}
@model List<Ticket>

  <!-- CONTENT -->
<div class="content">
    <div class="row">
        <div class="page">
            <table class="table" id="tablemanager">
                <thead class="tableheader">
                    <tr>
                    <th scope="col">ID</th>
                    <th scope="col">Пользователь</th>
                    <th scope="col">Последний ответ</th>
                    <th scope="col">Статус</th>
                    <th scope="col">Дата создания</th>
                    <th scope="col">Действия</th>
                    </tr>
                </thead>
                <tbody>
                    @foreach (var item in Model)
                    {
                        <tr>
                            <th scope="row">#@item.Id</th>
                            <td>@item.CreatedBy.Username</td>
                            <td>@item.Messages.LastOrDefault()?.SentBy.Username</td>
                            <td>@EnumDescriptionHelper.GetDescription(item.Status)</td>
                            <td>@item.CreatedAt.ToString("dd.MM.yyyy")</td>
                            <td>
                                <div class="interect">
                                    @*<a href="#close" data-bs-toggle="modal" data-bs-target="#close" class="delete"><i class="fa-solid fa-trash"></i></a>*@
                                    @if(item.Status != Triggero.Models.Enums.TicketStatus.Closed)
                                    {
                                         <a asp-action="TicketPage" asp-controller="Tickets" asp-route-id="@item.Id" class="reply"><i class="fa-solid fa-reply-all"></i></a>
                                    }
                                </div>
                            </td>
                        </tr>
                    }                
                </tbody>
            </table>

            <script>
                $(document).ready( function () {
                    $('#tablemanager').DataTable();
                } );
            </script>
        </div>
    </div>
</div>