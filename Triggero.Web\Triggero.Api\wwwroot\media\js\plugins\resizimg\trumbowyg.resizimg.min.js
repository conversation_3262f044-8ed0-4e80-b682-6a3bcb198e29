!function(e){"use strict";var i={minSize:32,step:4};function t(e){e.stopPropagation(),e.preventDefault()}var s=function(i){this.resizeCanvas=document.createElement("canvas"),this.resizeCanvas.setAttribute("tabindex","0"),this.resizeCanvas.id="trumbowyg-resizimg-"+ +new Date,this.ctx=null,this.resizeImg=null,this.pressEscape=function(e){e.reset()},this.pressBackspaceOrDelete=function(t){e(t.resizeCanvas).remove(),t.resizeImg=null,null!==i&&(i.syncCode(),i.$c.trigger("tbwchange"))};var s,n,r=!1,a=!1,o=function(e){var i=e.getBoundingClientRect();s=i.left,n=i.top},h=function(e,i,t,s,n){return i.translate(.5,.5),i.lineWidth=1,i.drawImage(t,5,5,s-10,n-10),i.beginPath(),i.rect(5,5,s-10,n-10),i.stroke(),i.beginPath(),i.fillStyle="rgb(255, 255, 255)",i.rect(s-10,n-10,9,9),i.fill(),i.stroke(),o(e),i};
// necessary to correctly print cursor over square. Called once for instance. Useless with trumbowyg.
this.init=function(){var i=this;e(window).on("scroll resize",(function(){i.reCalcOffset()}))},this.reCalcOffset=function(){o(this.resizeCanvas)},this.canvasId=function(){return this.resizeCanvas.id},this.isActive=function(){return null!==this.resizeImg},this.isFocusedNow=function(){return r},this.blurNow=function(){r=!1},this.reset=function(){null!==this.resizeImg&&(this.resizeImg.setAttribute("style","width: 100%; max-width: "+(this.resizeCanvas.clientWidth-10)+"px; height: auto; max-height: "+(this.resizeCanvas.clientHeight-10)+"px;"),e(this.resizeCanvas).replaceWith(e(this.resizeImg)),this.resizeCanvas.removeAttribute("style"),this.resizeImg=null)},this.setup=function(o,c){if(this.resizeImg=o,!this.resizeCanvas.getContext)return!1;r=!0,this.resizeCanvas.width=e(this.resizeImg).width()+10,this.resizeCanvas.height=e(this.resizeImg).height()+10,this.resizeCanvas.style.margin="-5px",this.ctx=this.resizeCanvas.getContext("2d"),e(this.resizeImg).replaceWith(e(this.resizeCanvas)),h(this.resizeCanvas,this.ctx,this.resizeImg,this.resizeCanvas.width,this.resizeCanvas.height),e(this.resizeCanvas).resizableSafe(c).on("mousedown",t);var u=this;return e(this.resizeCanvas).on("mousemove",(function(e){var i=Math.round(e.clientX-s),t=Math.round(e.clientY-n),r=a;u.ctx.rect(u.resizeCanvas.width-10,u.resizeCanvas.height-10,9,9),r!==(a=u.ctx.isPointInPath(i,t))&&(this.style.cursor=a?"se-resize":"default")})).on("keydown",(function(e){if(u.isActive()){var i=e.keyCode;27===i?u.pressEscape(u):8!==i&&46!==i||u.pressBackspaceOrDelete(u)}})).on("focus",t).on("blur",(function(){u.reset(),null!==i&&(i.syncCode(),i.$c.trigger("tbwchange"))})),this.resizeCanvas.focus(),!0},this.refresh=function(){this.resizeCanvas.getContext&&(this.resizeCanvas.width=this.resizeCanvas.clientWidth,this.resizeCanvas.height=this.resizeCanvas.clientHeight,h(this.resizeCanvas,this.ctx,this.resizeImg,this.resizeCanvas.width,this.resizeCanvas.height))}};e.extend(!0,e.trumbowyg,{plugins:{resizimg:{destroyResizable:function(){},init:function(n){var r=this.destroyResizable,a=new s(n);function o(){n.$ed.find("img").off("click").on("click",(function(e){a.isActive()&&a.reset(),a.setup(this,n.o.plugins.resizimg.resizable),t(e)}))}this.destroyResizable=function(){n.$ed.find("canvas.resizable").resizableSafe("destroy").off("mousedown",t).removeClass("resizable"),a.reset(),n.syncCode()},n.o.plugins.resizimg=e.extend(!0,{},i,n.o.plugins.resizimg||{},{resizable:{resizeWidth:!1,onDragStart:function(e,i){var t=n.o.plugins.resizimg,s=e.pageX-i.offset().left,r=e.pageY-i.offset().top;if(s<i.width()-t.minSize||r<i.height()-t.minSize)return!1},onDrag:function(e,i,t,s){var r=n.o.plugins.resizimg;return s<r.minSize&&(s=r.minSize),s-=s%r.step,i.height(s),!1},onDragEnd:function(){a.refresh(),n.syncCode()}}}),n.$c.on("tbwinit",(function(){o(),n.$ed.on("click",(function(i){e(i.target).is("img")||i.target.id===a.canvasId()||(t(i),a.reset(),n.syncCode(),n.$c.trigger("tbwchange"))})),n.$ed.on("scroll",(function(){a.reCalcOffset()}))})),n.$c.on("tbwfocus tbwchange",o),n.$c.on("tbwresize",(function(){a.reCalcOffset()})),n.$c.on("tbwblur",(function(){a.isFocusedNow()?a.blurNow():r()}))},destroy:function(){this.destroyResizable()}}}})}(jQuery);