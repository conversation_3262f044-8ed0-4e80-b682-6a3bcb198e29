﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <OutputType>Exe</OutputType>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
  </PropertyGroup>

  <ItemGroup>
    <None Remove="appsettings.json" />
  </ItemGroup>

  <ItemGroup>
    <Compile Include="..\..\..\Triggero.Web.Secrets.cs" Link="Triggero.Web.Secrets.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="..\..\..\..\Triggero\Secrets\secrets.json" Link="secrets.json">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
    <Content Include="appsettings.json">
      <CopyToOutputDirectory>Always</CopyToOutputDirectory>
    </Content>
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="Microsoft.EntityFrameworkCore.Relational" Version="8.0.5" />
  </ItemGroup>
 
  <ItemGroup>
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Testing" Version="8.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Triggero.Application\Triggero.Application.csproj" />
    <ProjectReference Include="..\Triggero.Database\Triggero.Database.csproj" />
    <ProjectReference Include="..\Triggero.PushNotifications\Triggero.PushNotifications.csproj" />
  </ItemGroup>

</Project>
